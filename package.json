{"name": "@samanhappy/mcphub", "version": "1.1.0", "description": "A hub server for mcp servers", "main": "dist/index.js", "type": "module", "bin": {"mcphub": "bin/cli.js"}, "files": ["dist", "bin", "mcp_settings.json", "servers.json", "frontend/dist", "README.md", "LICENSE"], "scripts": {"build": "pnpm backend:build && pnpm frontend:build", "backend:build": "tsc", "start": "node dist/index.js", "start:prod": "NODE_ENV=production PORT=8080 node dist/index.js", "start:pm2": "pm2 start ecosystem.config.js", "stop:pm2": "pm2 stop mcphub", "restart:pm2": "pm2 restart mcphub", "logs:pm2": "pm2 logs mcphub", "backend:dev": "tsx watch src/index.ts", "backend:debug": "tsx watch src/index.ts --inspect", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose", "test:ci": "jest --ci --coverage --watchAll=false", "frontend:dev": "cd frontend && vite", "frontend:build": "cd frontend && vite build", "frontend:preview": "cd frontend && vite preview", "dev": "concurrently \"pnpm backend:dev\" \"pnpm frontend:dev\"", "debug": "concurrently \"pnpm backend:debug\" \"pnpm frontend:dev\"", "deploy": "pnpm build && pnpm start:prod", "prepublishOnly": "npm run build && node scripts/verify-dist.js"}, "keywords": ["typescript", "server", "mcp", "model context protocol"], "author": "", "license": "ISC", "dependencies": {"@apidevtools/swagger-parser": "^11.0.1", "@modelcontextprotocol/sdk": "^1.12.1", "@types/adm-zip": "^0.5.7", "@types/multer": "^1.4.13", "@types/pg": "^8.15.2", "@types/ws": "^8.18.1", "adm-zip": "^0.5.16", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "dotenv": "^16.3.1", "dotenv-expand": "^12.0.2", "express": "^4.21.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "openai": "^4.103.0", "openapi-types": "^12.1.3", "pg": "^8.16.0", "pgvector": "^0.2.1", "playwright": "^1.53.2", "postgres": "^3.4.7", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.24", "uuid": "^11.1.0", "ws": "^8.18.3"}, "devDependencies": {"@playwright/test": "^1.53.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4.1.3", "@tailwindcss/vite": "^4.1.7", "@types/bcryptjs": "^3.0.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.5", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.21", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.1.2", "eslint": "^8.50.0", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "jest": "^29.7.0", "jest-environment-node": "^30.0.0", "jest-mock-extended": "4.0.0-beta1", "lucide-react": "^0.486.0", "next": "^15.2.4", "postcss": "^8.5.3", "prettier": "^3.0.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.4.1", "react-router-dom": "^7.6.0", "supertest": "^7.1.1", "tailwind-merge": "^3.1.0", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss": "^4.0.17", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "tsx": "^4.7.0", "typescript": "^5.2.2", "vite": "^6.3.5", "zod": "^3.24.2"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "packageManager": "pnpm@10.11.0+sha256.a69e9cb077da419d47d18f1dd52e207245b29cac6e076acedbeb8be3b1a67bd7"}