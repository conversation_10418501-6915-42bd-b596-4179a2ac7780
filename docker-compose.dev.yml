version: '3.8'

# 开发环境配置 - 覆盖生产配置
services:
  # 开发环境只启动PostgreSQL，应用在本地运行
  postgres:
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: mcphub_dev
      POSTGRES_USER: mcphub_dev_user
      POSTGRES_PASSWORD: mcphub_dev_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./docker/init-db:/docker-entrypoint-initdb.d

  # 开发环境的Redis (可选)
  redis:
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
