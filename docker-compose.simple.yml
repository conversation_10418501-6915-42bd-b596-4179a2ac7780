version: '3.8'

services:
  # PostgreSQL数据库服务
  postgres:
    image: pgvector/pgvector:pg16
    container_name: mcphub-postgres-simple
    restart: unless-stopped
    environment:
      - POSTGRES_DB=mcphub
      - POSTGRES_USER=mcphub
      - POSTGRES_PASSWORD=mcphub123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-db:/docker-entrypoint-initdb.d:ro
    ports:
      - "5432:5432"
    networks:
      - mcphub-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mcphub -d mcphub"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # MCPHub应用服务 (使用简单的Node容器)
  mcphub:
    image: node:18-alpine
    container_name: mcphub-simple
    restart: unless-stopped
    working_dir: /app
    command: sh -c "npm install -g pnpm && node dist/index.js"
    ports:
      - "8080:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=*******************************************/mcphub
    volumes:
      - .:/app:ro
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - mcphub-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres_data:
    driver: local

networks:
  mcphub-network:
    driver: bridge
