import { useState, useEffect, useCallback } from 'react';
import { OverviewData } from '../types/overview';
import { fetchOverviewData, fetchOverviewSummary } from '../services/overviewService';

interface UseOverviewDataResult {
  data: OverviewData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}



/**
 * Hook for fetching overview data
 */
export const useOverviewData = (): UseOverviewDataResult => {
  const [data, setData] = useState<OverviewData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async (forceRefresh = false): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      const result = await fetchOverviewData(forceRefresh);
      setData(result);
    } catch (err) {
      console.error('Error fetching overview data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: () => fetchData(true), // 强制刷新
  };
};

/**
 * Hook for fetching overview summary data (lightweight version)
 */
export const useOverviewSummary = () => {
  const [summary, setSummary] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSummaryData = useCallback(async (forceRefresh = false): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      const result = await fetchOverviewSummary(forceRefresh);
      setSummary(result);
    } catch (err) {
      console.error('Error fetching overview summary:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchSummaryData();
  }, [fetchSummaryData]);

  return {
    summary,
    loading,
    error,
    refetch: () => fetchSummaryData(true), // 强制刷新
  };
};
