import { useState, useCallback, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

interface ErrorState {
  message: string;
  type: 'network' | 'server' | 'business' | 'timeout' | 'unknown';
  retryCount: number;
  canRetry: boolean;
}

interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

interface UseErrorHandlerOptions {
  retryConfig?: Partial<RetryConfig>;
  onError?: (error: ErrorState) => void;
  onRetry?: (retryCount: number) => void;
  onSuccess?: () => void;
}

const defaultRetryConfig: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffMultiplier: 2,
};

export const useErrorHandler = (options: UseErrorHandlerOptions = {}) => {
  const { t } = useTranslation();
  const [error, setError] = useState<ErrorState | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const config = { ...defaultRetryConfig, ...options.retryConfig };

  // Use refs to store callbacks to avoid dependency issues
  const optionsRef = useRef(options);

  // Update ref when options change
  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  // Classify error type based on error object
  const classifyError = useCallback((error: any): ErrorState['type'] => {
    if (!navigator.onLine) {
      return 'network';
    }

    if (error instanceof TypeError) {
      if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
        return 'network';
      }
    }

    if (error.name === 'AbortError') {
      return 'timeout';
    }

    if (error.response) {
      const status = error.response.status;
      if (status >= 500) {
        return 'server';
      } else if (status >= 400) {
        return 'business';
      }
    }

    return 'unknown';
  }, []);

  // Get user-friendly error message
  const getErrorMessage = useCallback((error: any, type: ErrorState['type']): string => {
    switch (type) {
      case 'network':
        return t('errors.network', 'Network connection error. Please check your internet connection');
      case 'server':
        return t('errors.serverConnection', 'Unable to connect to the server. Please check if the server is running');
      case 'timeout':
        return t('errors.timeout', 'Request timeout. Please try again');
      case 'business':
        // Try to extract business error message
        if (error.response?.data?.message) {
          return error.response.data.message;
        }
        if (error.message) {
          return error.message;
        }
        return t('errors.business', 'Operation failed. Please check your input and try again');
      default:
        return error.message || t('errors.unknown', 'An unknown error occurred');
    }
  }, [t]);

  // Check if error is retryable
  const isRetryable = useCallback((type: ErrorState['type']): boolean => {
    return type === 'network' || type === 'server' || type === 'timeout';
  }, []);

  // Calculate delay for next retry
  const calculateDelay = useCallback((attempt: number): number => {
    const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
    return Math.min(delay, config.maxDelay);
  }, [config]);

  // Execute operation with retry logic
  const executeWithRetry = useCallback(async <T>(
    operation: () => Promise<T>,
    operationName?: string
  ): Promise<T> => {
    setIsLoading(true);
    setError(null);
    
    let lastError: any;
    let currentRetryCount = 0;

    for (let attempt = 1; attempt <= config.maxRetries + 1; attempt++) {
      try {
        const result = await operation();
        
        // Success
        setIsLoading(false);
        setRetryCount(0);
        setError(null);
        optionsRef.current.onSuccess?.();

        return result;
      } catch (err) {
        lastError = err;
        currentRetryCount = attempt - 1;
        
        const errorType = classifyError(err);
        const canRetry = isRetryable(errorType) && attempt <= config.maxRetries;
        
        console.error(`${operationName || 'Operation'} attempt ${attempt} failed:`, err);
        
        if (!canRetry) {
          // Final failure
          const errorState: ErrorState = {
            message: getErrorMessage(err, errorType),
            type: errorType,
            retryCount: currentRetryCount,
            canRetry: false,
          };
          
          setError(errorState);
          setRetryCount(currentRetryCount);
          setIsLoading(false);
          optionsRef.current.onError?.(errorState);

          throw err;
        }
        
        // Prepare for retry
        setRetryCount(currentRetryCount);
        optionsRef.current.onRetry?.(currentRetryCount);

        // Wait before retry
        const delay = calculateDelay(attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    // This should never be reached, but just in case
    throw lastError;
  }, [config, classifyError, isRetryable, getErrorMessage, calculateDelay]);

  // Manual retry function
  const retry = useCallback(async <T>(
    operation: () => Promise<T>,
    operationName?: string
  ): Promise<T> => {
    return executeWithRetry(operation, operationName);
  }, [executeWithRetry]);

  // Clear error state
  const clearError = useCallback(() => {
    setError(null);
    setRetryCount(0);
  }, []);

  // Handle error manually (without retry)
  const handleError = useCallback((err: any) => {
    const errorType = classifyError(err);
    const errorState: ErrorState = {
      message: getErrorMessage(err, errorType),
      type: errorType,
      retryCount: 0,
      canRetry: isRetryable(errorType),
    };
    
    setError(errorState);
    setIsLoading(false);
    optionsRef.current.onError?.(errorState);
  }, [classifyError, getErrorMessage, isRetryable]);

  return {
    error,
    isLoading,
    retryCount,
    executeWithRetry,
    retry,
    clearError,
    handleError,
    // Utility functions
    classifyError,
    getErrorMessage,
    isRetryable,
  };
};

export default useErrorHandler;
