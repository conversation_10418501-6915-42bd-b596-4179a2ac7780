// Monitoring service for system health and performance data
import { getApiUrl } from '../utils/runtime';
import { getToken } from './authService';

// Types for monitoring data
export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  services: {
    database: {
      status: 'up' | 'down';
      responseTime: number;
    };
    mcpServers: {
      status: 'up' | 'down';
      count: number;
      active: number;
    };
    cache: {
      status: 'up' | 'down';
      size: number;
      hitRate: number;
    };
  };
  lastCheck: string;
}

export interface PerformanceMetrics {
  cpu: {
    usage: number;
    cores: number;
  };
  memory: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  database: {
    connections: number;
    responseTime: number;
    queriesPerMinute: number;
  };
  api: {
    requestsPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
    activeConnections: number;
  };
}

export interface SystemStatus {
  system: {
    uptime: number;
    version: string;
    nodeVersion: string;
    platform: string;
    arch: string;
  };
  servers: {
    total: number;
    running: number;
    stopped: number;
    error: number;
  };
  performance: {
    totalCalls: number;
    successRate: number;
    avgResponseTime: string;
    errorRate: number;
  };
  database: {
    connected: boolean;
  };
}

export interface DatabaseStatus {
  connected: boolean;
  connectionPool: {
    active: number;
    idle: number;
    total: number;
  };
  performance: {
    avgQueryTime: number;
    slowQueries: number;
    totalQueries: number;
  };
  storage: {
    size: number;
    tables: number;
    indexes: number;
  };
}

export interface ServerStatus {
  name: string;
  status: 'running' | 'stopped' | 'error';
  type: string;
  uptime: number;
  lastActivity: string | null;
  performance: {
    totalCalls: number;
    successCalls: number;
    failedCalls: number;
    successRate: number;
  };
  tools: number;
  resources: number;
  prompts: number;
}

export interface ServersStatusResponse {
  summary: {
    total: number;
    running: number;
    stopped: number;
    error: number;
    totalCalls: number;
    totalTools: number;
  };
  servers: ServerStatus[];
}

export interface RealtimeData {
  timestamp: string;
  system: {
    cpu: number;
    memory: number;
    uptime: number;
  };
  servers: {
    active: number;
    total: number;
  };
  performance: {
    totalCalls: number;
    requestsPerMinute: number;
    avgResponseTime: number;
  };
  database: {
    connected: boolean;
    responseTime: number;
  };
}

export interface MonitoringResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}

// Fetch system health status
export const fetchSystemHealth = async (): Promise<MonitoringResponse<SystemHealth>> => {
  try {
    const token = getToken();
    const response = await fetch(getApiUrl('/monitoring/health'), {
      headers: {
        'x-auth-token': token || '',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching system health:', error);
    return {
      success: false,
      data: {} as SystemHealth,
      error: (error as Error).message,
    };
  }
};

// Fetch performance metrics
export const fetchPerformanceMetrics = async (): Promise<MonitoringResponse<PerformanceMetrics>> => {
  try {
    const token = getToken();
    const response = await fetch(getApiUrl('/monitoring/metrics'), {
      headers: {
        'x-auth-token': token || '',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching performance metrics:', error);
    return {
      success: false,
      data: {} as PerformanceMetrics,
      error: (error as Error).message,
    };
  }
};

// Fetch system status
export const fetchSystemStatus = async (): Promise<MonitoringResponse<SystemStatus>> => {
  try {
    const token = getToken();
    const response = await fetch(getApiUrl('/monitoring/status'), {
      headers: {
        'x-auth-token': token || '',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching system status:', error);
    return {
      success: false,
      data: {} as SystemStatus,
      error: (error as Error).message,
    };
  }
};

// Fetch database status
export const fetchDatabaseStatus = async (): Promise<MonitoringResponse<DatabaseStatus>> => {
  try {
    const token = getToken();
    const response = await fetch(getApiUrl('/monitoring/database'), {
      headers: {
        'x-auth-token': token || '',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching database status:', error);
    return {
      success: false,
      data: {} as DatabaseStatus,
      error: (error as Error).message,
    };
  }
};

// Fetch servers status
export const fetchServersStatus = async (): Promise<MonitoringResponse<ServersStatusResponse>> => {
  try {
    const token = getToken();
    const response = await fetch(getApiUrl('/monitoring/servers'), {
      headers: {
        'x-auth-token': token || '',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching servers status:', error);
    return {
      success: false,
      data: {} as ServersStatusResponse,
      error: (error as Error).message,
    };
  }
};

// Fetch realtime data
export const fetchRealtimeData = async (): Promise<MonitoringResponse<RealtimeData>> => {
  try {
    const token = getToken();
    const response = await fetch(getApiUrl('/monitoring/realtime'), {
      headers: {
        'x-auth-token': token || '',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching realtime data:', error);
    return {
      success: false,
      data: {} as RealtimeData,
      error: (error as Error).message,
    };
  }
};

// Utility function to format bytes
export const formatBytes = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

// Utility function to format uptime
export const formatUptime = (seconds: number): string => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
};
