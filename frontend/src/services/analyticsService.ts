// Analytics service for historical data and trends
import { getApiUrl } from '../utils/runtime';
import { getToken } from './authService';

// API调用缓存机制
interface CacheEntry {
  data: any;
  timestamp: number;
  promise?: Promise<any>;
}

const apiCache = new Map<string, CacheEntry>();
const CACHE_DURATION = 5000; // 5秒缓存

// 生成缓存键
const getCacheKey = (url: string, params: any): string => {
  return `${url}_${JSON.stringify(params)}`;
};

// Types for analytics data
export interface HistoryQueryParams {
  startTime: string;
  endTime: string;
  page?: number;
  pageSize?: number;
  serverNames?: string[];
  toolNames?: string[];
  clientIps?: string[];
  success?: boolean;
  groupBy?: 'hour' | 'day' | 'week' | 'month';
  metrics?: string[];
}

export interface TimeSeriesDataPoint {
  timestamp: string;
  count: number;
  success_rate: number;
  avg_response_time: number;
}

export interface HistoryCallRecord {
  id: string;
  server_name: string;
  tool_name: string;
  success: boolean;
  response_time: number;
  timestamp: string;
  client_ip?: string;
  error_message?: string;
}

export interface HistoryQueryResponse {
  success: boolean;
  data: {
    records?: HistoryCallRecord[];
    timeSeries?: TimeSeriesDataPoint[];
    pagination?: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
    };
  };
  error?: string;
}

export interface TrendsQueryParams {
  timeRange?: '1h' | '24h' | '7d' | '30d' | 'custom';
  granularity?: 'hour' | 'day' | 'week';
  metrics?: string[];
  serverNames?: string[];
  startTime?: string;
  endTime?: string;
}

export interface TrendPoint {
  timestamp: string;
  calls: number;
  success_rate: number;
  response_time: number;
}

export interface TrendAnalysis {
  trend: 'increasing' | 'decreasing' | 'stable';
  changeRate: number;
  slope: number;
  strength?: number; // R²值，表示趋势强度 (0-1)
}

export interface AnomalyPoint {
  timestamp: string;
  value: number;
  severity: 'low' | 'medium' | 'high';
  description: string;
  type: 'spike' | 'drop' | 'trend_break' | 'outlier' | 'volatility';
  category?: 'performance' | 'error' | 'usage' | 'system';
  duration?: number;
  relatedMetrics?: string[];
  confidence?: number;
  expectedValue?: number;
  deviation?: number;
}

// 洞察类型和严重程度
export type InsightType = 'trend' | 'anomaly' | 'seasonality' | 'correlation' | 'forecast' | 'recommendation';
export type InsightSeverity = 'info' | 'success' | 'warning' | 'critical';

// 洞察接口
export interface Insight {
  id: string;                 // 唯一标识
  type: InsightType;          // 洞察类型
  title: string;              // 标题
  description: string;        // 详细描述
  severity: InsightSeverity;  // 严重程度
  metrics: string[];          // 相关指标
  timestamp: string;          // 生成时间
  timeRange?: {               // 相关时间范围
    start: string;
    end: string;
  };
  relatedAnomalies?: string[]; // 相关异常ID
  confidence?: number;         // 置信度 (0-1)
  actionable?: boolean;        // 是否可操作
  recommendation?: string;     // 建议操作
}

export interface TrendsQueryResponse {
  success: boolean;
  data: {
    timeRange: {
      start: string;
      end: string;
      granularity: string;
    };
    series: {
      calls: {
        timestamps: string[];
        values: number[];
      };
      success_rate: {
        timestamps: string[];
        values: number[];
      };
      response_time: {
        timestamps: string[];
        values: number[];
      };
    };
    analysis: {
      calls: TrendAnalysis;
      success_rate: TrendAnalysis;
      response_time: TrendAnalysis;
    };
    anomalies: {
      calls: AnomalyPoint[];
      success_rate: AnomalyPoint[];
      response_time: AnomalyPoint[];
    };
    insights: Insight[];
  };
  error?: string;
}

// Fetch historical data
export const fetchHistoryData = async (params: HistoryQueryParams): Promise<HistoryQueryResponse> => {
  try {
    const token = getToken();
    const queryParams = new URLSearchParams();
    
    // Add required parameters
    queryParams.append('startTime', params.startTime);
    queryParams.append('endTime', params.endTime);
    
    // Add optional parameters
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());
    if (params.serverNames?.length) queryParams.append('serverNames', params.serverNames.join(','));
    if (params.toolNames?.length) queryParams.append('toolNames', params.toolNames.join(','));
    if (params.clientIps?.length) queryParams.append('clientIps', params.clientIps.join(','));
    if (params.success !== undefined) queryParams.append('success', params.success.toString());
    if (params.groupBy) queryParams.append('groupBy', params.groupBy);
    if (params.metrics?.length) queryParams.append('metrics', params.metrics.join(','));

    const response = await fetch(getApiUrl(`/analytics/history?${queryParams.toString()}`), {
      headers: {
        'x-auth-token': token || '',
      },
    });

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error fetching history data:', error);
    throw error;
  }
};
/**
 * 导出历史数据
 */
export const exportHistoryData = async (
  params: HistoryQueryParams & { format?: 'csv' | 'excel' }
): Promise<{ success: boolean; error?: string }> => {
  try {
    const token = getToken();
    const queryParams = new URLSearchParams();
    
    // Add required parameters
    queryParams.append('startTime', params.startTime);
    queryParams.append('endTime', params.endTime);
    
    // Add optional parameters
    if (params.serverNames?.length) queryParams.append('serverNames', params.serverNames.join(','));
    if (params.toolNames?.length) queryParams.append('toolNames', params.toolNames.join(','));
    if (params.clientIps?.length) queryParams.append('clientIps', params.clientIps.join(','));
    if (params.success !== undefined) queryParams.append('success', params.success.toString());
    if (params.format) queryParams.append('format', params.format);

    // 发起请求，设置较长的超时时间
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 300000); // 5分钟超时

    const response = await fetch(getApiUrl(`/analytics/history/export?${queryParams.toString()}`), {
      headers: {
        'x-auth-token': token || '',
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      let errorMessage = 'Export failed';
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch {
        // 如果响应不是JSON，使用状态文本
        errorMessage = response.statusText || errorMessage;
      }
      throw new Error(errorMessage);
    }

    // 检查响应类型
    const contentType = response.headers.get('Content-Type');
    if (!contentType || (!contentType.includes('text/csv') && !contentType.includes('application/vnd.ms-excel'))) {
      throw new Error('Invalid response format from server');
    }

    // 获取文件名
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = `mcp_history_export.${params.format || 'csv'}`;
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[*]?=['"]?([^'";]+)['"]?/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }

    // 创建下载链接
    const blob = await response.blob();
    
    // 检查blob大小
    if (blob.size === 0) {
      throw new Error('Exported file is empty');
    }

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    // 添加到DOM，触发下载，然后清理
    document.body.appendChild(link);
    link.click();
    
    // 延迟清理，确保下载开始
    setTimeout(() => {
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    }, 100);

    return { success: true };
  } catch (error) {
    console.error('Error exporting history data:', error);
    
    // 处理不同类型的错误
    let errorMessage = 'Unknown error';
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        errorMessage = 'Export timeout - please try with a smaller date range';
      } else {
        errorMessage = error.message;
      }
    }
    
    return { 
      success: false, 
      error: errorMessage
    };
  }
};;

// Fetch trends data with caching
export const fetchTrendsData = async (params: TrendsQueryParams): Promise<TrendsQueryResponse> => {
  try {
    const token = getToken();
    const queryParams = new URLSearchParams();

    // Add optional parameters
    if (params.timeRange) queryParams.append('timeRange', params.timeRange);
    if (params.granularity) queryParams.append('granularity', params.granularity);
    if (params.metrics?.length) queryParams.append('metrics', params.metrics.join(','));
    if (params.serverNames?.length) queryParams.append('serverNames', params.serverNames.join(','));
    if (params.startTime) queryParams.append('startTime', params.startTime);
    if (params.endTime) queryParams.append('endTime', params.endTime);

    const url = getApiUrl(`/analytics/trends?${queryParams.toString()}`);
    const cacheKey = getCacheKey(url, params);
    const now = Date.now();

    // 检查缓存
    const cached = apiCache.get(cacheKey);
    if (cached) {
      // 如果缓存未过期，返回缓存数据
      if (now - cached.timestamp < CACHE_DURATION) {
        console.log('Returning cached trends data');
        return cached.data;
      }

      // 如果有正在进行的请求，等待它完成
      if (cached.promise) {
        console.log('Waiting for ongoing trends request');
        return await cached.promise;
      }
    }

    // 创建新的请求
    const requestPromise = fetch(url, {
      headers: {
        'x-auth-token': token || '',
      },
    }).then(response => response.json());

    // 将请求存储到缓存中
    apiCache.set(cacheKey, {
      data: null,
      timestamp: now,
      promise: requestPromise
    });

    const result = await requestPromise;

    // 更新缓存数据
    apiCache.set(cacheKey, {
      data: result,
      timestamp: now
    });

    console.log('Fetched fresh trends data');
    return result;
  } catch (error) {
    console.error('Error fetching trends data:', error);
    throw error;
  }
};

// Get quick dashboard stats for the last 24 hours
export const fetchDashboardStats = async () => {
  const endTime = new Date().toISOString();
  const startTime = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
  
  try {
    // Fetch both history summary and trends for dashboard
    const [historyResponse, trendsResponse] = await Promise.all([
      fetchHistoryData({
        startTime,
        endTime,
        groupBy: 'hour',
        metrics: ['count', 'success_rate', 'avg_response_time']
      }),
      fetchTrendsData({
        timeRange: '24h',
        granularity: 'hour',
        metrics: ['calls', 'success_rate', 'response_time']
      })
    ]);

    return {
      history: historyResponse,
      trends: trendsResponse
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    throw error;
  }
};

// Filter options interface
export interface FilterOptions {
  serverNames: string[];
  toolNames: string[];
  clientIps: string[];
}

export interface FilterOptionsResponse {
  success: boolean;
  filters?: FilterOptions;
  error?: string;
}

// Fetch available filter options
export const fetchFilterOptions = async (serverNames?: string[]): Promise<FilterOptionsResponse> => {
  try {
    const token = getToken();
    
    // 构建查询参数
    const params = new URLSearchParams();
    if (serverNames && serverNames.length > 0) {
      params.append('serverNames', serverNames.join(','));
    }
    
    const url = getApiUrl('/analytics/filters') + (params.toString() ? `?${params.toString()}` : '');
    
    const response = await fetch(url, {
      headers: {
        'x-auth-token': token || '',
      },
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching filter options:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// Cleanup test data
export const cleanupTestData = async (): Promise<{ success: boolean; message?: string; error?: string }> => {
  try {
    const token = getToken();

    const response = await fetch(getApiUrl('/analytics/test-data'), {
      method: 'DELETE',
      headers: {
        'x-auth-token': token || '',
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error cleaning up test data:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
