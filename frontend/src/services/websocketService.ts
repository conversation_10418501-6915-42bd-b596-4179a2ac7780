// WebSocket client service for real-time data communication
import { getToken } from './authService';
import { getApiUrl, getBasePath } from '../utils/runtime';

// WebSocket message types
export interface WSMessage {
  type: string;
  data?: any;
  timestamp?: string;
  id?: string;
}

export interface WSDataMessage extends WSMessage {
  type: 'data';
  data: {
    channel: string;
    payload: any;
  };
}

export interface WSErrorMessage extends WSMessage {
  type: 'error';
  data: {
    message: string;
    code?: string;
  };
}

export interface WSConnectionStatusMessage extends WSMessage {
  type: 'connection_status';
  data: {
    status: 'connected' | 'disconnected' | 'reconnecting';
    clientCount?: number;
  };
}

// Available subscription channels
export enum WSChannel {
  SYSTEM_HEALTH = 'system_health',
  PERFORMANCE_METRICS = 'performance_metrics',
  REALTIME_DATA = 'realtime_data',
  SERVER_STATUS = 'server_status',
  DATABASE_STATUS = 'database_status',
  MONITORING_ALERTS = 'monitoring_alerts',
}

// Connection states
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

// Event listeners
export type WSEventListener = (data: any) => void;
export type WSConnectionListener = (state: ConnectionState) => void;
export type WSErrorListener = (error: string) => void;

export class WebSocketClientService {
  private ws: WebSocket | null = null;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private subscriptions: Set<string> = new Set();
  private eventListeners: Map<string, Set<WSEventListener>> = new Map();
  private connectionListeners: Set<WSConnectionListener> = new Set();
  private errorListeners: Set<WSErrorListener> = new Set();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private maxReconnectDelay = 30000; // Max 30 seconds
  private pingInterval: NodeJS.Timeout | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;

  constructor() {
    // Don't auto-connect, wait for explicit initialization
  }

  // Initialize WebSocket connection (should be called after user authentication)
  public initialize(): void {
    const token = getToken();
    if (!token) {
      console.warn('WebSocket initialization skipped: no authentication token');
      return;
    }
    this.connect();
  }

  // Connect to WebSocket server
  public connect(): void {
    if (this.connectionState === ConnectionState.CONNECTING || 
        this.connectionState === ConnectionState.CONNECTED) {
      return;
    }

    this.setConnectionState(ConnectionState.CONNECTING);

    try {
      // Get WebSocket URL
      const wsUrl = this.getWebSocketUrl();
      console.log('Connecting to WebSocket:', wsUrl);

      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      this.setConnectionState(ConnectionState.ERROR);
      this.notifyError(`Connection failed: ${error}`);
      this.scheduleReconnect();
    }
  }

  // Disconnect from WebSocket server
  public disconnect(): void {
    this.reconnectAttempts = this.maxReconnectAttempts; // Prevent reconnection
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.setConnectionState(ConnectionState.DISCONNECTED);
  }

  // Subscribe to channels
  public subscribe(channels: WSChannel[]): void {
    for (const channel of channels) {
      this.subscriptions.add(channel);
    }

    if (this.connectionState === ConnectionState.CONNECTED) {
      this.sendMessage({
        type: 'subscribe',
        data: { channels: Array.from(this.subscriptions) },
      });
    }
  }

  // Unsubscribe from channels
  public unsubscribe(channels: WSChannel[]): void {
    for (const channel of channels) {
      this.subscriptions.delete(channel);
    }

    if (this.connectionState === ConnectionState.CONNECTED) {
      this.sendMessage({
        type: 'unsubscribe',
        data: { channels },
      });
    }
  }

  // Add event listener for specific channel
  public addEventListener(channel: string, listener: WSEventListener): void {
    if (!this.eventListeners.has(channel)) {
      this.eventListeners.set(channel, new Set());
    }
    this.eventListeners.get(channel)!.add(listener);
  }

  // Remove event listener
  public removeEventListener(channel: string, listener: WSEventListener): void {
    const listeners = this.eventListeners.get(channel);
    if (listeners) {
      listeners.delete(listener);
      if (listeners.size === 0) {
        this.eventListeners.delete(channel);
      }
    }
  }

  // Add connection state listener
  public addConnectionListener(listener: WSConnectionListener): void {
    this.connectionListeners.add(listener);
  }

  // Remove connection state listener
  public removeConnectionListener(listener: WSConnectionListener): void {
    this.connectionListeners.delete(listener);
  }

  // Add error listener
  public addErrorListener(listener: WSErrorListener): void {
    this.errorListeners.add(listener);
  }

  // Remove error listener
  public removeErrorListener(listener: WSErrorListener): void {
    this.errorListeners.delete(listener);
  }

  // Get current connection state
  public getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  // Check if connected
  public isConnected(): boolean {
    return this.connectionState === ConnectionState.CONNECTED;
  }

  // Get WebSocket URL
  private getWebSocketUrl(): string {
    // In development, use Vite proxy
    if (import.meta.env.DEV) {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const basePath = getBasePath();
      return `${protocol}//${host}${basePath}/ws`;
    }

    // In production, use the same host as the current page
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const basePath = getBasePath();
    return `${protocol}//${host}${basePath}/ws`;
  }

  // Handle WebSocket open event
  private handleOpen(): void {
    console.log('WebSocket connected');
    this.reconnectAttempts = 0;
    this.reconnectDelay = 1000;

    // Authenticate
    const token = getToken();
    if (token) {
      this.sendMessage({
        type: 'auth',
        data: { token },
      });
    } else {
      console.error('WebSocket connected but no authentication token available');
      this.disconnect();
      return;
    }

    // Start ping interval
    this.startPingInterval();
  }

  // Handle WebSocket message
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WSMessage = JSON.parse(event.data);

      switch (message.type) {
        case 'auth_response':
          this.handleAuthResponse(message.data);
          break;
        case 'data':
          this.handleDataMessage(message as WSDataMessage);
          break;
        case 'error':
          this.handleErrorMessage(message as WSErrorMessage);
          break;
        case 'connection_status':
          this.handleConnectionStatus(message as WSConnectionStatusMessage);
          break;
        case 'pong':
          // Handle pong response
          break;
        default:
          console.warn('Unknown WebSocket message type:', message.type);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  // Handle WebSocket close event
  private handleClose(event: CloseEvent): void {
    console.log('WebSocket disconnected:', event.code, event.reason);
    this.setConnectionState(ConnectionState.DISCONNECTED);
    
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }

    // Schedule reconnection if not intentionally closed
    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnect();
    }
  }

  // Handle WebSocket error event
  private handleError(event: Event): void {
    console.error('WebSocket error:', event);
    this.setConnectionState(ConnectionState.ERROR);
    this.notifyError('WebSocket connection error');
  }

  // Handle authentication response
  private handleAuthResponse(data: { success: boolean; message?: string }): void {
    if (data.success) {
      console.log('WebSocket authenticated successfully');
      this.setConnectionState(ConnectionState.CONNECTED);
      
      // Subscribe to previously requested channels
      if (this.subscriptions.size > 0) {
        this.sendMessage({
          type: 'subscribe',
          data: { channels: Array.from(this.subscriptions) },
        });
      }
    } else {
      console.error('WebSocket authentication failed:', data.message);
      this.notifyError(`Authentication failed: ${data.message}`);
      this.disconnect();
    }
  }

  // Handle data message
  private handleDataMessage(message: WSDataMessage): void {
    const { channel, payload } = message.data;
    const listeners = this.eventListeners.get(channel);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(payload);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
    }
  }

  // Handle error message
  private handleErrorMessage(message: WSErrorMessage): void {
    console.error('WebSocket server error:', message.data.message);
    this.notifyError(message.data.message);
  }

  // Handle connection status message
  private handleConnectionStatus(message: WSConnectionStatusMessage): void {
    console.log('Connection status update:', message.data);
  }

  // Send message to server
  private sendMessage(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        ...message,
        timestamp: new Date().toISOString(),
      }));
    }
  }

  // Set connection state and notify listeners
  private setConnectionState(state: ConnectionState): void {
    if (this.connectionState !== state) {
      this.connectionState = state;
      this.connectionListeners.forEach(listener => {
        try {
          listener(state);
        } catch (error) {
          console.error('Error in connection listener:', error);
        }
      });
    }
  }

  // Notify error listeners
  private notifyError(error: string): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(error);
      } catch (err) {
        console.error('Error in error listener:', err);
      }
    });
  }

  // Schedule reconnection
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnection attempts reached');
      return;
    }

    this.setConnectionState(ConnectionState.RECONNECTING);
    this.reconnectAttempts++;

    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);
    
    console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    this.reconnectTimeout = setTimeout(() => {
      this.connect();
    }, delay);
  }

  // Start ping interval
  private startPingInterval(): void {
    this.pingInterval = setInterval(() => {
      if (this.connectionState === ConnectionState.CONNECTED) {
        this.sendMessage({ type: 'ping' });
      }
    }, 30000); // Ping every 30 seconds
  }
}

// Export singleton instance
export const websocketClient = new WebSocketClientService();
