// Overview数据服务
import { getApiUrl } from '../utils/runtime';
import { getToken } from './authService';
import { OverviewData } from '../types/overview';

// API响应接口
interface ApiResponse<T> {
  success: boolean;
  data: T;
  timestamp: string;
  message?: string;
  error?: string;
}

// 缓存机制
interface CacheEntry {
  data: any;
  timestamp: number;
  promise?: Promise<any>;
}

const apiCache = new Map<string, CacheEntry>();
const CACHE_DURATION = 30000; // 30秒缓存
const MAX_RETRIES = 3; // 最大重试次数
const RETRY_DELAY = 1000; // 重试延迟（毫秒）

// 延迟函数
const delay = (ms: number): Promise<void> =>
  new Promise(resolve => setTimeout(resolve, ms));

// 重试机制
const withRetry = async <T>(
  fn: () => Promise<T>,
  retries = MAX_RETRIES
): Promise<T> => {
  try {
    return await fn();
  } catch (error) {
    if (retries > 0) {
      console.warn(`Request failed, retrying... (${MAX_RETRIES - retries + 1}/${MAX_RETRIES})`, error);
      await delay(RETRY_DELAY);
      return withRetry(fn, retries - 1);
    }
    throw error;
  }
};

/**
 * 获取概览数据
 */
export const fetchOverviewData = async (
  forceRefresh = false
): Promise<OverviewData> => {
  const url = getApiUrl('/analytics/overview');
  const cacheKey = url;

  // 检查缓存
  if (!forceRefresh) {
    const cachedEntry = apiCache.get(cacheKey);
    if (cachedEntry) {
      // 如果有正在进行的请求，返回该Promise
      if (cachedEntry.promise) {
        return cachedEntry.promise;
      }
      
      // 如果缓存未过期，直接返回缓存数据
      if (Date.now() - cachedEntry.timestamp < CACHE_DURATION) {
        return Promise.resolve(cachedEntry.data);
      }
    }
  }

  // 创建新请求
  const promise = withRetry(async () => {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': getToken() || '',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: ApiResponse<OverviewData> = await response.json();

    if (result.success && result.data) {
      // 更新缓存
      apiCache.set(cacheKey, {
        data: result.data,
        timestamp: Date.now(),
      });
      return result.data;
    } else {
      throw new Error(result.error || result.message || 'Failed to fetch overview data');
    }
  }).catch(error => {
    // 删除缓存中的promise
    const cachedEntry = apiCache.get(cacheKey);
    if (cachedEntry) {
      delete cachedEntry.promise;
    }
    throw error;
  });

  // 将promise存入缓存
  apiCache.set(cacheKey, {
    ...(apiCache.get(cacheKey) || { data: null, timestamp: 0 }),
    promise,
  });

  return promise;
};

/**
 * 获取概览摘要数据
 */
export const fetchOverviewSummary = async (
  forceRefresh = false
): Promise<any> => {
  const url = getApiUrl('/analytics/overview/summary');
  const cacheKey = url;

  // 检查缓存
  if (!forceRefresh) {
    const cachedEntry = apiCache.get(cacheKey);
    if (cachedEntry) {
      // 如果有正在进行的请求，返回该Promise
      if (cachedEntry.promise) {
        return cachedEntry.promise;
      }
      
      // 如果缓存未过期，直接返回缓存数据
      if (Date.now() - cachedEntry.timestamp < CACHE_DURATION) {
        return Promise.resolve(cachedEntry.data);
      }
    }
  }

  // 创建新请求
  const promise = withRetry(async () => {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': getToken() || '',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (result.success && result.data) {
      // 更新缓存
      apiCache.set(cacheKey, {
        data: result.data,
        timestamp: Date.now(),
      });
      return result.data;
    } else {
      throw new Error(result.error || result.message || 'Failed to fetch overview summary');
    }
  }).catch(error => {
    // 删除缓存中的promise
    const cachedEntry = apiCache.get(cacheKey);
    if (cachedEntry) {
      delete cachedEntry.promise;
    }
    throw error;
  });

  // 将promise存入缓存
  apiCache.set(cacheKey, {
    ...(apiCache.get(cacheKey) || { data: null, timestamp: 0 }),
    promise,
  });

  return promise;
};

/**
 * 清除缓存
 */
export const clearOverviewCache = (): void => {
  apiCache.clear();
};
