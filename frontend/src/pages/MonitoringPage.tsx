import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  fetchSystemHealth,
  fetchPerformanceMetrics,
  fetchSystemStatus,
  fetchDatabaseStatus,
  fetchServersStatus,
  fetchRealtimeData,
  SystemHealth,
  PerformanceMetrics,
  SystemStatus,
  DatabaseStatus,
  ServersStatusResponse,
  RealtimeData,
  formatBytes,
  formatUptime,
} from '../services/monitoringService';
import {
  websocketClient,
  WSChannel,
  ConnectionState
} from '../services/websocketService';

// Status indicator component
const StatusIndicator: React.FC<{ status: 'healthy' | 'warning' | 'critical' | 'up' | 'down' }> = ({ status }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'healthy':
      case 'up':
        return 'bg-green-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'critical':
      case 'down':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className={`w-3 h-3 rounded-full ${getStatusColor()}`} />
  );
};

// System Health Card
const SystemHealthCard: React.FC<{ health: SystemHealth | null; loading: boolean }> = ({ health, loading }) => {
  const { t } = useTranslation();

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {t('monitoring.systemHealth', 'System Health')}
        </h3>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (!health) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {t('monitoring.systemHealth', 'System Health')}
        </h3>
        <p className="text-red-500">{t('monitoring.healthError', 'Failed to load health data')}</p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          {t('monitoring.systemHealth', 'System Health')}
        </h3>
        <div className="flex items-center space-x-2">
          <StatusIndicator status={health.status} />
          <span className={`text-sm font-medium ${
            health.status === 'healthy' ? 'text-green-600' :
            health.status === 'warning' ? 'text-yellow-600' : 'text-red-600'
          }`}>
            {health.status === 'healthy' ? t('monitoring.status.healthy', '健康') :
             health.status === 'warning' ? t('monitoring.status.warning', '警告') :
             t('monitoring.status.critical', '严重')}
          </span>
        </div>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {t('monitoring.database', 'Database')}
          </span>
          <div className="flex items-center space-x-2">
            <StatusIndicator status={health.services.database.status} />
            <span className="text-sm">
              {health.services.database.status === 'up' ? t('monitoring.status.up', '正常') : t('monitoring.status.down', '异常')}
            </span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {t('monitoring.mcpServers', 'MCP Servers')}
          </span>
          <div className="flex items-center space-x-2">
            <StatusIndicator status={health.services.mcpServers.status} />
            <span className="text-sm">
              {health.services.mcpServers.active}/{health.services.mcpServers.count}
            </span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {t('monitoring.cache', 'Cache')}
          </span>
          <div className="flex items-center space-x-2">
            <StatusIndicator status={health.services.cache.status} />
            <span className="text-sm">
              {health.services.cache.status === 'up' ? t('monitoring.status.up', '正常') : t('monitoring.status.down', '异常')}
            </span>
          </div>
        </div>
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          {t('monitoring.lastCheck', 'Last check')}: {new Date(health.lastCheck).toLocaleString()}
        </p>
      </div>
    </div>
  );
};

// Performance Metrics Card
const PerformanceMetricsCard: React.FC<{ metrics: PerformanceMetrics | null; loading: boolean }> = ({ metrics, loading }) => {
  const { t } = useTranslation();

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {t('monitoring.performanceMetrics', 'Performance Metrics')}
        </h3>
        <div className="animate-pulse space-y-3">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {t('monitoring.performanceMetrics', 'Performance Metrics')}
        </h3>
        <p className="text-red-500">{t('monitoring.metricsError', 'Failed to load metrics data')}</p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
        {t('monitoring.performanceMetrics', 'Performance Metrics')}
      </h3>

      <div className="space-y-4">
        {/* CPU Usage */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {t('monitoring.cpuUsage', 'CPU Usage')}
            </span>
            <span className="text-sm font-medium">{metrics.cpu.usage.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full"
              style={{ width: `${Math.min(metrics.cpu.usage, 100)}%` }}
            ></div>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {metrics.cpu.cores} {t('monitoring.cores', 'cores')}
          </p>
        </div>

        {/* Memory Usage */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {t('monitoring.memoryUsage', 'Memory Usage')}
            </span>
            <span className="text-sm font-medium">{metrics.memory.usage.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-green-600 h-2 rounded-full"
              style={{ width: `${Math.min(metrics.memory.usage, 100)}%` }}
            ></div>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {formatBytes(metrics.memory.used)} / {formatBytes(metrics.memory.total)}
          </p>
        </div>

        {/* Database Performance */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            {t('monitoring.database', 'Database')}
          </h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">
                {t('monitoring.connections', 'Connections')}
              </span>
              <p className="font-medium">{metrics.database.connections}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">
                {t('monitoring.responseTime', 'Response Time')}
              </span>
              <p className="font-medium">{metrics.database.responseTime}{t('monitoring.units.ms', 'ms')}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// System Status Card
const SystemStatusCard: React.FC<{ status: SystemStatus | null; loading: boolean }> = ({ status, loading }) => {
  const { t } = useTranslation();

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {t('monitoring.systemStatus', 'System Status')}
        </h3>
        <div className="animate-pulse space-y-3">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  if (!status) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {t('monitoring.systemStatus', 'System Status')}
        </h3>
        <p className="text-red-500">{t('monitoring.statusError', 'Failed to load status data')}</p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
        {t('monitoring.systemStatus', 'System Status')}
      </h3>

      <div className="space-y-4">
        {/* System Info */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            {t('monitoring.systemInfo', 'System Information')}
          </h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">
                {t('monitoring.uptime', 'Uptime')}
              </span>
              <p className="font-medium">{formatUptime(status.system.uptime)}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">
                {t('monitoring.version', 'Version')}
              </span>
              <p className="font-medium">{status.system.version}</p>
            </div>
          </div>
        </div>

        {/* Servers Summary */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            {t('monitoring.servers', 'Servers')}
          </h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">
                {t('monitoring.running', 'Running')}
              </span>
              <p className="font-medium text-green-600">{status.servers.running}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">
                {t('monitoring.total', 'Total')}
              </span>
              <p className="font-medium">{status.servers.total}</p>
            </div>
          </div>
        </div>

        {/* Performance Summary */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            {t('monitoring.performance', 'Performance')}
          </h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">
                {t('monitoring.totalCalls', 'Total Calls')}
              </span>
              <p className="font-medium">{status.performance.totalCalls.toLocaleString()}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">
                {t('monitoring.successRate', 'Success Rate')}
              </span>
              <p className="font-medium text-green-600">{status.performance.successRate.toFixed(1)}%</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const MonitoringPage: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [health, setHealth] = useState<SystemHealth | null>(null);
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [connectionState, setConnectionState] = useState<ConnectionState>(ConnectionState.DISCONNECTED);
  const [realtimeEnabled, setRealtimeEnabled] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Handle real-time data updates
  const handleRealtimeData = useCallback((data: RealtimeData) => {
    console.log('Received real-time data:', data);
    // Update metrics with real-time data
    setMetrics(prevMetrics => {
      if (!prevMetrics) return prevMetrics;
      return {
        ...prevMetrics,
        cpu: {
          ...prevMetrics.cpu,
          usage: data.system.cpu,
        },
        memory: {
          ...prevMetrics.memory,
          usage: data.system.memory,
        },
        api: {
          ...prevMetrics.api,
          requestsPerMinute: data.performance.requestsPerMinute,
          averageResponseTime: data.performance.avgResponseTime,
        },
        database: {
          ...prevMetrics.database,
          responseTime: data.database.responseTime,
        },
      };
    });

    // Update status with real-time data
    setStatus(prevStatus => {
      if (!prevStatus) return prevStatus;
      return {
        ...prevStatus,
        servers: {
          ...prevStatus.servers,
          running: data.servers.active,
          total: data.servers.total,
        },
        performance: {
          ...prevStatus.performance,
          totalCalls: data.performance.totalCalls,
        },
        database: {
          ...prevStatus.database,
          connected: data.database.connected,
        },
      };
    });
  }, []);

  // Handle connection state changes
  const handleConnectionStateChange = useCallback((state: ConnectionState) => {
    setConnectionState(state);
    console.log('WebSocket connection state changed:', state);
  }, []);

  // Handle WebSocket errors
  const handleWebSocketError = useCallback((error: string) => {
    console.error('WebSocket error:', error);
  }, []);

  const loadMonitoringData = async () => {
    setLoading(true);
    setError(null);
    try {
      const [healthResponse, metricsResponse, statusResponse] = await Promise.all([
        fetchSystemHealth(),
        fetchPerformanceMetrics(),
        fetchSystemStatus(),
      ]);

      let hasError = false;
      let errorMessages: string[] = [];

      if (healthResponse.success) {
        setHealth(healthResponse.data);
      } else {
        hasError = true;
        errorMessages.push(`健康检查失败: ${healthResponse.error || '未知错误'}`);
      }

      if (metricsResponse.success) {
        setMetrics(metricsResponse.data);
      } else {
        hasError = true;
        errorMessages.push(`性能指标获取失败: ${metricsResponse.error || '未知错误'}`);
      }

      if (statusResponse.success) {
        setStatus(statusResponse.data);
      } else {
        hasError = true;
        errorMessages.push(`系统状态获取失败: ${statusResponse.error || '未知错误'}`);
      }

      if (hasError) {
        setError(errorMessages.join('; '));
      } else {
        setLastUpdate(new Date());
      }
    } catch (error) {
      console.error('Error loading monitoring data:', error);
      setError(`加载监控数据时发生错误: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  // Toggle auto refresh (try WebSocket first, fallback to polling)
  const toggleRealtimeUpdates = () => {
    if (realtimeEnabled) {
      // Disable auto updates
      websocketClient.unsubscribe([WSChannel.REALTIME_DATA, WSChannel.SYSTEM_HEALTH, WSChannel.PERFORMANCE_METRICS]);
      websocketClient.removeEventListener(WSChannel.REALTIME_DATA, handleRealtimeData);
      setRealtimeEnabled(false);
    } else {
      // Enable auto updates - try WebSocket, but don't require it
      if (connectionState === ConnectionState.CONNECTED) {
        websocketClient.subscribe([WSChannel.REALTIME_DATA, WSChannel.SYSTEM_HEALTH, WSChannel.PERFORMANCE_METRICS]);
        websocketClient.addEventListener(WSChannel.REALTIME_DATA, handleRealtimeData);
      }
      // Always enable the flag - polling will work as fallback
      setRealtimeEnabled(true);
    }
  };

  useEffect(() => {
    loadMonitoringData();

    // Set up WebSocket listeners
    websocketClient.addConnectionListener(handleConnectionStateChange);
    websocketClient.addErrorListener(handleWebSocketError);

    // Set initial connection state
    setConnectionState(websocketClient.getConnectionState());

    // Try to initialize WebSocket if not already connected
    if (websocketClient.getConnectionState() === ConnectionState.DISCONNECTED) {
      websocketClient.initialize();
    }

    // Set up auto-refresh with different intervals based on mode
    const interval = setInterval(() => {
      if (realtimeEnabled) {
        // If auto-refresh is enabled but WebSocket is not connected, poll more frequently
        if (connectionState !== ConnectionState.CONNECTED) {
          loadMonitoringData();
        }
      } else {
        // If auto-refresh is disabled, poll less frequently for basic updates
        loadMonitoringData();
      }
    }, realtimeEnabled ? 10000 : 60000); // 10s when auto-refresh on, 60s when off

    return () => {
      clearInterval(interval);
      websocketClient.removeConnectionListener(handleConnectionStateChange);
      websocketClient.removeErrorListener(handleWebSocketError);
      if (realtimeEnabled) {
        websocketClient.unsubscribe([WSChannel.REALTIME_DATA, WSChannel.SYSTEM_HEALTH, WSChannel.PERFORMANCE_METRICS]);
        websocketClient.removeEventListener(WSChannel.REALTIME_DATA, handleRealtimeData);
      }
    };
  }, [handleConnectionStateChange, handleWebSocketError, handleRealtimeData, realtimeEnabled]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('monitoring.title', 'System Monitoring')}
          </h1>

          {/* Last Update Time */}
          {lastUpdate && (
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {t('monitoring.lastUpdate', 'Last updated')}: {lastUpdate.toLocaleString()}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-3">
          {/* Auto Refresh Toggle */}
          <button
            onClick={toggleRealtimeUpdates}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              realtimeEnabled
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            {realtimeEnabled ? t('monitoring.autoRefreshOn', '自动刷新 开') : t('monitoring.autoRefreshOff', '自动刷新 关')}
          </button>

          {/* Manual Refresh Button */}
          <button
            onClick={loadMonitoringData}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? t('common.loading', 'Loading...') : t('common.refresh', 'Refresh')}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                {t('monitoring.error', 'Monitoring Error')}
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                {error}
              </div>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={() => setError(null)}
                className="inline-flex rounded-md bg-red-50 dark:bg-red-900/20 p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/40"
              >
                <svg className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <SystemHealthCard health={health} loading={loading} />
        <PerformanceMetricsCard metrics={metrics} loading={loading} />
        <SystemStatusCard status={status} loading={loading} />
      </div>
    </div>
  );
};

export default MonitoringPage;
