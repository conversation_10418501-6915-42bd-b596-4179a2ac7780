import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  fetchHistoryData,
  exportHistoryData,
  fetchFilterOptions,
  fetchTrendsData,
  cleanupTestData,
  HistoryQueryParams,
  HistoryCallRecord,
  FilterOptions,
  TrendsQueryResponse,
  TrendPoint,
  TrendAnalysis,
  AnomalyPoint
} from '../services/analyticsService';
import Pagination from '../components/ui/Pagination';
import DateRangeSelector, { DateRange } from '../components/analytics/DateRangeSelector';
import MultiSelectFilter from '../components/analytics/MultiSelectFilter';
import FilterPresets, { FilterPreset } from '../components/analytics/FilterPresets';
import InteractiveTimeSeriesChart from '../components/analytics/InteractiveTimeSeriesChart';
import { NoDataEmptyState, ErrorEmptyState } from '../components/ui/EmptyState';
import { <PERSON><PERSON>pinner, InlineSpinner } from '../components/ui/LoadingSpinner';
import useErrorHandler from '../hooks/useErrorHandler';

// Analytics子页面组件
import OverviewTab from '../components/analytics/OverviewTab';

const HistoryTab: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [records, setRecords] = useState<HistoryCallRecord[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  });
  const [filters, setFilters] = useState<HistoryQueryParams>({
    startTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date().toISOString(),
    serverNames: [],
    toolNames: [],
    clientIps: [],
    success: undefined
  });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    serverNames: [],
    toolNames: [],
    clientIps: []
  });
  const [loadingFilterOptions, setLoadingFilterOptions] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [exportError, setExportError] = useState<string | null>(null);
  const [cleaningTestData, setCleaningTestData] = useState(false);
  const [cleanupMessage, setCleanupMessage] = useState<string | null>(null);

  // Load filter options
  const loadFilterOptions = useCallback(async (serverNames?: string[]) => {
    setLoadingFilterOptions(true);
    try {
      const response = await fetchFilterOptions(serverNames);
      if (response.success && response.filters) {
        setFilterOptions(response.filters);
      } else {
        console.error('Failed to load filter options:', response.error);
        // Set default empty arrays to prevent undefined errors
        setFilterOptions({
          serverNames: [],
          toolNames: [],
          clientIps: []
        });
      }
    } catch (error) {
      console.error('Error loading filter options:', error);
      // Set default empty arrays to prevent undefined errors
      setFilterOptions({
        serverNames: [],
        toolNames: [],
        clientIps: []
      });
    } finally {
      setLoadingFilterOptions(false);
    }
  }, []);;

  // Load history data
  const loadHistoryData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const queryParams: HistoryQueryParams = {
        ...filters,
        page: pagination.page,
        pageSize: pagination.pageSize
      };

      const result = await fetchHistoryData(queryParams);
      setRecords(result.data || []);
      setPagination(prev => ({
        ...prev,
        total: result.pagination?.total || 0,
        totalPages: result.pagination?.totalPages || 0
      }));
    } catch (error) {
      console.error('Error loading history data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load history data');
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.page, pagination.pageSize]);

  // Handle filter changes
  const handleFilterChange = (key: keyof HistoryQueryParams, value: any) => {
    setFilters(prev => {
      const newFilters = { ...prev, [key]: value };
      
      // 如果服务器筛选发生变化，清空工具筛选并重新加载工具选项
      if (key === 'serverNames') {
        newFilters.toolNames = []; // 清空当前工具选择
        // 异步重新加载工具选项
        loadFilterOptions(value.length > 0 ? value : undefined);
      }
      
      return newFilters;
    });
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
  };;

  // Handle date range change
  const handleDateRangeChange = (range: DateRange) => {
    setFilters(prev => ({
      ...prev,
      startTime: range.startTime,
      endTime: range.endTime
    }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handlePageSizeChange = (pageSize: number) => {
    setPagination(prev => ({ ...prev, pageSize, page: 1 }));
  };

  // Handle preset application
  const handleApplyPreset = (preset: FilterPreset) => {
    setFilters(preset.filters);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  // Handle export
  const handleExport = async (format: 'csv' | 'excel') => {
    setExporting(true);
    setExportError(null);
    try {
      const queryParams: HistoryQueryParams = {
        ...filters,
        page: 1,
        pageSize: 50000 // Large number for export
      };

      await exportHistoryData(queryParams, format);
    } catch (error) {
      console.error('Error exporting data:', error);
      setExportError(error instanceof Error ? error.message : 'Export failed');
    } finally {
      setExporting(false);
    }
  };

  // Handle cleanup test data
  const handleCleanupTestData = async () => {
    if (!window.confirm(t('analytics.confirmCleanupTestData', '确定要清理所有测试数据吗？此操作不可撤销。'))) {
      return;
    }

    setCleaningTestData(true);
    setCleanupMessage(null);
    try {
      const response = await cleanupTestData();
      if (response.success) {
        setCleanupMessage(t('analytics.cleanupSuccess', '测试数据清理成功'));
        // Reload data to reflect changes
        await loadHistoryData();
        await loadFilterOptions();
      } else {
        setCleanupMessage(response.error || t('analytics.cleanupFailed', '清理失败'));
      }
    } catch (error) {
      console.error('Error cleaning up test data:', error);
      setCleanupMessage(error instanceof Error ? error.message : t('analytics.cleanupFailed', '清理失败'));
    } finally {
      setCleaningTestData(false);
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  // Format response time
  const formatResponseTime = (responseTime: number | null) => {
    return responseTime ? `${responseTime}ms` : '-';
  };

  // Load initial data
  useEffect(() => {
    loadFilterOptions();
  }, [loadFilterOptions]);

  useEffect(() => {
    loadHistoryData();
  }, [loadHistoryData]);

  return (
    <div className="space-y-6">
      {/* Filters Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t('analytics.filters', 'Filters')}
          </h3>
          <button
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 focus:outline-none"
          >
            {showAdvancedFilters ?
              t('common.hideAdvanced', 'Hide Advanced') :
              t('analytics.advancedFilters', 'Advanced Filters')}
          </button>
        </div>

        {/* Date Range Selector */}
        <DateRangeSelector
          value={{ startTime: filters.startTime, endTime: filters.endTime }}
          onChange={handleDateRangeChange}
          className="mb-4"
        />

        {/* Basic Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
          {/* Success Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('analytics.status', 'Status')}
            </label>
            <select
              value={filters.success === undefined ? '' : filters.success.toString()}
              onChange={(e) => {
                const value = e.target.value;
                handleFilterChange('success', value === '' ? undefined : value === 'true');
              }}
              className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">{t('analytics.allStatus', 'All Status')}</option>
              <option value="true">{t('analytics.success', 'Success')}</option>
              <option value="false">{t('analytics.failed', 'Failed')}</option>
            </select>
          </div>

          {/* Server Filter */}
          <MultiSelectFilter
            label={t('analytics.serverFilter', 'Server Filter')}
            options={filterOptions.serverNames}
            value={filters.serverNames}
            onChange={(value) => handleFilterChange('serverNames', value)}
            placeholder={t('analytics.selectServer', 'Select Server')}
            loading={loadingFilterOptions}
          />

          {/* Tool Filter */}
          <MultiSelectFilter
            label={t('analytics.toolFilter', 'Tool Filter')}
            options={filterOptions.toolNames}
            value={filters.toolNames}
            onChange={(value) => handleFilterChange('toolNames', value)}
            placeholder={t('analytics.selectTool', 'Select Tool')}
            loading={loadingFilterOptions}
          />
        </div>

        {/* Advanced Filters */}
        {showAdvancedFilters && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mb-4">
            <h4 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-3">
              {t('analytics.advancedFilters', 'Advanced Filters')}
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              {/* Client IP Filter */}
              <MultiSelectFilter
                label={t('analytics.clientIpFilter', 'Client IP Filter')}
                options={filterOptions.clientIps}
                value={filters.clientIps}
                onChange={(value) => handleFilterChange('clientIps', value)}
                placeholder={t('analytics.selectClientIp', 'Select Client IP')}
                loading={loadingFilterOptions}
              />

              {/* Filter Presets */}
              <FilterPresets
                currentFilters={filters}
                onApplyPreset={handleApplyPreset}
              />
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="mt-4 flex flex-wrap gap-2">
          <button
            onClick={loadHistoryData}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {loading ? t('common.loading', 'Loading...') : t('analytics.search', 'Search')}
          </button>

          <button
            onClick={() => {
              setFilters({
                startTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().slice(0, 16),
                endTime: new Date().toISOString().slice(0, 16),
                serverNames: [],
                toolNames: [],
                clientIps: [],
                success: undefined
              });
            }}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            {t('analytics.reset', 'Reset')}
          </button>

          {/* Export Buttons */}
          <div className="flex space-x-2">
            <button
              onClick={() => handleExport('csv')}
              disabled={exporting || loading}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center"
            >
              {exporting && (
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              )}
              {exporting ? t('analytics.exporting', '导出中...') : t('analytics.exportCSV', '导出 CSV')}
            </button>

            <button
              onClick={() => handleExport('excel')}
              disabled={exporting || loading}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center"
            >
              {exporting && (
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 0 1 8-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 0 1 4 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              )}
              {exporting ? t('analytics.exporting', '导出中...') : t('analytics.exportExcel', '导出 Excel')}
            </button>
          </div>

          {/* Cleanup Test Data Button */}
          <button
            onClick={handleCleanupTestData}
            disabled={cleaningTestData || loading}
            className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-red-500 flex items-center"
          >
            {cleaningTestData && (
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 0 1 8-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 0 1 4 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            {cleaningTestData ? t('analytics.cleaningTestData', '清理中...') : t('analytics.cleanupTestData', '清理测试数据')}
          </button>
        </div>

        {/* Export Error */}
        {exportError && (
          <div className="mt-4">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
              <p className="text-red-800 dark:text-red-200">{exportError}</p>
            </div>
          </div>
        )}

        {/* Cleanup Message */}
        {cleanupMessage && (
          <div className="mt-4">
            <div className={`border rounded-md p-4 ${
              cleanupMessage.includes('成功') || cleanupMessage.includes('success')
                ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
            }`}>
              <p className={
                cleanupMessage.includes('成功') || cleanupMessage.includes('success')
                  ? 'text-green-800 dark:text-green-200'
                  : 'text-red-800 dark:text-red-200'
              }>
                {cleanupMessage}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Results Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        {/* Results Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {t('analytics.results', 'Results')}
            </h3>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="p-6">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
              <p className="text-red-800 dark:text-red-200">{error}</p>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="p-6">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-2 text-gray-600 dark:text-gray-400">
                {t('common.loading', 'Loading...')}
              </span>
            </div>
          </div>
        )}

        {/* Data Table */}
        {!loading && !error && (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('analytics.timestamp', 'Timestamp')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('analytics.server', 'Server')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('analytics.tool', 'Tool')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('analytics.status', 'Status')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('analytics.responseTime', 'Response Time')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('analytics.clientIp', 'Client IP')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('analytics.error', 'Error')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {records.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        {t('analytics.noData', 'No data available')}
                      </td>
                    </tr>
                  ) : (
                    records.map((record) => (
                      <tr key={record.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {formatTimestamp(record.timestamp)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {record.server_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {record.tool_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            record.success
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                              : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                          }`}>
                            {record.success ? t('analytics.success', 'Success') : t('analytics.failed', 'Failed')}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {formatResponseTime(record.response_time)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {record.client_ip || '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate">
                          {record.error_message || '-'}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                <Pagination
                  currentPage={pagination.page}
                  totalPages={pagination.totalPages}
                  onPageChange={handlePageChange}
                  pageSize={pagination.pageSize}
                  totalItems={pagination.total}
                  onPageSizeChange={handlePageSizeChange}
                  pageSizeOptions={[10, 20, 50, 100]}
                  showPageSizeSelector={true}
                  showDetailedInfo={true}
                  showJumpToPage={true}
                />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

const TrendsTab: React.FC = () => {
  const { t } = useTranslation();
  const [trendsData, setTrendsData] = useState<TrendsQueryResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');
  const [granularity, setGranularity] = useState<'hour' | 'day' | 'week'>('hour');
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>(['calls', 'success_rate', 'response_time']);

  // Convert series data to TrendPoint format
  const convertSeriesToTrendPoints = useCallback((trendsResponse: TrendsQueryResponse): TrendPoint[] => {
    if (!trendsResponse.success || !trendsResponse.data || !trendsResponse.data.series) {
      return [];
    }

    const { series } = trendsResponse.data;
    const trendPoints: TrendPoint[] = [];

    // Get the timestamps from any available series (they should all have the same timestamps)
    const timestamps = series.calls?.timestamps || series.success_rate?.timestamps || series.response_time?.timestamps || [];

    for (let i = 0; i < timestamps.length; i++) {
      trendPoints.push({
        timestamp: timestamps[i],
        calls: series.calls?.values[i] || 0,
        success_rate: series.success_rate?.values[i] || 0,
        response_time: series.response_time?.values[i] || 0,
      });
    }

    return trendPoints;
  }, []);

  // Load trends data
  const loadTrendsData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await fetchTrendsData({
        timeRange,
        granularity,
        metrics: selectedMetrics
      });
      setTrendsData(result);
    } catch (error) {
      console.error('Error loading trends data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load trends data');
    } finally {
      setLoading(false);
    }
  }, [timeRange, granularity, selectedMetrics]);

  // Load initial data
  useEffect(() => {
    loadTrendsData();
  }, [loadTrendsData]);

  const timeRangeOptions = [
    { value: '1h', label: t('analytics.timeRangeOptions.1h', 'Last Hour') },
    { value: '24h', label: t('analytics.timeRangeOptions.24h', 'Last 24 Hours') },
    { value: '7d', label: t('analytics.timeRangeOptions.7d', 'Last 7 Days') },
    { value: '30d', label: t('analytics.timeRangeOptions.30d', 'Last 30 Days') }
  ];

  const granularityOptions = [
    { value: 'hour', label: t('analytics.granularityOptions.hour', 'Hourly') },
    { value: 'day', label: t('analytics.granularityOptions.day', 'Daily') },
    { value: 'week', label: t('analytics.granularityOptions.week', 'Weekly') }
  ];

  const metricOptions = [
    { value: 'calls', label: t('analytics.metricsOptions.calls', 'API Calls') },
    { value: 'success_rate', label: t('analytics.metricsOptions.successRate', 'Success Rate') },
    { value: 'response_time', label: t('analytics.metricsOptions.responseTime', 'Response Time') }
  ];

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Time Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('analytics.timeRange', 'Time Range')}
            </label>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as any)}
              className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {timeRangeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Granularity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('analytics.granularity', 'Granularity')}
            </label>
            <select
              value={granularity}
              onChange={(e) => setGranularity(e.target.value as any)}
              className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {granularityOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Metrics */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('analytics.metrics', 'Metrics')}
            </label>
            <div className="space-y-2">
              {metricOptions.map(option => (
                <label key={option.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedMetrics.includes(option.value)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedMetrics(prev => [...prev, option.value]);
                      } else {
                        setSelectedMetrics(prev => prev.filter(m => m !== option.value));
                      }
                    }}
                    className="mr-2 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                  />
                  <span className="text-sm text-gray-900 dark:text-white">
                    {option.label}
                  </span>
                </label>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-4">
          <button
            onClick={loadTrendsData}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {loading ? t('common.loading', 'Loading...') : t('analytics.refresh', 'Refresh')}
          </button>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <p className="text-red-800 dark:text-red-200">{error}</p>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-600 dark:text-gray-400">
              {t('common.loading', 'Loading...')}
            </span>
          </div>
        </div>
      )}

      {/* Charts */}
      {!loading && !error && trendsData && (
        <div className="space-y-6">
          {selectedMetrics.map(metric => {
            const trendPoints = convertSeriesToTrendPoints(trendsData);
            return (
              <div key={metric} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  {metricOptions.find(m => m.value === metric)?.label}
                </h3>
                <div className="h-64">
                  <InteractiveTimeSeriesChart
                    data={trendPoints}
                    metric={metric as 'calls' | 'success_rate' | 'response_time'}
                    height={250}
                    width={0}
                    showTooltip={true}
                    enableZoom={true}
                    enablePan={true}
                    compact={false}
                    anomalies={trendsData.data?.anomalies}
                  />
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* No Data State */}
      {!loading && !error && (!trendsData || !trendsData.success || !trendsData.data || convertSeriesToTrendPoints(trendsData).length === 0) && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <NoDataEmptyState
            title={t('analytics.noTrendsData', 'No trends data available')}
            description={t('analytics.noTrendsDataDesc', 'Try adjusting the time range or check back later.')}
          />
        </div>
      )}
    </div>
  );
};

const MonitoringTab: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
        {t('pages.analytics.monitoring')}
      </h2>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <p className="text-gray-600 dark:text-gray-400">
          监控面板页面内容将在后续任务中实现
        </p>
      </div>
    </div>
  );
};

// 标签页配置
// 标签页配置 - 需要在组件内部使用t函数
const getTabsConfig = (t: any) => [
  { id: 'overview', label: t('pages.analytics.overview', 'Overview'), component: OverviewTab },
  { id: 'history', label: t('pages.analytics.history', 'History'), component: HistoryTab },
  { id: 'trends', label: t('pages.analytics.trends', 'Trends'), component: TrendsTab },
  { id: 'monitoring', label: t('pages.analytics.monitoring', 'Monitoring'), component: MonitoringTab },
];

const AnalyticsPage: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('overview');

  // 获取标签页配置
  const tabs = getTabsConfig(t);

  // 获取当前活动标签页的组件
  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || OverviewTab;

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          {t('pages.analytics.title', 'Analytics')}
        </h1>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
          {t('pages.analytics.description', 'View analytics and insights for your MCP servers')}
        </p>
      </div>

      {/* 标签页导航 */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* 标签页内容 */}
      <div className="mt-6">
        <ActiveComponent />
      </div>
    </div>
  );
};

export default AnalyticsPage;
