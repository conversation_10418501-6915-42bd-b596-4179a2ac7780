import React, { useState, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useServerData } from '@/hooks/useServerData';
import { ServerCallStats } from '@/types';
import Pagination from '../components/ui/Pagination';
import { fetchTrendsData, TrendPoint, TrendAnalysis } from '../services/analyticsService';
import InteractiveTimeSeriesChart from '../components/analytics/InteractiveTimeSeriesChart';

interface DashboardAnalytics {
  totalCalls24h: number;
  successRate24h: number;
  avgResponseTime24h: number;
  trendsData: TrendPoint[];
  callsTrend: TrendAnalysis;
  successRateTrend: TrendAnalysis;
  responseTimeTrend: TrendAnalysis;
}

// Fetch analytics data
const fetchAnalyticsData = async (): Promise<DashboardAnalytics | null> => {
  try {
    // Fetch trends data for the last 24 hours
    const trendsResult = await fetchTrendsData({
      timeRange: '24h',
      granularity: 'hour',
      metrics: ['calls', 'success_rate', 'response_time']
    });

    if (!trendsResult.success) {
      throw new Error(trendsResult.error || 'Failed to fetch trends data');
    }

    const { series, analysis } = trendsResult.data;

    // Convert series data to TrendPoint format
    const trendsData: TrendPoint[] = [];
    if (series && series.calls && series.calls.timestamps) {
      for (let i = 0; i < series.calls.timestamps.length; i++) {
        trendsData.push({
          timestamp: series.calls.timestamps[i],
          calls: series.calls.values[i] || 0,
          success_rate: series.success_rate?.values[i] || 0,
          response_time: series.response_time?.values[i] || 0,
        });
      }
    }
    
// Calculate 24h totals
    const totalCalls24h = trendsData.reduce((sum: number, point: TrendPoint) => sum + point.calls, 0);
    const avgSuccessRate24h = trendsData.length > 0
      ? trendsData.reduce((sum: number, point: TrendPoint) => sum + point.success_rate, 0) / trendsData.length
      : 0;
    const avgResponseTime24h = trendsData.length > 0
      ? trendsData.reduce((sum: number, point: TrendPoint) => sum + point.response_time, 0) / trendsData.length
      : 0;

    return {
      totalCalls24h,
      successRate24h: Math.round(avgSuccessRate24h),
      avgResponseTime24h: Math.round(avgResponseTime24h),
      trendsData: trendsData,
      callsTrend: analysis.calls,
      successRateTrend: analysis.success_rate,
      responseTimeTrend: analysis.response_time,
    };
  } catch (error) {
    console.error('Error fetching analytics data:', error);
    return null;
  }
};;

const DashboardPage: React.FC = () => {
  const { t } = useTranslation();
  const { servers, error, setError, isLoading } = useServerData();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Analytics state
  const [analyticsData, setAnalyticsData] = useState<DashboardAnalytics | null>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(true);
  const [analyticsError, setAnalyticsError] = useState<string | null>(null);

  // Fetch analytics data on component mount
  useEffect(() => {
    const loadAnalyticsData = async () => {
      setAnalyticsLoading(true);
      setAnalyticsError(null);
      
      const data = await fetchAnalyticsData();
      if (data) {
        setAnalyticsData(data);
      } else {
        setAnalyticsError('Failed to load analytics data');
      }
      
      setAnalyticsLoading(false);
    };

    loadAnalyticsData();
  }, []);

  // Sort servers by call count (totalCalls) in descending order
  const sortedServers = useMemo(() => {
    return [...servers].sort((a, b) => {
      const aCallCount = a.callStats?.totalCalls || 0;
      const bCallCount = b.callStats?.totalCalls || 0;
      return bCallCount - aCallCount;
    });
  }, [servers]);

  // Calculate pagination
  const totalPages = Math.ceil(sortedServers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedServers = sortedServers.slice(startIndex, endIndex);

  // Calculate server statistics
  const serverStats = {
    total: servers.length,
    online: servers.filter(server => server.status === 'connected').length,
    offline: servers.filter(server => server.status === 'disconnected').length,
    connecting: servers.filter(server => server.status === 'connecting').length
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Enhanced trend indicator component with clear labeling
  const TrendIndicator = ({ trend, changeRate }: { trend: TrendAnalysis; changeRate: number }) => {
    const isPositive = trend.trend === 'increasing';
    const isNegative = trend.trend === 'decreasing';
    const isStable = trend.trend === 'stable';

    return (
      <div className="flex flex-col items-end text-right">
        <div className="flex items-center space-x-1">
          {isPositive && (
            <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7m0 10H7" />
            </svg>
          )}
          {isNegative && (
            <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10m0-10h10" />
            </svg>
          )}
          {isStable && (
            <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          )}
          <span className={`text-sm font-medium ${
            isPositive ? 'text-green-600' : isNegative ? 'text-red-600' : 'text-gray-600'
          }`}>
            {isPositive ? '+' : isNegative ? '-' : ''}{Math.abs(changeRate).toFixed(1)}%
          </span>
        </div>
        <div className="text-xs text-gray-500 mt-1">
          {t('pages.dashboard.changeRateLabel', 'vs 前24h')}
        </div>
      </div>
    );
  };



  // Map status to translation keys
  const statusTranslations = {
    connected: 'status.online',
    disconnected: 'status.offline',
    connecting: 'status.connecting'
  }
  // Format call statistics for display
  const formatCallStats = (callStats?: ServerCallStats) => {
    if (!callStats || callStats.totalCalls === 0) {
      return {
        display: t('server.neverCalled'),
        className: 'text-gray-400'
      };
    }

    const { totalCalls, lastCallTime, successCalls, failedCalls } = callStats;
    const successRate = totalCalls > 0 ? Math.round((successCalls / totalCalls) * 100) : 0;
    
    // Format last call time
    let timeDisplay = '';
    if (lastCallTime) {
      const now = Date.now();
      const diffMs = now - lastCallTime;
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      
      if (diffMinutes < 1) {
        timeDisplay = t('server.justNow');
      } else if (diffMinutes < 60) {
        timeDisplay = t('server.minutesAgo', { minutes: diffMinutes });
      } else if (diffHours < 24) {
        timeDisplay = t('server.hoursAgo', { hours: diffHours });
      } else {
        timeDisplay = t('server.daysAgo', { days: diffDays });
      }
    }

    return {
      display: (
        <div className="text-xs">
          <div className="font-medium">{t('server.callCount', { count: totalCalls })}</div>
          {timeDisplay && <div className="text-gray-400">{timeDisplay}</div>}
          <div className={`${successRate >= 90 ? 'text-green-600' : successRate >= 70 ? 'text-yellow-600' : 'text-red-600'}`}>
            {t('server.successRate', { rate: successRate })}
          </div>
        </div>
      ),
      className: 'text-gray-600'
    };
  };

  return (
    <div>
      <h1 className="text-2xl font-bold text-gray-900 mb-8">{t('pages.dashboard.title')}</h1>

      {error && (
        <div className="mb-6 bg-red-50 border-l-4 border-red-500 p-4 rounded shadow-sm error-box">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-status-red text-lg font-medium">{t('app.error')}</h3>
              <p className="text-gray-600 mt-1">{error}</p>
            </div>
            <button
              onClick={() => setError(null)}
              className="ml-4 text-gray-500 hover:text-gray-700 transition-colors duration-200"
              aria-label={t('app.closeButton')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 011.414 0L10 8.586l4.293-4.293a1 1 111.414 1.414L11.414 10l4.293 4.293a1 1 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 01-1.414-1.414L8.586 10 4.293 5.707a1 1 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {isLoading && (
        <div className="bg-white shadow rounded-lg p-6 flex items-center justify-center loading-container">
          <div className="flex flex-col items-center">
            <svg className="animate-spin h-10 w-10 text-blue-500 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p className="text-gray-600">{t('app.loading')}</p>
          </div>
        </div>
      )}

      {!isLoading && (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {/* Total servers */}
          <div className="bg-white rounded-lg shadow p-6 dashboard-card">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-800 icon-container status-icon-blue">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                </svg>
              </div>
              <div className="ml-4">
                <h2 className="text-xl font-semibold text-gray-700">{t('pages.dashboard.totalServers')}</h2>
                <p className="text-3xl font-bold text-gray-900">{serverStats.total}</p>
              </div>
            </div>
          </div>

          {/* Online servers */}
          <div className="bg-white rounded-lg shadow p-6 dashboard-card">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-800 icon-container status-icon-green">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <h2 className="text-xl font-semibold text-gray-700">{t('pages.dashboard.onlineServers')}</h2>
                <p className="text-3xl font-bold text-gray-900">{serverStats.online}</p>
              </div>
            </div>
          </div>

          {/* Offline servers */}
          <div className="bg-white rounded-lg shadow p-6 dashboard-card">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-red-100 text-red-800 icon-container status-icon-red">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <h2 className="text-xl font-semibold text-gray-700">{t('pages.dashboard.offlineServers')}</h2>
                <p className="text-3xl font-bold text-gray-900">{serverStats.offline}</p>
              </div>
            </div>
          </div>

          {/* Connecting servers */}
          <div className="bg-white rounded-lg shadow p-6 dashboard-card">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 text-yellow-800 icon-container status-icon-yellow">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <h2 className="text-xl font-semibold text-gray-700">{t('pages.dashboard.connectingServers')}</h2>
                <p className="text-3xl font-bold text-gray-900">{serverStats.connecting}</p>
              </div>
            </div>

          </div>
        </div>
      )}

      {/* Analytics Section */}
      {!isLoading && !analyticsLoading && analyticsData && (
        <div className="mt-8">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">{t('pages.dashboard.analytics24h', 'Last 24 Hours Analytics')}</h2>
            <p className="text-sm text-gray-600 leading-relaxed">
              {t('pages.dashboard.analyticsDescription', 'This feature displays key performance metrics for the past 24 hours, helping you monitor API usage, identify performance trends, and detect potential issues early.')}
            </p>
          </div>
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* Total API Calls */}
            <div className="bg-white rounded-lg shadow p-6 min-w-0">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-500">{t('pages.dashboard.totalCalls24h', 'Total API Calls')}</h3>
                  <p className="text-2xl font-bold text-gray-900">{analyticsData.totalCalls24h.toLocaleString()}</p>
                </div>
                <div className="flex-shrink-0 ml-4">
                  <TrendIndicator trend={analyticsData.callsTrend} changeRate={analyticsData.callsTrend.changeRate} />
                </div>
              </div>
              <div className="mt-4 w-full overflow-hidden">
                <div className="w-full h-56">
                  <InteractiveTimeSeriesChart
                    data={analyticsData.trendsData}
                    metric="calls"
                    height={220}
                    width={0} // Will be calculated by component
                    showTooltip={true}
                    enableZoom={false}
                    enablePan={false}
                    compact={false}
                  />
                </div>
              </div>
            </div>

            {/* Success Rate */}
            <div className="bg-white rounded-lg shadow p-6 min-w-0">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-500">{t('pages.dashboard.successRate24h', 'Success Rate')}</h3>
                  <p className="text-2xl font-bold text-gray-900">{analyticsData.successRate24h}%</p>
                </div>
                <div className="flex-shrink-0 ml-4">
                  <TrendIndicator trend={analyticsData.successRateTrend} changeRate={analyticsData.successRateTrend.changeRate} />
                </div>
              </div>
              <div className="mt-4 w-full overflow-hidden">
                <div className="w-full h-56">
                  <InteractiveTimeSeriesChart
                    data={analyticsData.trendsData}
                    metric="success_rate"
                    height={220}
                    width={0} // Will be calculated by component
                    showTooltip={true}
                    enableZoom={false}
                    enablePan={false}
                    compact={false}
                  />
                </div>
              </div>
            </div>

            {/* Average Response Time */}
            <div className="bg-white rounded-lg shadow p-6 min-w-0">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-500">{t('pages.dashboard.avgResponseTime24h', 'Avg Response Time')}</h3>
                  <p className="text-2xl font-bold text-gray-900">{analyticsData.avgResponseTime24h}ms</p>
                </div>
                <div className="flex-shrink-0 ml-4">
                  <TrendIndicator trend={analyticsData.responseTimeTrend} changeRate={analyticsData.responseTimeTrend.changeRate} />
                </div>
              </div>
              <div className="mt-4 w-full overflow-hidden">
                <div className="w-full h-56">
                  <InteractiveTimeSeriesChart
                    data={analyticsData.trendsData}
                    metric="response_time"
                    height={220}
                    width={0} // Will be calculated by component
                    showTooltip={true}
                    enableZoom={false}
                    enablePan={false}
                    compact={false}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Analytics Error */}
      {analyticsError && (
        <div className="mt-8 bg-yellow-50 border-l-4 border-yellow-500 p-4 rounded shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                {t('pages.dashboard.analyticsError', 'Analytics data unavailable')}: {analyticsError}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Server list with pagination */}
      {servers.length > 0 && !isLoading && (
        <div className="mt-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-900">{t('pages.dashboard.allServers')}</h2>
            <div className="text-sm text-gray-600">
              {t('pages.dashboard.showingServers', {
                start: startIndex + 1,
                end: Math.min(endIndex, sortedServers.length),
                total: sortedServers.length
              })}
            </div>
          </div>
          <div className="bg-white shadow rounded-lg overflow-hidden table-container">
            <table className="min-w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th scope="col" className="px-6 py-5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('server.name')}
                  </th>
                  <th scope="col" className="px-6 py-5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('server.status')}
                  </th>
                  <th scope="col" className="px-6 py-5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('server.tools')}
                  </th>
                  <th scope="col" className="px-6 py-5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('server.apiCalls')}
                  </th>
                  <th scope="col" className="px-6 py-5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('server.enabled')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedServers.map((server, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {server.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${server.status === 'connected'
                        ? 'status-badge-online'
                        : server.status === 'disconnected'
                          ? 'status-badge-offline'
                          : 'status-badge-connecting'
                        }`}>
                        {t(statusTranslations[server.status] || server.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {server.tools?.length || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {(() => {
                        const stats = formatCallStats(server.callStats);
                        return (
                          <div className={stats.className}>
                            {stats.display}
                          </div>
                        );
                      })()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {server.enabled !== false ? (
                        <span className="text-green-600">✓</span>
                      ) : (
                        <span className="text-status-red">✗</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default DashboardPage;