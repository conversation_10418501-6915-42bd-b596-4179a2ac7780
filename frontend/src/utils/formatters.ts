import { useTranslation } from 'react-i18next';

/**
 * 格式化工具函数，支持国际化
 */

// 获取当前语言的格式化配置
export const getFormatConfig = (t: any) => {
  return {
    locale: t('format.locale', 'en-US'),
    currency: t('format.currency', 'USD'),
    dateTime: {
      short: t('format.dateTime.short', 'MM/dd HH:mm'),
      medium: t('format.dateTime.medium', 'MM/dd/yyyy HH:mm:ss'),
      long: t('format.dateTime.long', 'MMMM dd, yyyy HH:mm:ss'),
    },
    number: {
      decimal: parseInt(t('format.number.decimal', '2')),
      thousand: t('format.number.thousand', ','),
      decimalPoint: t('format.number.decimalPoint', '.'),
    },
  };
};

/**
 * 格式化数值显示
 */
export const formatValue = (value: number, metricType: string, t: any) => {
  const config = getFormatConfig(t);
  const locale = config.locale;

  switch (metricType) {
    case 'calls':
      return value.toLocaleString(locale);
    case 'success_rate':
      return `${value.toFixed(1)}%`;
    case 'response_time':
      return `${value.toFixed(0)}${t('monitoring.units.ms', 'ms')}`;
    default:
      return value.toLocaleString(locale);
  }
};

/**
 * 格式化日期时间
 */
export const formatDate = (timestamp: string, format: 'short' | 'medium' | 'long' = 'medium', t: any) => {
  const config = getFormatConfig(t);
  const locale = config.locale;
  const date = new Date(timestamp);

  switch (format) {
    case 'short':
      return date.toLocaleString(locale, {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    case 'medium':
      return date.toLocaleString(locale, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    case 'long':
      return date.toLocaleString(locale, {
        year: 'numeric',
        month: 'long',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    default:
      return date.toLocaleString(locale);
  }
};

/**
 * 格式化时间（仅时分）
 */
export const formatTime = (timestamp: string, t: any) => {
  const config = getFormatConfig(t);
  const locale = config.locale;
  const date = new Date(timestamp);
  
  return date.toLocaleTimeString(locale, {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

/**
 * 格式化百分比
 */
export const formatPercentage = (value: number, decimals: number = 1, t: any) => {
  const config = getFormatConfig(t);
  const locale = config.locale;
  
  return `${value.toFixed(decimals)}%`;
};

/**
 * 格式化数字（带千分位分隔符）
 */
export const formatNumber = (value: number, decimals?: number, t?: any) => {
  const config = t ? getFormatConfig(t) : { locale: 'en-US' };
  const locale = config.locale;
  
  if (decimals !== undefined) {
    return value.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  
  return value.toLocaleString(locale);
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number, t: any) => {
  const config = getFormatConfig(t);
  const locale = config.locale;
  
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)).toLocaleString(locale) + ' ' + sizes[i];
};

/**
 * 格式化持续时间
 */
export const formatDuration = (milliseconds: number, t: any) => {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    return `${days}d ${hours % 24}h ${minutes % 60}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};

/**
 * Hook for using formatters with translation
 */
export const useFormatters = () => {
  const { t } = useTranslation();
  
  return {
    formatValue: (value: number, metricType: string) => formatValue(value, metricType, t),
    formatDate: (timestamp: string, format?: 'short' | 'medium' | 'long') => formatDate(timestamp, format, t),
    formatTime: (timestamp: string) => formatTime(timestamp, t),
    formatPercentage: (value: number, decimals?: number) => formatPercentage(value, decimals, t),
    formatNumber: (value: number, decimals?: number) => formatNumber(value, decimals, t),
    formatFileSize: (bytes: number) => formatFileSize(bytes, t),
    formatDuration: (milliseconds: number) => formatDuration(milliseconds, t),
  };
};
