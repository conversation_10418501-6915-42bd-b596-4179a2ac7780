export function generateRandomKey(length: number = 32): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return Array.from(array)
    .map((x) => characters.charAt(x % characters.length))
    .join('');
}

/**
 * 生成安全的Bearer认证密钥
 * @param length 密钥长度，默认48字符，范围32-64
 * @returns 安全的随机密钥字符串
 */
export function generateSecureBearerKey(length: number = 48): string {
  // 确保长度在合理范围内
  const keyLength = Math.max(32, Math.min(64, length));

  // 使用更安全的字符集，避免容易混淆的字符
  // 排除了 0、O、l、I 等容易混淆的字符
  const safeCharacters = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789-._';

  const array = new Uint8Array(keyLength);
  crypto.getRandomValues(array);

  return Array.from(array)
    .map((x) => safeCharacters.charAt(x % safeCharacters.length))
    .join('');
}
