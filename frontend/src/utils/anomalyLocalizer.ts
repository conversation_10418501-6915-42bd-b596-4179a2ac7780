import { TFunction } from 'i18next';

/**
 * 异常描述本地化工具
 * 将后端返回的英文异常描述转换为本地化文本
 */

interface AnomalyDescriptionParams {
  value?: number;
  percent?: number;
  from?: string;
  to?: string;
}

/**
 * 解析后端返回的异常描述并提取参数
 */
export const parseAnomalyDescription = (description: string): {
  type: string;
  params: AnomalyDescriptionParams;
} => {
  // 匹配不同类型的异常描述模式
  const patterns = [
    // Spike detected: 123.45 above expected
    {
      pattern: /Spike detected: ([\d.]+) above expected/,
      type: 'spike',
      extract: (match: RegExpMatchArray) => ({ value: parseFloat(match[1]) })
    },
    // Drop detected: 123.45 below expected
    {
      pattern: /Drop detected: ([\d.]+) below expected/,
      type: 'drop',
      extract: (match: RegExpMatchArray) => ({ value: parseFloat(match[1]) })
    },
    // Significant spike: 123.45 above expected (50% increase)
    {
      pattern: /Significant spike: ([\d.]+) above expected \((\d+)% increase\)/,
      type: 'significantSpike',
      extract: (match: RegExpMatchArray) => ({ 
        value: parseFloat(match[1]), 
        percent: parseInt(match[2]) 
      })
    },
    // Significant drop: 123.45 below expected (50% decrease)
    {
      pattern: /Significant drop: ([\d.]+) below expected \((\d+)% decrease\)/,
      type: 'significantDrop',
      extract: (match: RegExpMatchArray) => ({ 
        value: parseFloat(match[1]), 
        percent: parseInt(match[2]) 
      })
    },
    // Volatility detected: 25% deviation from recent trend
    {
      pattern: /Volatility detected: (\d+)% deviation from recent trend/,
      type: 'volatility',
      extract: (match: RegExpMatchArray) => ({ percent: parseInt(match[1]) })
    },
    // Trend break detected: 123.45 deviation from expected pattern
    {
      pattern: /Trend break detected: ([\d.]+) deviation from expected pattern/,
      type: 'trendBreak',
      extract: (match: RegExpMatchArray) => ({ value: parseFloat(match[1]) })
    },
    // Trend break: direction changed from up to down
    {
      pattern: /Trend break: direction changed from (\w+) to (\w+)/,
      type: 'trendBreakDirection',
      extract: (match: RegExpMatchArray) => ({ from: match[1], to: match[2] })
    }
  ];

  for (const { pattern, type, extract } of patterns) {
    const match = description.match(pattern);
    if (match) {
      return {
        type,
        params: extract(match)
      };
    }
  }

  // 如果没有匹配到任何模式，返回原始描述
  return {
    type: 'unknown',
    params: {}
  };
};

/**
 * 本地化异常描述
 */
export const localizeAnomalyDescription = (description: string, t: TFunction): string => {
  const { type, params } = parseAnomalyDescription(description);

  switch (type) {
    case 'spike':
      return t('analytics.anomaly.descriptions.spike', { value: params.value });
    
    case 'drop':
      return t('analytics.anomaly.descriptions.drop', { value: params.value });
    
    case 'significantSpike':
      return t('analytics.anomaly.descriptions.significantSpike', { 
        value: params.value, 
        percent: params.percent 
      });
    
    case 'significantDrop':
      return t('analytics.anomaly.descriptions.significantDrop', { 
        value: params.value, 
        percent: params.percent 
      });
    
    case 'volatility':
      return t('analytics.anomaly.descriptions.volatility', { percent: params.percent });
    
    case 'trendBreak':
      return t('analytics.anomaly.descriptions.trendBreak', { value: params.value });
    
    case 'trendBreakDirection':
      const fromText = params.from === 'up' ? 
        t('analytics.anomaly.descriptions.directionUp') : 
        t('analytics.anomaly.descriptions.directionDown');
      const toText = params.to === 'up' ? 
        t('analytics.anomaly.descriptions.directionUp') : 
        t('analytics.anomaly.descriptions.directionDown');
      
      return t('analytics.anomaly.descriptions.trendBreakDirection', { 
        from: fromText, 
        to: toText 
      });
    
    default:
      // 如果无法解析，返回原始描述
      return description;
  }
};

/**
 * 本地化异常建议
 */
export const localizeAnomalyRecommendation = (
  recommendation: string, 
  metric: string, 
  anomalyType: string, 
  severity: string, 
  t: TFunction
): string => {
  // 根据指标、异常类型和严重程度生成本地化建议
  const key = `${metric}${anomalyType.charAt(0).toUpperCase() + anomalyType.slice(1)}${severity.charAt(0).toUpperCase() + severity.slice(1)}`;
  
  // 尝试匹配特定的建议键
  const specificKey = `analytics.anomaly.recommendations.${key}`;
  const specificRecommendation = t(specificKey, { defaultValue: null });
  
  if (specificRecommendation) {
    return specificRecommendation;
  }

  // 尝试匹配通用建议
  const generalKeys = [
    `analytics.anomaly.recommendations.${metric}${anomalyType.charAt(0).toUpperCase() + anomalyType.slice(1)}`,
    `analytics.anomaly.recommendations.${metric}${severity.charAt(0).toUpperCase() + severity.slice(1)}`,
    'analytics.anomaly.recommendations.general'
  ];

  for (const key of generalKeys) {
    const translatedRecommendation = t(key, { defaultValue: null });
    if (translatedRecommendation) {
      return translatedRecommendation;
    }
  }

  // 如果都没有匹配到，返回原始建议
  const generalRecommendation = t('analytics.anomaly.recommendations.general', { defaultValue: null });
  return generalRecommendation || recommendation;
};

/**
 * Hook for using anomaly localizer with translation
 */
export const useAnomalyLocalizer = () => {
  // This would need to be implemented with useTranslation hook
  // For now, we'll return the functions that accept t parameter
  return {
    localizeDescription: (description: string, t: TFunction) => 
      localizeAnomalyDescription(description, t),
    localizeRecommendation: (recommendation: string, metric: string, type: string, severity: string, t: TFunction) => 
      localizeAnomalyRecommendation(recommendation, metric, type, severity, t),
  };
};
