/**
 * Overview页面相关的TypeScript类型定义
 */

// TOP排名数据项
export interface TopServerItem {
  rank: number;
  serverName: string;
  callCount: number;
  percentage: number;
  trend: 'up' | 'down' | 'stable';
  trendValue: number;
}

// 工具生态健康度数据
export interface ToolEcosystemData {
  healthScore: number;
  totalTools: number;
  healthyTools: number;
  problemTools: number;
  offlineTools: number;
}

// 工具热度分析数据
export interface ToolHeatmapData {
  hotTools: {
    name: string;
    callCount: number;
    percentage: number;
  }[];
  coldTools: {
    name: string;
    callCount: number;
    percentage: number;
  }[];
  heatMatrix: {
    toolName: string;
    heatLevel: 'hot' | 'warm' | 'cold';
    callFrequency: number;
  }[];
}

// 业务价值指数数据
export interface BusinessValueData {
  overallScore: number;
  highValueTools: number;
  mediumValueTools: number;
  lowValueTools: number;
  valueDistribution: {
    category: string;
    score: number;
    count: number;
  }[];
}

// 工具分类分布数据
export interface ToolCategoryItem {
  category: string;
  count: number;
  percentage: number;
  color: string;
}

// 调用效率分析数据
export interface CallEfficiencyData {
  averageResponseTime: number;
  highEfficiencyTools: number;
  mediumEfficiencyTools: number;
  lowEfficiencyTools: number;
  efficiencyDistribution: {
    range: string;
    count: number;
    percentage: number;
  }[];
  needOptimization: {
    toolName: string;
    avgResponseTime: number;
    callCount: number;
  }[];
}

// 智能洞察数据类型定义
export interface TrendInsight {
  id: string;
  type: 'usage_surge' | 'new_hotspot' | 'growth_trend' | 'performance_boost' | 'adoption_trend';
  title: string;
  description: string;
  serverName: string;
  // 关键数值
  primaryValue: number;
  primaryUnit: string;
  changePercentage: number;
  comparisonPeriod: string;
  // 业务影响
  businessImpact: 'high' | 'medium' | 'low';
  impactDescription: string;
  // 行动建议
  actionRecommendation: string;
  actionUrgency: 'immediate' | 'soon' | 'monitor';
  // 元数据
  priority: 'high' | 'medium' | 'low';
  severity: 'critical' | 'warning' | 'info' | 'success';
  confidence: number;
  metrics: string[];
  timestamp: string;
  dataSource: string;
  actionable: boolean;
}

export interface WarningInsight {
  id: string;
  type: 'no_calls' | 'slow_response' | 'low_success_rate' | 'connection_issue' | 'resource_exhaustion' | 'security_risk';
  title: string;
  description: string;
  serverName: string;
  // 关键数值
  primaryValue: number;
  primaryUnit: string;
  thresholdValue: number;
  exceedPercentage: number;
  // 业务影响
  businessImpact: 'high' | 'medium' | 'low';
  impactDescription: string;
  affectedUsers: number;
  // 行动建议
  actionRecommendation: string;
  actionUrgency: 'immediate' | 'soon' | 'monitor';
  estimatedFixTime: string;
  // 元数据
  severity: 'critical' | 'warning' | 'info';
  actionRequired: boolean;
  confidence: number;
  metrics: string[];
  timestamp: string;
  dataSource: string;
  actionable: boolean;
}

export interface RecommendationInsight {
  id: string;
  category: 'performance' | 'reliability' | 'resource' | 'monitoring' | 'security' | 'cost_optimization';
  title: string;
  description: string;
  // 关键数值
  potentialImprovement: number;
  improvementUnit: string;
  currentValue: number;
  targetValue: number;
  // 业务影响
  businessImpact: 'high' | 'medium' | 'low';
  impactDescription: string;
  expectedROI: string;
  // 实施信息
  implementationSteps: string[];
  estimatedEffort: string;
  estimatedTime: string;
  requiredResources: string[];
  // 元数据
  impact: 'high' | 'medium' | 'low';
  effort: 'low' | 'medium' | 'high';
  actionable: boolean;
  confidence: number;
  metrics: string[];
  timestamp: string;
  dataSource: string;
  severity: 'info' | 'success';
}

export interface InsightsData {
  trends: TrendInsight[];
  warnings: WarningInsight[];
  recommendations: RecommendationInsight[];
  lastUpdated: string;
}

// 保持向后兼容的简化洞察接口
export interface InsightItem {
  type: 'trend' | 'attention' | 'optimization';
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  actionable: boolean;
  timestamp: string;
}

// 元数据
export interface OverviewMetadata {
  lastUpdated: string;
  dataRange: {
    startTime: string;
    endTime: string;
  };
  totalCalls: number;
  totalServers: number;
}

// 完整的概览数据接口
export interface OverviewData {
  topServers: TopServerItem[];
  toolEcosystem: ToolEcosystemData;
  toolHeatmap: ToolHeatmapData;
  businessValue: BusinessValueData;
  toolCategories: ToolCategoryItem[];
  callEfficiency: CallEfficiencyData;
  insights: InsightsData;
  metadata: OverviewMetadata;
}

// KPI卡片基础Props
export interface KPICardProps {
  title: string;
  loading?: boolean;
  error?: string | null;
  className?: string;
  children: React.ReactNode;
}

// 状态指示器Props
export interface StatusIndicatorProps {
  status: 'healthy' | 'warning' | 'critical';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

// 趋势指示器Props
export interface TrendIndicatorProps {
  trend: 'up' | 'down' | 'stable';
  value: number;
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  className?: string;
}

// 图表通用Props
export interface ChartProps {
  width?: number;
  height?: number;
  className?: string;
}

// 环形进度图Props
export interface CircularProgressProps extends ChartProps {
  value: number;
  max?: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  showLabel?: boolean;
  label?: string;
}

// 热度矩阵图Props
export interface HeatmapMatrixProps extends ChartProps {
  data: {
    toolName: string;
    heatLevel: 'hot' | 'warm' | 'cold';
    callFrequency: number;
  }[];
  maxItems?: number;
}

// 价值分布条形图Props
export interface ValueDistributionChartProps extends ChartProps {
  data: {
    category: string;
    score: number;
    count: number;
  }[];
}

// 饼图Props
export interface PieChartProps extends ChartProps {
  data: {
    category: string;
    count: number;
    percentage: number;
    color: string;
  }[];
  size?: number;
  showLegend?: boolean;
}

// 效率分布图Props
export interface EfficiencyChartProps extends ChartProps {
  data: {
    range: string;
    count: number;
    percentage: number;
  }[];
}

// 指标卡片Props
export interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  trend?: {
    direction: 'up' | 'down' | 'stable';
    value: number;
  };
  color?: 'blue' | 'green' | 'purple' | 'orange';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

// 进度条Props
export interface ProgressBarProps {
  value: number;
  max?: number;
  height?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'gradient';
  showLabel?: boolean;
  label?: string;
  className?: string;
  animated?: boolean;
}

// 服务器排名项Props
export interface ServerRankingItemProps {
  server: TopServerItem;
  maxCallCount: number;
  showProgress?: boolean;
  showTrend?: boolean;
  showPercentage?: boolean;
  className?: string;
}
