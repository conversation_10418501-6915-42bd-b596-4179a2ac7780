{"app": {"title": "MCP Publishing Platform", "error": "Error", "closeButton": "Close", "noServers": "No MCP servers available", "loading": "Loading...", "logout": "Logout", "profile": "Profile", "changePassword": "Change Password", "toggleSidebar": "Toggle Sidebar", "welcomeUser": "Welcome, {{username}}", "name": "MCP Hub"}, "about": {"title": "About", "versionInfo": "MCP Hub Version: {{version}}", "newVersion": "New version available!", "currentVersion": "Current version", "newVersionAvailable": "New version {{version}} is available", "viewOnGitHub": "View on GitHub", "checkForUpdates": "Check for Updates", "checking": "Checking for updates..."}, "profile": {"viewProfile": "View profile", "userCenter": "User Center"}, "sponsor": {"label": "Sponsor", "title": "Support the Project", "rewardAlt": "Reward QR Code", "supportMessage": "Support the development of MCP Hub by buying me a coffee!", "supportButton": "Support on Ko-fi"}, "wechat": {"label": "WeChat", "title": "Connect via WeChat", "qrCodeAlt": "WeChat QR Code", "scanMessage": "Scan this QR code to connect with us on WeChat"}, "discord": {"label": "Discord", "title": "Join our Discord server", "community": "Join our growing community on Discord for support, discussions, and updates!"}, "theme": {"title": "Theme", "light": "Light", "dark": "Dark", "system": "System"}, "auth": {"login": "<PERSON><PERSON>", "loginTitle": "<PERSON>gin to MCP Hub", "username": "Username", "password": "Password", "loggingIn": "Logging in...", "emptyFields": "Username and password cannot be empty", "loginFailed": "<PERSON><PERSON> failed, please check your username and password", "loginError": "An error occurred during login", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "passwordsNotMatch": "New password and confirmation do not match", "changePasswordSuccess": "Password changed successfully", "changePasswordError": "Failed to change password", "changePassword": "Change Password", "passwordChanged": "Password changed successfully", "passwordChangeError": "Failed to change password"}, "server": {"addServer": "Add Server", "add": "Add", "edit": "Edit", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this server?", "deleteWarning": "Deleting server '{{name}}' will remove it and all its data. This action cannot be undone.", "status": "Status", "tools": "Tools", "name": "Server Name", "url": "Server URL", "apiKey": "API Key", "save": "Save", "cancel": "Cancel", "invalidConfig": "Could not find configuration data for {{serverName}}", "addError": "Failed to add server", "editError": "Failed to edit server {{serverName}}", "deleteError": "Failed to delete server {{serverName}}", "updateError": "Failed to update server", "editTitle": "Edit Server: {{serverName}}", "type": "Server Type", "command": "Command", "arguments": "Arguments", "envVars": "Environment Variables", "headers": "HTTP Headers", "key": "key", "value": "value", "enabled": "Enabled", "apiCalls": "API Calls", "callCount": "{{count}} calls", "neverCalled": "Never called", "justNow": "Just now", "minutesAgo": "{{minutes}}m ago", "hoursAgo": "{{hours}}h ago", "daysAgo": "{{days}}d ago", "successRate": "{{rate}}% success", "enable": "Enable", "disable": "Disable", "requestOptions": "Configuration", "timeout": "Request Timeout", "timeoutDescription": "Timeout for requests to the MCP server (ms)", "maxTotalTimeout": "Maximum Total Timeout", "maxTotalTimeoutDescription": "Maximum total timeout for requests sent to the MCP server (ms) (Use with progress notifications)", "resetTimeoutOnProgress": "Reset Timeout on Progress", "resetTimeoutOnProgressDescription": "Reset timeout on progress notifications", "remove": "Remove", "toggleError": "Failed to toggle server {{serverName}}", "alreadyExists": "Server {{server<PERSON>ame}} already exists", "invalidData": "Invalid server data provided", "notFound": "Server {{serverName}} not found", "namePlaceholder": "Enter server name", "urlPlaceholder": "Enter server URL", "commandPlaceholder": "Enter command", "argumentsPlaceholder": "Enter arguments", "errorDetails": "<PERSON><PERSON><PERSON>", "viewErrorDetails": "View error details", "confirmVariables": "Confirm Variable Configuration", "variablesDetected": "Variables detected in configuration. Please confirm these variables are properly configured:", "detectedVariables": "Detected Variables", "confirmVariablesMessage": "Please ensure these variables are properly defined in your runtime environment. Continue adding server?", "confirmAndAdd": "Confirm and Add", "openapi": {"inputMode": "Input Mode", "inputModeUrl": "Specification URL", "inputModeSchema": "JSON Schema", "specUrl": "OpenAPI Specification URL", "schema": "OpenAPI JSON Schema", "schemaHelp": "Paste your complete OpenAPI JSON schema here", "security": "Security Type", "securityNone": "None", "securityApiKey": "API Key", "securityHttp": "HTTP Authentication", "securityOAuth2": "OAuth 2.0", "securityOpenIdConnect": "OpenID Connect", "apiKeyConfig": "API Key Configuration", "apiKeyName": "Header/Parameter Name", "apiKeyIn": "Location", "apiKeyValue": "API Key Value", "httpAuthConfig": "HTTP Authentication Configuration", "httpScheme": "Authentication Scheme", "httpCredentials": "Credentials", "oauth2Config": "OAuth 2.0 Configuration", "oauth2Token": "Access Token", "openIdConnectConfig": "OpenID Connect Configuration", "openIdConnectUrl": "Discovery URL", "openIdConnectToken": "ID Token"}}, "status": {"online": "Online", "offline": "Offline", "connecting": "Connecting"}, "errors": {"general": "Something went wrong", "network": "Network connection error. Please check your internet connection", "serverConnection": "Unable to connect to the server. Please check if the server is running", "serverAdd": "Failed to add server. Please check the server status", "serverUpdate": "Failed to edit server {{serverName}}. Please check the server status", "serverFetch": "Failed to retrieve server data. Please try again later", "initialStartup": "The server might be starting up. Please wait a moment as this process can take some time on first launch...", "serverInstall": "Failed to install server", "failedToFetchSettings": "Failed to fetch settings", "failedToUpdateRouteConfig": "Failed to update route configuration", "failedToUpdateSmartRoutingConfig": "Failed to update smart routing configuration", "timeout": "Request timeout. Please try again", "business": "Operation failed. Please check your input and try again", "unknown": "An unknown error occurred", "retrying": "Retrying... ({{current}}/{{max}})"}, "common": {"processing": "Processing...", "save": "Save", "cancel": "Cancel", "refresh": "Refresh", "create": "Create", "submitting": "Submitting...", "delete": "Delete", "remove": "Remove", "copy": "Copy", "copySuccess": "Copied to clipboard", "copyFailed": "Co<PERSON> failed", "close": "Close", "confirm": "Confirm", "loading": "Loading...", "hideAdvanced": "Hide Advanced"}, "nav": {"dashboard": "Dashboard", "servers": "Servers", "groups": "Groups", "settings": "Settings", "changePassword": "Change Password", "market": "Market", "logs": "Logs", "analytics": "Analytics", "monitoring": "Monitoring"}, "pages": {"dashboard": {"title": "Dashboard", "totalServers": "Total", "onlineServers": "Online", "offlineServers": "Offline", "connectingServers": "Connecting", "recentServers": "Recent Servers", "allServers": "All Servers", "showingServers": "Showing {{start}}-{{end}} of {{total}} servers", "analytics24h": "Last 24 Hours Analytics", "totalCalls24h": "Total API Calls", "successRate24h": "Success Rate", "avgResponseTime24h": "Avg Response Time", "analyticsDescription": "This feature displays key performance metrics for the past 24 hours, helping you monitor API usage, identify performance trends, and detect potential issues early.", "trendChangeRate": "Change rate compared to previous 24 hours", "trendIncreasing": "Increasing trend", "trendDecreasing": "Decreasing trend", "trendStable": "Stable trend", "changeRateLabel": "vs 24h ago"}, "servers": {"title": "Servers Management"}, "groups": {"title": "Group Management"}, "settings": {"title": "Settings", "language": "Language", "account": "Account <PERSON><PERSON>", "password": "Change Password", "appearance": "Appearance", "routeConfig": "Security", "installConfig": "Installation", "smartRouting": "Smart Routing"}, "market": {"title": "Server Market - (Data from mcpm.sh)"}, "logs": {"title": "System Logs"}, "analytics": {"title": "Analytics", "overview": "Overview", "history": "History", "trends": "Trends", "monitoring": "Monitoring"}}, "analytics": {"filters": "Filters", "startTime": "Start Time", "endTime": "End Time", "status": "Status", "allStatus": "All Status", "success": "Success", "failed": "Failed", "search": "Search", "reset": "Reset", "results": "Results", "pageSize": "<PERSON>", "showingResults": "Showing {{start}}-{{end}} of {{total}} results", "timestamp": "Timestamp", "server": "Server", "tool": "Tool", "responseTime": "Response Time", "clientIp": "Client IP", "error": "Error", "noData": "No data available", "noDataDescription": "There is no data to display for the selected criteria.", "errorDescription": "An error occurred while loading the data. Please try again.", "loadingDescription": "Please wait while we load your data.", "retryButton": "Retry", "insufficientData": "Insufficient data", "insufficientDataDescription": "At least {{minPoints}} data points are required for analysis, but only {{currentPoints}} are available.", "exportFailed": "Export failed", "serverFilter": "Server Filter", "toolFilter": "Tool Filter", "selectServer": "Select Server", "selectTool": "Select Tool", "allServers": "All Servers", "allTools": "All Tools", "exportCSV": "Export CSV", "exportExcel": "Export Excel", "exporting": "Exporting...", "timeRange": "Time Range", "granularity": "Granularity", "refresh": "Refresh", "noTrendsData": "No trends data available", "noTrendsDataDesc": "Try adjusting the time range or check back later.", "timeRangeOptions": {"1h": "Last Hour", "24h": "Last 24 Hours", "7d": "Last 7 Days", "30d": "Last 30 Days"}, "granularityOptions": {"hour": "Hourly", "day": "Daily", "week": "Weekly"}, "metricsOptions": {"calls": "API Calls", "successRate": "Success Rate", "responseTime": "Response Time"}, "metrics": {"call_count": "Call Count", "growth_rate": "Growth Rate", "tool_usage": "Tool Usage", "popularity": "Popularity", "health_score": "Health Score", "ecosystem_status": "Ecosystem Status", "response_time": "Response Time", "performance": "Performance", "tool_performance": "Tool Performance", "tool_health": "Tool Health", "availability": "Availability", "efficiency": "Efficiency", "load_distribution": "Load Distribution", "reliability": "Reliability", "health_monitoring": "Health Monitoring", "system_stability": "System Stability"}, "noAnomalies": "No anomalies detected", "anomaliesDetected": "Anomalies Detected", "viewAll": "View All", "andMore": "And {{count}} more...", "insightsTitle": "Insights", "insights": {"dashboard": "Insights Dashboard", "allInsights": "All Insights", "viewAll": "View All", "noInsights": "No insights available", "searchPlaceholder": "Search insights...", "details": "Insight Details", "type": {"trend": "Trend", "anomaly": "Anomaly", "seasonality": "Seasonality", "correlation": "Correlation", "forecast": "Forecast", "recommendation": "Recommendation"}, "severity": {"critical": "Critical", "warning": "Warning", "success": "Success", "info": "Info"}, "metrics": "Related Metrics", "confidence": "Confidence", "timestamp": "Generated", "timeRange": "Time Range", "recommendation": "Recommendation", "critical": "Critical", "warning": "Warning", "actionable": "Actionable", "topInsights": "Key Insights", "businessImpact": "Business Impact", "impact": {"high": "High", "medium": "Medium", "low": "Low"}, "urgency": {"immediate": "Immediate", "soon": "Soon", "monitor": "Monitor"}, "viewDetails": "View Details", "total": "Total", "typeDistribution": "Insight Types"}, "dateRange": {"quickSelect": "Quick Select", "last1h": "Last 1 Hour", "last24h": "Last 24 Hours", "last7d": "Last 7 Days", "last30d": "Last 30 Days", "today": "Today", "yesterday": "Yesterday", "now": "Now", "setToNow": "Set to current time"}, "itemsSelected": "items selected", "overview": {"description": "Comprehensive overview of your MCP ecosystem", "lastUpdated": "Last updated", "topServers": "TOP Servers", "topActiveServers": "Most Active Servers Ranking", "timeRange": "Past 24 hours data", "top5": "TOP 5", "totalCalls": "Total Calls", "activeServers": "Active Servers", "serversUnit": "servers", "top5Share": "TOP5 Share", "ofTotalCalls": "of total calls", "noServerData": "No server data available", "noData": "No overview data available", "avgResponseTime": "Average Response Time", "insights": "Smart Insights", "viewAllInsights": "View All Insights", "noInsights": "No important insights", "noInsightsDesc": "System is running normally, no issues found that require attention", "insightsError": "Failed to load insights data", "overallScore": "Overall Score", "toolEcosystem": "Tool Ecosystem Health", "viewFullRanking": "View Full Ranking", "totalTools": "Total Tools", "healthyTools": "Healthy Tools", "problemTools": "Problem Tools", "offlineTools": "Offline Tools", "healthDistribution": "Health Distribution", "toolHeatmap": "Tool Heatmap", "hotTools": "Hot Tools", "coldTools": "Cold Tools", "heatDistribution": "Heat Distribution", "hotToolsCalls": "Hot Calls", "hotLevel": "Hot", "warmLevel": "Warm", "coldLevel": "Cold", "toolName": "Tool Name", "callCount": "Call Count", "ofTotal": "of total", "calls": "calls", "noToolData": "No tool data", "noToolDataDesc": "Please try again later or check data source", "showingTopTools": "Showing top {{count}} tools", "totalToolsAvailable": "{{total}} tools available", "businessValue": {"title": "Business Value Index", "subtitle": "Tool Value Analysis", "overallScore": "Overall Score", "grade": "Grade", "totalTools": "Total Tools", "highValueRatio": "High Value Ratio", "highValue": "High Value", "mediumValue": "Medium Value", "lowValue": "Low Value", "excellent": "Excellent", "good": "Good", "average": "Average", "poor": "Poor", "critical": "Critical", "categoryDistribution": "Category Value Distribution", "averageScore": "Average Score", "totalCategories": "Total Categories", "score": "Score", "toolCount": "Tool Count", "valueLevel": "Value Level", "highValueCategories": "High Value Categories", "highValueTools": "High Value Tools", "mediumValueTools": "Medium Value Tools", "lowValueTools": "Low Value Tools"}, "toolCategories": {"title": "Tool Categories", "subtitle": "Functional Type Analysis", "totalTools": "Total Tools", "totalCategories": "Categories", "largestCategory": "Largest Category", "diversityIndex": "Diversity Index", "distribution": "Category Distribution", "categories": "Categories", "categoryNames": {"data": "Data Processing", "web": "Web Tools", "file": "File Operations", "api": "API Interface", "database": "Database", "ai": "Artificial Intelligence", "utility": "Utilities", "development": "Development Tools", "communication": "Communication", "automation": "Automation", "security": "Security Tools", "monitoring": "Monitoring", "analytics": "Analytics", "content": "Content Management", "system": "System Tools", "数据获取": "Data Acquisition", "开发工具": "Development Tools", "通信协作": "Communication & Collaboration", "其他工具": "Other Tools", "分析处理": "Analysis & Processing"}}, "callEfficiency": "Call Efficiency", "problemToolsAlert": "Found {{count}} tools that need attention"}, "searchOptions": "Search options...", "selectAll": "Select All", "deselectAll": "Deselect All", "clearAll": "Clear All", "noOptionsFound": "No options found", "noOptionsAvailable": "No options available", "savedFilters": "Saved Filters", "saveCurrentFilters": "Save Current", "saveFilterPreset": "Save Filter Preset", "presetName": "Preset Name", "apply": "Apply", "export": "Export", "import": "Import", "importError": "Import error. Invalid file format.", "noSavedFilters": "No saved filters yet", "advancedFilters": "Advanced Filters", "userIdFilter": "User ID Filter", "selectUser": "Select User", "chart": {"noData": "No data available", "noDataDescription": "No data points are available for the selected time range.", "instructions": "Scroll to zoom, drag to pan", "reset": "Reset View", "zoom": "Zoom", "pointDetails": "Data Point Details", "timestamp": "Timestamp"}, "anomaly": {"description": "Anomaly Description", "low": "Low", "medium": "Medium", "high": "High", "detailTitle": "Anomaly Details", "historyTitle": "Anomaly History", "viewHistory": "View Anomaly History", "timestamp": "Timestamp", "type": {"spike": "Spike", "drop": "Drop", "trend_break": "Trend Break", "outlier": "Outlier", "volatility": "Volatility"}, "severity": "Severity", "category": {"performance": "Performance", "error": "Error", "usage": "Usage", "system": "System"}, "duration": "Duration", "dataPoints": "data points", "values": "Values", "actualValue": "Actual", "expectedValue": "Expected", "deviation": "Deviation", "confidence": "Confidence", "filterType": "Type", "filterSeverity": "Severity", "filterMetric": "Metric", "filterByType": "Filter by Type", "showing": "Showing {{count}} of {{total}} anomalies", "noAnomalies": "No anomalies found", "total": "Total", "andMore": "And {{count}} more...", "viewAll": "View All", "descriptions": {"spike": "Spike detected: {{value}} above expected", "drop": "Drop detected: {{value}} below expected", "significantSpike": "Significant spike: {{value}} above expected ({{percent}}% increase)", "significantDrop": "Significant drop: {{value}} below expected ({{percent}}% decrease)", "volatility": "Volatility detected: {{percent}}% deviation from recent trend", "trendBreak": "Trend break detected: {{value}} deviation from expected pattern", "trendBreakDirection": "Trend break: direction changed from {{from}} to {{to}}", "directionUp": "up", "directionDown": "down"}, "recommendations": {"callsDropHigh": "Investigate potential service outages or connectivity issues causing drops in API calls.", "callsSpikeHigh": "Check for unusual traffic patterns or potential DDoS attacks causing spikes in API calls.", "successRateDropHigh": "Urgently investigate service errors causing drops in success rate.", "responseTimeSpikeHigh": "Check for performance bottlenecks causing spikes in response time.", "general": "Monitor related metrics and investigate the cause of anomalies."}}}, "logs": {"filters": "Filters", "search": "Search logs...", "autoScroll": "Auto-scroll", "clearLogs": "Clear logs", "loading": "Loading logs...", "noLogs": "No logs available.", "noMatch": "No logs match the current filters.", "mainProcess": "Main Process", "childProcess": "Child Process", "main": "Main", "child": "Child"}, "groups": {"add": "Add", "addNew": "Add New Group", "edit": "Edit Group", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this group?", "deleteWarning": "Deleting group '{{name}}' will remove it and all its server associations. This action cannot be undone.", "name": "Group Name", "namePlaceholder": "Enter group name", "nameRequired": "Group name is required", "description": "Description", "descriptionPlaceholder": "Enter group description (optional)", "createError": "Failed to create group", "updateError": "Failed to update group", "deleteError": "Failed to delete group", "serverAddError": "Failed to add server to group", "serverRemoveError": "Failed to remove server from group", "addServer": "Add Server to Group", "selectServer": "Select a server to add", "servers": "Servers in Group", "remove": "Remove", "noGroups": "No groups available. Create a new group to get started.", "noServers": "No servers in this group.", "noServerOptions": "No servers available", "serverCount": "{{count}} Servers", "bearerAuthKey": "Bearer Authentication Key", "bearerAuthKeyPlaceholder": "Enter Bearer authentication key (optional)", "bearerAuthKeyDescription": "Set an independent Bearer authentication key for this group, which will take priority over global configuration", "bearerAuthKeyInvalid": "Invalid Bearer key format. Must be 8-256 characters, containing only letters, numbers, hyphens, underscores, and dots", "showBearerKey": "Show Key", "hideBearerKey": "Hide Key", "bearerAuthEnabled": "Bearer Authentication Enabled", "bearerAuthDisabled": "Using Global Authentication", "bearerAuthTooltip": "This group has its own Bearer authentication key configured", "generateSecureKey": "Generate Secure Key", "generateRandomKey": "Generate Random Key", "keyGenerated": "New secure key generated", "keyGenerationFailed": "Key generation failed"}, "market": {"title": "Server Market", "official": "Official", "by": "By", "unknown": "Unknown", "tools": "tools", "search": "Search", "searchPlaceholder": "Search for servers by name, category, or tags", "clearFilters": "Clear", "clearCategoryFilter": "", "clearTagFilter": "", "categories": "Categories", "tags": "Tags", "showTags": "Show tags", "hideTags": "Hide tags", "moreTags": "", "noServers": "No servers found matching your search", "backToList": "Back to list", "install": "Install", "installing": "Installing...", "installed": "Installed", "installServer": "Install Server: {{name}}", "installSuccess": "Server {{serverName}} installed successfully", "author": "Author", "license": "License", "repository": "Repository", "examples": "Examples", "arguments": "Arguments", "argumentName": "Name", "description": "Description", "required": "Required", "example": "Example", "viewSchema": "View schema", "fetchError": "Error fetching market servers", "serverNotFound": "Server not found", "searchError": "Error searching servers", "filterError": "Error filtering servers by category", "tagFilterError": "Error filtering servers by tag", "noInstallationMethod": "No installation method available for this server", "showing": "Showing {{from}}-{{to}} of {{total}} servers", "perPage": "Per page", "confirmVariablesMessage": "Please ensure these variables are properly defined in your runtime environment. Continue installing server?", "confirmAndInstall": "Confirm and Install"}, "pagination": {"previous": "Previous", "next": "Next", "page": "Page {{page}}", "of": "of {{total}}", "showing": "Showing {{start}}-{{end}} of {{total}}", "noData": "No data available", "itemsPerPage": "Items per page", "items": "items", "jumpToPage": "Jump to page", "go": "Go"}, "tool": {"run": "Run", "running": "Running...", "runTool": "<PERSON>", "cancel": "Cancel", "noDescription": "No description available", "inputSchema": "Input Schema:", "runToolWithName": "Run Tool: {{name}}", "execution": "Tool Execution", "successful": "Successful", "failed": "Failed", "result": "Result:", "error": "Error", "errorDetails": "Error Details:", "noContent": "<PERSON>l executed successfully but returned no content.", "unknownError": "Unknown error occurred", "jsonResponse": "JSON Response:", "toolResult": "Tool result", "noParameters": "This tool does not require any parameters.", "selectOption": "Select an option", "enterValue": "Enter {{type}} value", "enabled": "Enabled", "enableSuccess": "Tool {{name}} enabled successfully", "disableSuccess": "Tool {{name}} disabled successfully", "toggleFailed": "Failed to toggle tool status", "parameters": "Tool Parameters", "formMode": "Form Mode", "jsonMode": "JSON Mode", "jsonConfiguration": "JSON Configuration", "invalidJsonFormat": "Invalid JSON format", "fixJsonBeforeSwitching": "Please fix JSON format before switching to form mode", "item": "Item {{index}}", "addItem": "Add {{key}} item", "enterKey": "Enter {{key}}"}, "settings": {"enableGlobalRoute": "Enable Global Route", "enableGlobalRouteDescription": "Allow connections to /sse endpoint without specifying a group ID", "enableGroupNameRoute": "Enable Group Name Route", "enableGroupNameRouteDescription": "Allow connections to /sse endpoint using group names instead of just group IDs", "enableBearerAuth": "Enable Bearer Authentication", "enableBearerAuthDescription": "Require bearer token authentication for MCP requests", "bearerAuthKey": "Bearer Authentication Key", "bearerAuthKeyDescription": "The authentication key that will be required in the <PERSON><PERSON> token", "bearerAuthKeyPlaceholder": "Enter bearer authentication key", "skipAuth": "<PERSON><PERSON> Au<PERSON>nti<PERSON>", "skipAuthDescription": "Bypass login requirement for frontend and API access (DEFAULT OFF for security)", "pythonIndexUrl": "Python Package Repository URL", "pythonIndexUrlDescription": "Set UV_DEFAULT_INDEX environment variable for Python package installation", "pythonIndexUrlPlaceholder": "e.g. https://pypi.org/simple", "npmRegistry": "NPM Registry URL", "npmRegistryDescription": "Set npm_config_registry environment variable for NPM package installation", "npmRegistryPlaceholder": "e.g. https://registry.npmjs.org/", "installConfig": "Installation", "systemConfigUpdated": "System configuration updated successfully", "enableSmartRouting": "Enable Smart Routing", "enableSmartRoutingDescription": "Enable smart routing feature to search the most suitable tool based on input (using $smart group name)", "dbUrl": "PostgreSQL URL (requires pgvector support)", "dbUrlPlaceholder": "e.g. postgresql://user:password@localhost:5432/dbname", "openaiApiBaseUrl": "OpenAI API Base URL", "openaiApiBaseUrlPlaceholder": "https://api.openai.com/v1", "openaiApiKey": "OpenAI API Key", "openaiApiKeyPlaceholder": "Enter OpenAI API key", "openaiApiEmbeddingModel": "OpenAI Embedding Model", "openaiApiEmbeddingModelPlaceholder": "text-embedding-3-small", "smartRoutingConfigUpdated": "Smart routing configuration updated successfully", "smartRoutingRequiredFields": "Database URL and OpenAI API Key are required to enable smart routing", "smartRoutingValidationError": "Please fill in the required fields before enabling Smart Routing: {{fields}}"}, "dxt": {"upload": "Upload", "uploadTitle": "Upload DXT Extension", "dropFileHere": "Drop your .dxt file here", "orClickToSelect": "or click to select from your computer", "invalidFileType": "Please select a valid .dxt file", "noFileSelected": "Please select a .dxt file to upload", "uploading": "Uploading...", "uploadFailed": "Failed to upload DXT file", "installServer": "Install MCP Server from DXT", "extensionInfo": "Extension Information", "name": "Name", "version": "Version", "description": "Description", "author": "Author", "tools": "Tools", "serverName": "Server Name", "serverNamePlaceholder": "Enter a name for this server", "install": "Install", "installing": "Installing...", "installFailed": "Failed to install server from DXT", "serverExistsTitle": "Server Already Exists", "serverExistsConfirm": "Server '{{serverName}}' already exists. Do you want to override it with the new version?", "override": "Override"}, "monitoring": {"title": "System Monitoring", "systemHealth": "System Health", "performanceMetrics": "Performance Metrics", "systemStatus": "System Status", "systemInfo": "System Information", "database": "Database", "mcpServers": "MCP Servers", "cache": "<PERSON><PERSON>", "servers": "Servers", "performance": "Performance", "lastCheck": "Last check", "healthError": "Failed to load health data", "metricsError": "Failed to load metrics data", "statusError": "Failed to load status data", "cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "cores": "cores", "connections": "Connections", "responseTime": "Response Time", "uptime": "Uptime", "version": "Version", "running": "Running", "total": "Total", "totalCalls": "Total Calls", "successRate": "Success Rate", "connected": "Connected", "connecting": "Connecting", "reconnecting": "Reconnecting", "disconnected": "Disconnected", "realtimeOn": "Real-time ON", "realtimeOff": "Real-time OFF", "autoRefreshOn": "Auto Refresh ON", "autoRefreshOff": "Auto Refresh OFF", "lastUpdate": "Last updated", "error": "Monitoring Error", "status": {"healthy": "Healthy", "warning": "Warning", "critical": "Critical", "up": "Up", "down": "Down"}, "units": {"ms": "ms", "percent": "%", "bytes": "bytes"}}, "format": {"locale": "en-US", "currency": "USD", "dateTime": {"short": "MM/dd HH:mm", "medium": "MM/dd/yyyy HH:mm:ss", "long": "MMMM dd, yyyy HH:mm:ss"}, "number": {"decimal": 2, "thousand": ",", "decimalPoint": "."}}}