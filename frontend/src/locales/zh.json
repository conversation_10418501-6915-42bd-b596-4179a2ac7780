{"app": {"title": "MCP发布平台", "error": "错误", "closeButton": "关闭", "noServers": "没有可用的 MCP 服务器", "loading": "加载中...", "logout": "退出登录", "profile": "个人资料", "changePassword": "修改密码", "toggleSidebar": "切换侧边栏", "welcomeUser": "欢迎, {{username}}", "name": "MCP Hub"}, "about": {"title": "关于", "versionInfo": "MCP Hub 版本: {{version}}", "newVersion": "有新版本可用！", "currentVersion": "当前版本", "newVersionAvailable": "新版本 {{version}} 已发布", "viewOnGitHub": "在 GitHub 上查看", "checkForUpdates": "检查更新", "checking": "检查更新中..."}, "profile": {"viewProfile": "查看个人中心", "userCenter": "个人中心"}, "sponsor": {"label": "赞助", "title": "支持项目", "rewardAlt": "赞赏码", "supportMessage": "通过捐赠支持 MCP Hub 的开发！", "supportButton": "在 Ko-fi 上支持"}, "wechat": {"label": "微信", "title": "微信联系", "qrCodeAlt": "微信二维码", "scanMessage": "扫描二维码添加微信"}, "discord": {"label": "Discord", "title": "加入我们的 Discord 服务器", "community": "加入我们不断壮大的 Discord 社区，获取支持、参与讨论并了解最新动态！"}, "theme": {"title": "主题", "light": "浅色", "dark": "深色", "system": "系统"}, "auth": {"login": "登录", "loginTitle": "登录 MCP Hub", "username": "用户名", "password": "密码", "loggingIn": "登录中...", "emptyFields": "用户名和密码不能为空", "loginFailed": "登录失败，请检查用户名和密码", "loginError": "登录过程中出现错误", "currentPassword": "当前密码", "newPassword": "新密码", "confirmPassword": "确认密码", "passwordsNotMatch": "新密码与确认密码不一致", "changePasswordSuccess": "密码修改成功", "changePasswordError": "修改密码失败", "changePassword": "修改密码", "passwordChanged": "密码修改成功", "passwordChangeError": "修改密码失败"}, "server": {"addServer": "添加服务器", "add": "添加", "edit": "编辑", "delete": "删除", "confirmDelete": "您确定要删除此服务器吗？", "deleteWarning": "删除服务器 '{{name}}' 将会移除该服务器及其所有数据。此操作无法撤销。", "status": "状态", "tools": "工具", "name": "服务器名称", "url": "服务器 URL", "apiKey": "API 密钥", "save": "保存", "cancel": "取消", "addError": "添加服务器失败", "editError": "编辑服务器 {{serverName}} 失败", "invalidConfig": "无法找到 {{serverName}} 的配置数据", "deleteError": "删除服务器 {{serverName}} 失败", "updateError": "更新服务器失败", "editTitle": "编辑服务器: {{serverName}}", "type": "服务器类型", "command": "命令", "arguments": "参数", "envVars": "环境变量", "headers": "HTTP 请求头", "key": "键", "value": "值", "enabled": "已启用", "apiCalls": "API调用", "callCount": "{{count}} 次调用", "neverCalled": "从未调用", "justNow": "刚刚", "minutesAgo": "{{minutes}}分钟前", "hoursAgo": "{{hours}}小时前", "daysAgo": "{{days}}天前", "successRate": "成功率 {{rate}}%", "enable": "启用", "disable": "禁用", "requestOptions": "配置", "timeout": "请求超时", "timeoutDescription": "请求超时时间（毫秒）", "maxTotalTimeout": "最大总超时", "maxTotalTimeoutDescription": "无论是否有进度通知的最大总超时时间（毫秒）", "resetTimeoutOnProgress": "收到进度通知时重置超时", "resetTimeoutOnProgressDescription": "适用于发送周期性进度更新的长时间运行操作", "remove": "移除", "toggleError": "切换服务器 {{serverName}} 状态失败", "alreadyExists": "服务器 {{serverName}} 已经存在", "invalidData": "提供的服务器数据无效", "notFound": "找不到服务器 {{serverName}}", "namePlaceholder": "请输入服务器名称", "urlPlaceholder": "请输入服务器URL", "commandPlaceholder": "请输入命令", "argumentsPlaceholder": "请输入参数", "errorDetails": "错误详情", "viewErrorDetails": "查看错误详情", "confirmVariables": "确认变量配置", "variablesDetected": "检测到配置中包含变量，请确认这些变量是否已正确配置：", "detectedVariables": "检测到的变量", "confirmVariablesMessage": "请确保这些变量在运行环境中已正确定义。是否继续添加服务器？", "confirmAndAdd": "确认并添加", "openapi": {"inputMode": "输入模式", "inputModeUrl": "规范 URL", "inputModeSchema": "JSON 模式", "specUrl": "OpenAPI 规范 URL", "schema": "OpenAPI JSON 模式", "schemaHelp": "请在此处粘贴完整的 OpenAPI JSON 模式", "security": "安全类型", "securityNone": "无", "securityApiKey": "API 密钥", "securityHttp": "HTTP 认证", "securityOAuth2": "OAuth 2.0", "securityOpenIdConnect": "OpenID Connect", "apiKeyConfig": "API 密钥配置", "apiKeyName": "请求头/参数名称", "apiKeyIn": "位置", "apiKeyValue": "API 密钥值", "httpAuthConfig": "HTTP 认证配置", "httpScheme": "认证方案", "httpCredentials": "凭据", "oauth2Config": "OAuth 2.0 配置", "oauth2Token": "访问令牌", "openIdConnectConfig": "OpenID Connect 配置", "openIdConnectUrl": "发现 URL", "openIdConnectToken": "ID 令牌"}}, "status": {"online": "在线", "offline": "离线", "connecting": "连接中"}, "errors": {"general": "发生错误", "network": "网络连接错误，请检查您的互联网连接", "serverConnection": "无法连接到服务器，请检查服务器是否正在运行", "serverAdd": "添加服务器失败，请检查服务器状态", "serverUpdate": "编辑服务器 {{serverName}} 失败，请检查服务器状态", "serverFetch": "获取服务器数据失败，请稍后重试", "initialStartup": "服务器可能正在启动中。首次启动可能需要一些时间，请耐心等候...", "serverInstall": "安装服务器失败", "failedToFetchSettings": "获取设置失败", "failedToUpdateSystemConfig": "更新系统配置失败", "failedToUpdateRouteConfig": "更新路由配置失败", "failedToUpdateSmartRoutingConfig": "更新智能路由配置失败", "timeout": "请求超时，请重试", "business": "操作失败，请检查输入并重试", "unknown": "发生未知错误", "retrying": "正在重试... ({{current}}/{{max}})"}, "common": {"processing": "处理中...", "save": "保存", "cancel": "取消", "refresh": "刷新", "create": "创建", "submitting": "提交中...", "delete": "删除", "remove": "移除", "copy": "复制", "copySuccess": "已复制到剪贴板", "copyFailed": "复制失败", "close": "关闭", "confirm": "确认", "loading": "加载中...", "hideAdvanced": "隐藏高级选项"}, "nav": {"dashboard": "仪表盘", "servers": "服务器", "settings": "设置", "changePassword": "修改密码", "groups": "分组", "market": "市场", "logs": "日志", "analytics": "数据分析", "monitoring": "系统监控"}, "pages": {"dashboard": {"title": "仪表盘", "totalServers": "总数", "onlineServers": "在线", "offlineServers": "离线", "connectingServers": "连接中", "recentServers": "最近的服务器", "allServers": "所有服务器", "showingServers": "显示第 {{start}}-{{end}} 条，共 {{total}} 条服务器", "analytics24h": "最近24小时数据分析", "totalCalls24h": "总API调用", "successRate24h": "成功率", "avgResponseTime24h": "平均响应时间", "analyticsDescription": "此功能展示系统在过去24小时内的关键性能指标，帮助您监控API使用情况、识别性能趋势并及时发现潜在问题。", "trendChangeRate": "相对于前24小时的变化率", "trendIncreasing": "上升趋势", "trendDecreasing": "下降趋势", "trendStable": "稳定趋势", "changeRateLabel": "vs 前24h"}, "servers": {"title": "服务器管理"}, "settings": {"title": "设置", "language": "语言", "account": "账户设置", "password": "修改密码", "appearance": "外观", "routeConfig": "安全配置", "installConfig": "安装", "smartRouting": "智能路由"}, "groups": {"title": "分组管理"}, "market": {"title": "服务器市场 - （数据来源于 mcpm.sh）"}, "logs": {"title": "系统日志"}, "analytics": {"title": "数据分析", "description": "查看和分析您的MCP服务器的分析数据和洞察", "overview": "概览", "history": "历史查询", "trends": "趋势分析", "monitoring": "监控面板"}}, "analytics": {"filters": "筛选条件", "startTime": "开始时间", "endTime": "结束时间", "status": "状态", "allStatus": "全部状态", "success": "成功", "failed": "失败", "search": "搜索", "reset": "重置", "results": "查询结果", "pageSize": "每页显示", "showingResults": "显示第 {{start}}-{{end}} 条，共 {{total}} 条结果", "timestamp": "时间戳", "server": "服务器", "tool": "工具", "responseTime": "响应时间", "clientIp": "客户端IP", "error": "错误信息", "noData": "暂无数据", "noDataDescription": "当前筛选条件下没有可显示的数据。", "errorDescription": "加载数据时发生错误，请重试。", "loadingDescription": "正在加载数据，请稍候...", "retryButton": "重试", "insufficientData": "数据点不足", "insufficientDataDescription": "需要至少 {{minPoints}} 个数据点才能进行分析，当前只有 {{currentPoints}} 个。", "exportFailed": "导出失败", "serverFilter": "服务器筛选", "toolFilter": "工具筛选", "selectServer": "选择服务器", "selectTool": "选择工具", "allServers": "全部服务器", "allTools": "全部工具", "exportCSV": "导出CSV", "exportExcel": "导出Excel", "exporting": "导出中...", "cleanupTestData": "清理测试数据", "cleaningTestData": "清理中...", "overview": {"description": "MCP生态系统的全面概览", "lastUpdated": "最后更新", "topServers": "TOP服务器", "topActiveServers": "最活跃服务器排名", "timeRange": "过去24小时数据", "top5": "前5名", "totalCalls": "总调用次数", "activeServers": "活跃服务器", "serversUnit": "个服务器", "top5Share": "TOP5占比", "ofTotalCalls": "占总调用量", "noServerData": "暂无服务器数据", "noData": "暂无概览数据", "avgResponseTime": "平均响应时间", "insights": "智能洞察", "viewAllInsights": "查看所有洞察", "noInsights": "暂无重要洞察", "noInsightsDesc": "系统运行正常，没有发现需要关注的问题", "insightsError": "洞察数据加载失败", "overallScore": "综合评分", "toolEcosystem": "工具生态健康度", "viewFullRanking": "查看完整排名", "totalTools": "总工具数", "healthyTools": "健康工具", "problemTools": "问题工具", "offlineTools": "离线工具", "healthDistribution": "健康度分布", "toolHeatmap": "工具热度分析", "hotTools": "热门工具", "coldTools": "冷门工具", "heatDistribution": "热度分布", "hotToolsCalls": "热门调用", "hotLevel": "热门", "warmLevel": "温热", "coldLevel": "冷门", "toolName": "工具名称", "callCount": "调用次数", "ofTotal": "占比", "calls": "次调用", "noToolData": "暂无工具数据", "noToolDataDesc": "请稍后再试或检查数据源", "showingTopTools": "显示前 {{count}} 个工具", "totalToolsAvailable": "共 {{total}} 个工具", "businessValue": {"title": "业务价值指数", "subtitle": "工具价值分析", "overallScore": "综合评分", "grade": "等级", "totalTools": "总工具数", "highValueRatio": "高价值占比", "highValue": "高价值", "mediumValue": "中价值", "lowValue": "低价值", "excellent": "优秀", "good": "良好", "average": "一般", "poor": "较差", "critical": "严重", "categoryDistribution": "类别价值分布", "averageScore": "平均评分", "totalCategories": "总类别数", "score": "评分", "toolCount": "工具数量", "valueLevel": "价值等级", "highValueCategories": "高价值类别", "highValueTools": "高价值工具", "mediumValueTools": "中价值工具", "lowValueTools": "低价值工具"}, "toolCategories": {"title": "工具分类分布", "subtitle": "功能类型分析", "totalTools": "总工具数", "totalCategories": "分类数量", "largestCategory": "最大分类占比", "diversityIndex": "多样性指数", "distribution": "分类分布", "categories": "分类", "categoryNames": {"data": "数据处理", "web": "网络工具", "file": "文件操作", "api": "API接口", "database": "数据库", "ai": "人工智能", "utility": "实用工具", "development": "开发工具", "communication": "通信工具", "automation": "自动化", "security": "安全工具", "monitoring": "监控工具", "analytics": "分析工具", "content": "内容管理", "system": "系统工具", "数据获取": "数据获取", "开发工具": "开发工具", "通信协作": "通信协作", "其他工具": "其他工具", "分析处理": "分析处理"}}, "callEfficiency": "调用效率分析", "problemToolsAlert": "发现 {{count}} 个工具需要关注"}, "confirmCleanupTestData": "确定要清理所有测试数据吗？此操作不可撤销。", "cleanupSuccess": "测试数据清理成功", "cleanupFailed": "清理失败", "dateRange": {"quickSelect": "快速选择", "last1h": "最近1小时", "last24h": "最近24小时", "last7d": "最近7天", "last30d": "最近30天", "today": "今天", "yesterday": "昨天", "now": "现在", "setToNow": "设置为当前时间"}, "timeRange": "时间范围", "timeRangeOptions": {"1h": "1小时", "24h": "24小时", "7d": "7天", "30d": "30天"}, "metrics": {"call_count": "调用次数", "growth_rate": "增长率", "tool_usage": "工具使用量", "popularity": "受欢迎程度", "health_score": "健康评分", "ecosystem_status": "生态系统状态", "response_time": "响应时间", "performance": "性能", "tool_performance": "工具性能", "tool_health": "工具健康度", "availability": "可用性", "efficiency": "效率", "load_distribution": "负载分布", "reliability": "可靠性", "health_monitoring": "健康监控", "system_stability": "系统稳定性"}, "metricsOptions": {"calls": "API调用", "successRate": "成功率", "responseTime": "响应时间"}, "granularity": "粒度", "granularityOptions": {"hour": "小时", "day": "天", "week": "周"}, "noAnomalies": "未检测到异常", "anomaliesDetected": "检测到异常", "viewAll": "查看全部", "andMore": "还有 {{count}} 个...", "insightsTitle": "洞察分析", "insights": {"dashboard": "洞察分析仪表板", "allInsights": "所有洞察", "viewAll": "查看全部", "noInsights": "暂无可用洞察", "searchPlaceholder": "搜索洞察...", "details": "洞察详情", "confidence": "置信度", "actionable": "可操作", "type": {"trend": "趋势", "anomaly": "异常", "seasonality": "季节性", "correlation": "相关性", "forecast": "预测", "recommendation": "建议"}, "severity": {"critical": "严重", "warning": "警告", "success": "良好", "info": "信息"}, "topInsights": "关键洞察", "businessImpact": "业务影响", "impact": {"high": "高", "medium": "中", "low": "低"}, "recommendation": "建议", "urgency": {"immediate": "立即处理", "soon": "尽快处理", "monitor": "持续监控"}, "viewDetails": "查看详情", "metrics": "相关指标", "timestamp": "生成时间", "timeRange": "时间范围", "critical": "严重", "warning": "警告", "total": "总计", "typeDistribution": "洞察类型分布"}, "itemsSelected": "项已选择", "searchOptions": "搜索选项...", "selectAll": "全选", "deselectAll": "取消全选", "clearAll": "清空", "noOptionsFound": "未找到选项", "noOptionsAvailable": "无可用选项", "savedFilters": "已保存的筛选条件", "saveCurrentFilters": "保存当前筛选", "saveFilterPreset": "保存筛选预设", "presetName": "预设名称", "apply": "应用", "export": "导出", "import": "导入", "importError": "导入错误，文件格式无效", "noSavedFilters": "暂无保存的筛选条件", "advancedFilters": "高级筛选", "clientIpFilter": "客户端IP筛选", "selectClientIp": "选择客户端IP", "userIdFilter": "用户ID筛选", "selectUser": "选择用户", "chart": {"noData": "暂无数据", "noDataDescription": "所选时间范围内没有可用的数据点。", "instructions": "滚动缩放，拖拽平移", "reset": "重置视图", "zoom": "缩放", "pointDetails": "数据点详情", "timestamp": "时间戳"}, "anomaly": {"description": "异常描述", "low": "低", "medium": "中", "high": "高", "detailTitle": "异常详情", "historyTitle": "异常历史记录", "viewHistory": "查看异常历史", "timestamp": "时间戳", "type": {"spike": "峰值", "drop": "下降", "trend_break": "趋势中断", "outlier": "离群值", "volatility": "波动性"}, "severity": "严重程度", "category": {"performance": "性能", "error": "错误", "usage": "使用", "system": "系统"}, "duration": "持续时间", "dataPoints": "个数据点", "values": "数值", "actualValue": "实际值", "expectedValue": "期望值", "deviation": "偏差", "confidence": "置信度", "filterType": "类型", "filterSeverity": "严重程度", "filterMetric": "指标", "filterByType": "按类型筛选", "showing": "显示 {{count}} / {{total}} 个异常", "noAnomalies": "未找到异常", "total": "总计", "andMore": "还有 {{count}} 个...", "viewAll": "查看全部", "descriptions": {"spike": "检测到峰值：{{value}} 高于预期", "drop": "检测到下降：{{value}} 低于预期", "significantSpike": "显著峰值：{{value}} 高于预期（{{percent}}% 增长）", "significantDrop": "显著下降：{{value}} 低于预期（{{percent}}% 下降）", "volatility": "检测到波动性：相对于近期趋势偏差 {{percent}}%", "trendBreak": "检测到趋势中断：{{value}} 偏离预期模式", "trendBreakDirection": "趋势中断：方向从{{from}}转为{{to}}", "directionUp": "上升", "directionDown": "下降"}, "recommendations": {"callsDropHigh": "调查可能的服务中断或连接问题导致的API调用下降。", "callsSpikeHigh": "检查异常流量模式或潜在的DDoS攻击导致的API调用激增。", "successRateDropHigh": "紧急调查服务错误导致的成功率下降。", "responseTimeSpikeHigh": "检查导致响应时间激增的性能瓶颈。", "general": "监控相关指标并调查异常原因。"}}}, "logs": {"filters": "筛选", "search": "搜索日志...", "autoScroll": "自动滚动", "clearLogs": "清除日志", "loading": "加载日志中...", "noLogs": "暂无日志。", "noMatch": "没有匹配当前筛选条件的日志。", "mainProcess": "主进程", "childProcess": "子进程", "main": "主", "child": "子"}, "groups": {"add": "添加", "addNew": "添加新分组", "edit": "编辑分组", "delete": "删除", "confirmDelete": "您确定要删除此分组吗？", "deleteWarning": "删除分组 '{{name}}' 将会移除该分组及其所有服务器关联。此操作无法撤销。", "name": "分组名称", "namePlaceholder": "请输入分组名称", "nameRequired": "分组名称不能为空", "description": "描述", "descriptionPlaceholder": "请输入分组描述（可选）", "createError": "创建分组失败", "updateError": "更新分组失败", "deleteError": "删除分组失败", "serverAddError": "向分组添加服务器失败", "serverRemoveError": "从分组移除服务器失败", "addServer": "添加服务器到分组", "selectServer": "选择要添加的服务器", "servers": "分组中的服务器", "remove": "移除", "noGroups": "暂无可用分组。创建一个新分组以开始使用。", "noServers": "此分组中没有服务器。", "noServerOptions": "没有可用的服务器", "serverCount": "{{count}} 台服务器", "bearerAuthKey": "Bearer认证密钥", "bearerAuthKeyPlaceholder": "请输入Bearer认证密钥（可选）", "bearerAuthKeyDescription": "为此分组设置独立的Bearer认证密钥，将优先于全局配置使用", "bearerAuthKeyInvalid": "Bearer密钥格式无效。必须为8-256个字符，只能包含字母、数字、连字符、下划线和点号", "showBearerKey": "显示密钥", "hideBearerKey": "隐藏密钥", "bearerAuthEnabled": "已启用Bearer认证", "bearerAuthDisabled": "使用全局认证", "bearerAuthTooltip": "此分组已配置独立的Bearer认证密钥", "generateSecureKey": "生成安全密钥", "generateRandomKey": "生成随机密钥", "keyGenerated": "已生成新的安全密钥", "keyGenerationFailed": "密钥生成失败"}, "market": {"title": "服务器市场", "official": "官方", "by": "作者", "unknown": "未知", "tools": "工具", "search": "搜索", "searchPlaceholder": "搜索服务器名称、分类或标签", "clearFilters": "清除", "clearCategoryFilter": "", "clearTagFilter": "", "categories": "分类", "tags": "标签", "showTags": "显示标签", "hideTags": "隐藏标签", "moreTags": "", "noServers": "未找到匹配的服务器", "backToList": "返回列表", "install": "安装", "installing": "安装中...", "installed": "已安装", "installServer": "安装服务器: {{name}}", "installSuccess": "服务器 {{serverName}} 安装成功", "author": "作者", "license": "许可证", "repository": "代码仓库", "examples": "示例", "arguments": "参数", "argumentName": "名称", "description": "描述", "required": "必填", "example": "示例", "viewSchema": "查看结构", "fetchError": "获取服务器市场数据失败", "serverNotFound": "未找到服务器", "searchError": "搜索服务器失败", "filterError": "按分类筛选服务器失败", "tagFilterError": "按标签筛选服务器失败", "noInstallationMethod": "该服务器没有可用的安装方法", "showing": "显示 {{from}}-{{to}}/{{total}} 个服务器", "perPage": "每页显示", "confirmVariablesMessage": "请确保这些变量在运行环境中已正确定义。是否继续安装服务器？", "confirmAndInstall": "确认并安装"}, "pagination": {"previous": "上一页", "next": "下一页", "page": "第 {{page}} 页", "of": "共 {{total}} 页", "showing": "显示第 {{start}}-{{end}} 条，共 {{total}} 条", "noData": "暂无数据", "itemsPerPage": "每页显示", "items": "条记录", "jumpToPage": "跳转到", "go": "跳转"}, "tool": {"run": "运行", "running": "运行中...", "runTool": "运行", "cancel": "取消", "noDescription": "无描述信息", "inputSchema": "输入模式：", "runToolWithName": "运行工具：{{name}}", "execution": "工具执行", "successful": "成功", "failed": "失败", "result": "结果：", "error": "错误", "errorDetails": "错误详情：", "noContent": "工具执行成功但未返回内容。", "unknownError": "发生未知错误", "jsonResponse": "JSON 响应：", "toolResult": "工具结果", "noParameters": "此工具不需要任何参数。", "selectOption": "选择一个选项", "enterValue": "输入{{type}}值", "enabled": "已启用", "enableSuccess": "工具 {{name}} 启用成功", "disableSuccess": "工具 {{name}} 禁用成功", "toggleFailed": "切换工具状态失败", "parameters": "工具参数", "formMode": "表单模式", "jsonMode": "JSON 模式", "jsonConfiguration": "JSON 配置", "invalidJsonFormat": "无效的 JSON 格式", "fixJsonBeforeSwitching": "请修复 JSON 格式后再切换到表单模式", "item": "项目 {{index}}", "addItem": "添加 {{key}} 项目", "enterKey": "输入 {{key}}"}, "settings": {"enableGlobalRoute": "启用全局路由", "enableGlobalRouteDescription": "允许不指定组 ID 就连接到 /sse 端点", "enableGroupNameRoute": "启用组名路由", "enableGroupNameRouteDescription": "允许使用组名而不仅仅是组 ID 连接到 /sse 端点", "enableBearerAuth": "启用 Bearer 认证", "enableBearerAuthDescription": "对 MCP 请求启用 Bearer 令牌认证", "bearerAuthKey": "Bearer 认证密钥", "bearerAuthKeyDescription": "Bearer 令牌中需要携带的认证密钥", "bearerAuthKeyPlaceholder": "请输入 Bearer 认证密钥", "skipAuth": "免登录开关", "skipAuthDescription": "跳过前端和 API 访问的登录要求（默认关闭确保安全性）", "pythonIndexUrl": "Python 包仓库地址", "pythonIndexUrlDescription": "设置 UV_DEFAULT_INDEX 环境变量，用于 Python 包安装", "pythonIndexUrlPlaceholder": "例如: https://mirrors.aliyun.com/pypi/simple", "npmRegistry": "NPM 仓库地址", "npmRegistryDescription": "设置 npm_config_registry 环境变量，用于 NPM 包安装", "npmRegistryPlaceholder": "例如: https://registry.npmmirror.com/", "installConfig": "安装配置", "systemConfigUpdated": "系统配置更新成功", "enableSmartRouting": "启用智能路由", "enableSmartRoutingDescription": "开启智能路由功能，根据输入自动搜索最合适的工具（使用 $smart 分组）", "dbUrl": "PostgreSQL 连接地址（必须支持 pgvector）", "dbUrlPlaceholder": "例如: postgresql://user:password@localhost:5432/dbname", "openaiApiBaseUrl": "OpenAI API 基础地址", "openaiApiBaseUrlPlaceholder": "https://api.openai.com/v1", "openaiApiKey": "OpenAI API 密钥", "openaiApiKeyDescription": "用于访问 OpenAI API 的密钥", "openaiApiKeyPlaceholder": "请输入 OpenAI API 密钥", "openaiApiEmbeddingModel": "OpenAI 嵌入模型", "openaiApiEmbeddingModelPlaceholder": "text-embedding-3-small", "smartRoutingConfigUpdated": "智能路由配置更新成功", "smartRoutingRequiredFields": "启用智能路由需要填写数据库连接地址和 OpenAI API 密钥", "smartRoutingValidationError": "启用智能路由前请先填写必要字段：{{fields}}"}, "dxt": {"upload": "上传", "uploadTitle": "上传 DXT 扩展", "dropFileHere": "将 .dxt 文件拖拽到此处", "orClickToSelect": "或点击从计算机选择", "invalidFileType": "请选择有效的 .dxt 文件", "noFileSelected": "请选择要上传的 .dxt 文件", "uploading": "上传中...", "uploadFailed": "上传 DXT 文件失败", "installServer": "从 DXT 安装 MCP 服务器", "extensionInfo": "扩展信息", "name": "名称", "version": "版本", "description": "描述", "author": "作者", "tools": "工具", "serverName": "服务器名称", "serverNamePlaceholder": "为此服务器输入名称", "install": "安装", "installing": "安装中...", "installFailed": "从 DXT 安装服务器失败", "serverExistsTitle": "服务器已存在", "serverExistsConfirm": "服务器 '{{serverName}}' 已存在。是否要用新版本覆盖它？", "override": "覆盖"}, "monitoring": {"title": "系统监控", "systemHealth": "系统健康状态", "performanceMetrics": "性能指标", "systemStatus": "系统状态", "systemInfo": "系统信息", "database": "数据库", "mcpServers": "MCP服务器", "cache": "缓存", "servers": "服务器", "performance": "性能", "lastCheck": "最后检查", "healthError": "加载健康数据失败", "metricsError": "加载指标数据失败", "statusError": "加载状态数据失败", "cpuUsage": "CPU使用率", "memoryUsage": "内存使用率", "cores": "核心", "connections": "连接数", "responseTime": "响应时间", "uptime": "运行时间", "version": "版本", "running": "运行中", "total": "总计", "totalCalls": "总调用次数", "successRate": "成功率", "connected": "已连接", "connecting": "连接中", "reconnecting": "重连中", "disconnected": "已断开", "realtimeOn": "实时更新 开", "realtimeOff": "实时更新 关", "autoRefreshOn": "自动刷新 开", "autoRefreshOff": "自动刷新 关", "lastUpdate": "最后更新", "error": "监控错误", "status": {"healthy": "健康", "warning": "警告", "critical": "严重", "up": "正常", "down": "异常"}, "units": {"ms": "毫秒", "percent": "%", "bytes": "字节"}}, "format": {"locale": "zh-CN", "currency": "CNY", "dateTime": {"short": "MM/dd HH:mm", "medium": "yyyy/MM/dd HH:mm:ss", "long": "yyyy年MM月dd日 HH:mm:ss"}, "number": {"decimal": 2, "thousand": ",", "decimalPoint": "."}}}