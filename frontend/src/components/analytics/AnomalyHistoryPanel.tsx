import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AnomalyPoint } from '../../services/analyticsService';
import AnomalyDetailPanel from './AnomalyDetailPanel';

interface AnomalyHistoryPanelProps {
  anomalies: {
    calls?: AnomalyPoint[];
    success_rate?: AnomalyPoint[];
    response_time?: AnomalyPoint[];
  };
  onClose: () => void;
}

const AnomalyHistoryPanel: React.FC<AnomalyHistoryPanelProps> = ({
  anomalies,
  onClose,
}) => {
  const { t } = useTranslation();
  const [selectedAnomaly, setSelectedAnomaly] = useState<{
    anomaly: AnomalyPoint;
    metric: 'calls' | 'success_rate' | 'response_time';
  } | null>(null);
  const [filterType, setFilterType] = useState<string>('all');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [filterMetric, setFilterMetric] = useState<string>('all');

  // Combine all anomalies with their metric type
  const allAnomalies = useMemo(() => {
    const combined: Array<{
      anomaly: AnomalyPoint;
      metric: 'calls' | 'success_rate' | 'response_time';
    }> = [];

    if (anomalies.calls) {
      anomalies.calls.forEach(anomaly => {
        combined.push({ anomaly, metric: 'calls' });
      });
    }
    if (anomalies.success_rate) {
      anomalies.success_rate.forEach(anomaly => {
        combined.push({ anomaly, metric: 'success_rate' });
      });
    }
    if (anomalies.response_time) {
      anomalies.response_time.forEach(anomaly => {
        combined.push({ anomaly, metric: 'response_time' });
      });
    }

    // Sort by timestamp (newest first)
    return combined.sort((a, b) => 
      new Date(b.anomaly.timestamp).getTime() - new Date(a.anomaly.timestamp).getTime()
    );
  }, [anomalies]);

  // Filter anomalies
  const filteredAnomalies = useMemo(() => {
    return allAnomalies.filter(({ anomaly, metric }) => {
      if (filterType !== 'all' && anomaly.type !== filterType) return false;
      if (filterSeverity !== 'all' && anomaly.severity !== filterSeverity) return false;
      if (filterMetric !== 'all' && metric !== filterMetric) return false;
      return true;
    });
  }, [allAnomalies, filterType, filterSeverity, filterMetric]);

  // Get statistics
  const stats = useMemo(() => {
    const total = allAnomalies.length;
    const high = allAnomalies.filter(({ anomaly }) => anomaly.severity === 'high').length;
    const medium = allAnomalies.filter(({ anomaly }) => anomaly.severity === 'medium').length;
    const low = allAnomalies.filter(({ anomaly }) => anomaly.severity === 'low').length;

    const typeStats = {
      spike: allAnomalies.filter(({ anomaly }) => anomaly.type === 'spike').length,
      drop: allAnomalies.filter(({ anomaly }) => anomaly.type === 'drop').length,
      trend_break: allAnomalies.filter(({ anomaly }) => anomaly.type === 'trend_break').length,
      outlier: allAnomalies.filter(({ anomaly }) => anomaly.type === 'outlier').length,
      volatility: allAnomalies.filter(({ anomaly }) => anomaly.type === 'volatility').length,
    };

    return { total, high, medium, low, typeStats };
  }, [allAnomalies]);

  // Format date for display
  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  // Get color for severity
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // Get metric label
  const getMetricLabel = (metric: string) => {
    switch (metric) {
      case 'calls':
        return t('analytics.metrics.calls', 'API Calls');
      case 'success_rate':
        return t('analytics.metrics.successRate', 'Success Rate');
      case 'response_time':
        return t('analytics.metrics.responseTime', 'Response Time');
      default:
        return metric;
    }
  };

  return (
    <>
      <div className="fixed inset-0 z-40 flex items-center justify-center bg-black bg-opacity-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {t('analytics.anomaly.historyTitle', 'Anomaly History')}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* Statistics */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.total}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t('analytics.anomaly.total', 'Total')}
                </p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-red-600">{stats.high}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t('analytics.anomaly.high', 'High')}
                </p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-yellow-600">{stats.medium}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t('analytics.anomaly.medium', 'Medium')}
                </p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{stats.low}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t('analytics.anomaly.low', 'Low')}
                </p>
              </div>
            </div>
          </div>
          
          {/* Filters */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('analytics.anomaly.filterType', 'Type')}
                </label>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">{t('common.all', 'All')}</option>
                  <option value="spike">{t('analytics.anomaly.type.spike', 'Spike')}</option>
                  <option value="drop">{t('analytics.anomaly.type.drop', 'Drop')}</option>
                  <option value="trend_break">{t('analytics.anomaly.type.trend_break', 'Trend Break')}</option>
                  <option value="outlier">{t('analytics.anomaly.type.outlier', 'Outlier')}</option>
                  <option value="volatility">{t('analytics.anomaly.type.volatility', 'Volatility')}</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('analytics.anomaly.filterSeverity', 'Severity')}
                </label>
                <select
                  value={filterSeverity}
                  onChange={(e) => setFilterSeverity(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">{t('common.all', 'All')}</option>
                  <option value="high">{t('analytics.anomaly.high', 'High')}</option>
                  <option value="medium">{t('analytics.anomaly.medium', 'Medium')}</option>
                  <option value="low">{t('analytics.anomaly.low', 'Low')}</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('analytics.anomaly.filterMetric', 'Metric')}
                </label>
                <select
                  value={filterMetric}
                  onChange={(e) => setFilterMetric(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">{t('common.all', 'All')}</option>
                  <option value="calls">{t('analytics.metrics.calls', 'API Calls')}</option>
                  <option value="success_rate">{t('analytics.metrics.successRate', 'Success Rate')}</option>
                  <option value="response_time">{t('analytics.metrics.responseTime', 'Response Time')}</option>
                </select>
              </div>
            </div>
          </div>
          
          {/* Anomaly List */}
          <div className="flex-1 overflow-y-auto p-4">
            {filteredAnomalies.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">
                  {t('analytics.anomaly.noAnomalies', 'No anomalies found')}
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredAnomalies.map(({ anomaly, metric }, index) => (
                  <div
                    key={index}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                    onClick={() => setSelectedAnomaly({ anomaly, metric })}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center mb-1">
                          <span className={`px-2 py-1 rounded text-xs font-medium mr-2 ${getSeverityColor(anomaly.severity)}`}>
                            {t(`analytics.anomaly.${anomaly.severity}`, anomaly.severity.toUpperCase())}
                          </span>
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {t(`analytics.anomaly.type.${anomaly.type}`, anomaly.type)}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                            in {getMetricLabel(metric)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                          {anomaly.description}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {formatDate(anomaly.timestamp)}
                        </p>
                      </div>
                      <div className="text-right">
                        {anomaly.confidence && (
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {(anomaly.confidence * 100).toFixed(0)}% {t('analytics.anomaly.confidence', 'confidence')}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          {/* Footer */}
          <div className="flex justify-between items-center p-4 border-t border-gray-200 dark:border-gray-700">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t('analytics.anomaly.showing', 'Showing {{count}} of {{total}} anomalies', {
                count: filteredAnomalies.length,
                total: allAnomalies.length
              })}
            </p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              {t('common.close', 'Close')}
            </button>
          </div>
        </div>
      </div>
      
      {/* Anomaly Detail Panel */}
      {selectedAnomaly && (
        <AnomalyDetailPanel
          anomaly={selectedAnomaly.anomaly}
          metricType={selectedAnomaly.metric}
          onClose={() => setSelectedAnomaly(null)}
        />
      )}
    </>
  );
};

export default AnomalyHistoryPanel;
