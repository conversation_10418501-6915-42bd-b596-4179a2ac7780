import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

// Filter preset interface
export interface FilterPreset {
  id: string;
  name: string;
  filters: any; // The actual filter values
  createdAt: string;
}

export interface FilterPresetsProps {
  currentFilters: any;
  onApplyPreset: (preset: FilterPreset) => void;
  className?: string;
}

const STORAGE_KEY = 'mcp_analytics_filter_presets';

const FilterPresets: React.FC<FilterPresetsProps> = ({
  currentFilters,
  onApplyPreset,
  className = ''
}) => {
  const { t } = useTranslation();
  const [presets, setPresets] = useState<FilterPreset[]>([]);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [presetName, setPresetName] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  // Load presets from localStorage
  useEffect(() => {
    const savedPresets = localStorage.getItem(STORAGE_KEY);
    if (savedPresets) {
      try {
        setPresets(JSON.parse(savedPresets));
      } catch (error) {
        console.error('Error parsing saved filter presets:', error);
        // If there's an error, reset the presets
        localStorage.removeItem(STORAGE_KEY);
      }
    }
  }, []);

  // Save presets to localStorage
  const savePresetsToStorage = (updatedPresets: FilterPreset[]) => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedPresets));
    setPresets(updatedPresets);
  };

  // Save current filters as a preset
  const handleSavePreset = () => {
    if (!presetName.trim()) return;

    const newPreset: FilterPreset = {
      id: `preset_${Date.now()}`,
      name: presetName.trim(),
      filters: { ...currentFilters },
      createdAt: new Date().toISOString()
    };

    const updatedPresets = [...presets, newPreset];
    savePresetsToStorage(updatedPresets);
    setPresetName('');
    setShowSaveDialog(false);
  };

  // Delete a preset
  const handleDeletePreset = (id: string) => {
    const updatedPresets = presets.filter(preset => preset.id !== id);
    savePresetsToStorage(updatedPresets);
    setShowDeleteConfirm(null);
  };

  // Export presets to a JSON file
  const handleExportPresets = () => {
    if (presets.length === 0) return;

    const dataStr = JSON.stringify(presets, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
    
    const exportFileDefaultName = `mcp_filter_presets_${new Date().toISOString().slice(0, 10)}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  // Import presets from a JSON file
  const handleImportPresets = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedPresets = JSON.parse(e.target?.result as string);
        if (Array.isArray(importedPresets)) {
          // Validate imported presets
          const validPresets = importedPresets.filter(preset => 
            preset.id && preset.name && preset.filters && preset.createdAt
          );
          
          // Merge with existing presets, avoiding duplicates
          const existingIds = presets.map(p => p.id);
          const newPresets = validPresets.filter(p => !existingIds.includes(p.id));
          
          savePresetsToStorage([...presets, ...newPresets]);
        }
      } catch (error) {
        console.error('Error importing presets:', error);
        alert(t('analytics.importError', 'Error importing presets. Invalid file format.'));
      }
    };
    reader.readAsText(file);
    
    // Reset the input
    event.target.value = '';
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          {t('analytics.savedFilters', 'Saved Filters')}
        </h3>
        <div className="flex gap-2">
          <button
            onClick={() => setShowSaveDialog(true)}
            className="px-3 py-1 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {t('analytics.saveCurrentFilters', 'Save Current')}
          </button>
          <button
            onClick={handleExportPresets}
            disabled={presets.length === 0}
            className="px-3 py-1 text-sm bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {t('analytics.export', 'Export')}
          </button>
          <label className="px-3 py-1 text-sm bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 cursor-pointer">
            {t('analytics.import', 'Import')}
            <input
              type="file"
              accept=".json"
              onChange={handleImportPresets}
              className="hidden"
            />
          </label>
        </div>
      </div>

      {/* Save Dialog */}
      {showSaveDialog && (
        <div className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md p-4 shadow-md">
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-2">
            {t('analytics.saveFilterPreset', 'Save Filter Preset')}
          </h4>
          <input
            type="text"
            value={presetName}
            onChange={(e) => setPresetName(e.target.value)}
            placeholder={t('analytics.presetName', 'Preset Name')}
            className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 mb-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <div className="flex justify-end gap-2">
            <button
              onClick={() => setShowSaveDialog(false)}
              className="px-3 py-1 text-sm bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <button
              onClick={handleSavePreset}
              disabled={!presetName.trim()}
              className="px-3 py-1 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {t('common.save', 'Save')}
            </button>
          </div>
        </div>
      )}

      {/* Presets List */}
      {presets.length > 0 ? (
        <div className="space-y-2 max-h-60 overflow-y-auto">
          {presets.map((preset) => (
            <div
              key={preset.id}
              className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md p-3 flex justify-between items-center"
            >
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  {preset.name}
                </h4>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {new Date(preset.createdAt).toLocaleDateString()}
                </p>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => onApplyPreset(preset)}
                  className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  {t('analytics.apply', 'Apply')}
                </button>
                {showDeleteConfirm === preset.id ? (
                  <>
                    <button
                      onClick={() => handleDeletePreset(preset.id)}
                      className="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 focus:outline-none focus:ring-1 focus:ring-red-500"
                    >
                      {t('common.confirm', 'Confirm')}
                    </button>
                    <button
                      onClick={() => setShowDeleteConfirm(null)}
                      className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-gray-500"
                    >
                      {t('common.cancel', 'Cancel')}
                    </button>
                  </>
                ) : (
                  <button
                    onClick={() => setShowDeleteConfirm(preset.id)}
                    className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-gray-500"
                  >
                    {t('common.delete', 'Delete')}
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-4 text-gray-500 dark:text-gray-400">
          {t('analytics.noSavedFilters', 'No saved filters yet')}
        </div>
      )}
    </div>
  );
};

export default FilterPresets;
