/* Overview页面样式 */

.overviewContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.kpiGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.kpiCard {
  width: 380px;
  height: 280px;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  transition: all 0.2s ease-in-out;
}

.kpiCard:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.dark .kpiCard {
  background: #1f2937;
  border: 1px solid #374151;
}

.cardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.cardTitle {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
}

.dark .cardTitle {
  color: #f9fafb;
}

.cardSubtitle {
  font-size: 0.75rem;
  color: #6b7280;
}

.dark .cardSubtitle {
  color: #9ca3af;
}

.rankingList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.rankingItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.rankingLeft {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  min-width: 0;
}

.rankingIcon {
  font-size: 1.125rem;
  flex-shrink: 0;
}

.rankingName {
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .rankingName {
  color: #f9fafb;
}

.rankingRight {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.rankingPercentage {
  font-size: 0.875rem;
  color: #6b7280;
}

.dark .rankingPercentage {
  color: #9ca3af;
}

.trendIcon {
  font-size: 0.75rem;
}

.trendUp {
  color: #10b981;
}

.trendDown {
  color: #ef4444;
}

.trendStable {
  color: #6b7280;
}

.circularProgress {
  position: relative;
  width: 6rem;
  height: 6rem;
  margin: 0 auto;
}

.progressRing {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 8px solid #e5e7eb;
  position: relative;
}

.dark .progressRing {
  border-color: #374151;
}

.progressFill {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 8px solid #10b981;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
  transform: rotate(-90deg);
}

.progressLabel {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;
  font-weight: 700;
  color: #111827;
}

.dark .progressLabel {
  color: #f9fafb;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  font-size: 0.875rem;
}

.statItem {
  display: flex;
  flex-direction: column;
}

.statLabel {
  color: #6b7280;
}

.dark .statLabel {
  color: #9ca3af;
}

.statValue {
  font-weight: 500;
  margin-left: 0.25rem;
}

.statValueHealthy {
  color: #10b981;
}

.statValueWarning {
  color: #f59e0b;
}

.statValueCritical {
  color: #ef4444;
}

.statValueNormal {
  color: #111827;
}

.dark .statValueNormal {
  color: #f9fafb;
}

.insightsPanel {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.dark .insightsPanel {
  background: #1f2937;
  border: 1px solid #374151;
}

.insightsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.insightCard {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
}

.dark .insightCard {
  border-color: #374151;
}

.insightHeader {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.insightType {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 9999px;
}

.insightTypeTrend {
  background: #dbeafe;
  color: #1e40af;
}

.dark .insightTypeTrend {
  background: rgba(59, 130, 246, 0.2);
  color: #93c5fd;
}

.insightTypeAttention {
  background: #fef3c7;
  color: #92400e;
}

.dark .insightTypeAttention {
  background: rgba(245, 158, 11, 0.2);
  color: #fbbf24;
}

.insightTypeOptimization {
  background: #d1fae5;
  color: #065f46;
}

.dark .insightTypeOptimization {
  background: rgba(16, 185, 129, 0.2);
  color: #6ee7b7;
}

.insightPriority {
  font-size: 0.75rem;
}

.priorityHigh {
  color: #ef4444;
}

.priorityMedium {
  color: #f59e0b;
}

.priorityLow {
  color: #10b981;
}

.insightTitle {
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
}

.dark .insightTitle {
  color: #f9fafb;
}

.insightDescription {
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.4;
}

.dark .insightDescription {
  color: #9ca3af;
}

.actionButton {
  color: #2563eb;
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.2s ease-in-out;
}

.actionButton:hover {
  color: #1d4ed8;
}

.dark .actionButton {
  color: #60a5fa;
}

.dark .actionButton:hover {
  color: #93c5fd;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .kpiGrid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
  
  .kpiCard {
    width: 100%;
    max-width: 380px;
  }
}

@media (max-width: 768px) {
  .kpiGrid {
    grid-template-columns: 1fr;
  }
  
  .kpiCard {
    width: 100%;
    height: auto;
    min-height: 280px;
  }
  
  .insightsGrid {
    grid-template-columns: 1fr;
  }
}
