import React from 'react';
import { useTranslation } from 'react-i18next';
import { AnomalyPoint } from '../../services/analyticsService';

interface AnomalyDetailPanelProps {
  anomaly: AnomalyPoint;
  onClose: () => void;
  metricType: 'calls' | 'success_rate' | 'response_time';
}

const AnomalyDetailPanel: React.FC<AnomalyDetailPanelProps> = ({
  anomaly,
  onClose,
  metricType,
}) => {
  const { t } = useTranslation();

  // Format date for display
  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  // Format value for display
  const formatValue = (value: number, type: string) => {
    switch (type) {
      case 'calls':
        return value.toLocaleString();
      case 'success_rate':
        return `${value.toFixed(1)}%`;
      case 'response_time':
        return `${value.toFixed(0)}ms`;
      default:
        return value.toString();
    }
  };

  // Get color for severity
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // Get icon for anomaly type
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'spike':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
        );
      case 'drop':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0v-8m0 8l-8-8-4 4-6-6" />
          </svg>
        );
      case 'trend_break':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'volatility':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        );
    }
  };

  // Get category label
  const getCategoryLabel = (category?: string) => {
    if (!category) return '';
    
    switch (category) {
      case 'performance':
        return t('analytics.anomaly.category.performance', 'Performance');
      case 'error':
        return t('analytics.anomaly.category.error', 'Error');
      case 'usage':
        return t('analytics.anomaly.category.usage', 'Usage');
      case 'system':
        return t('analytics.anomaly.category.system', 'System');
      default:
        return category;
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className={`mr-2 p-1 rounded ${getSeverityColor(anomaly.severity)}`}>
              {getTypeIcon(anomaly.type)}
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {t('analytics.anomaly.detailTitle', 'Anomaly Details')}
            </h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Content */}
        <div className="p-4">
          {/* Basic Info */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t('analytics.anomaly.timestamp', 'Timestamp')}
              </h4>
              <span className="text-sm text-gray-900 dark:text-white">
                {formatDate(anomaly.timestamp)}
              </span>
            </div>
            
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t('analytics.anomaly.type', 'Type')}
              </h4>
              <span className="text-sm text-gray-900 dark:text-white flex items-center">
                {getTypeIcon(anomaly.type)}
                <span className="ml-1">{t(`analytics.anomaly.type.${anomaly.type}`, anomaly.type)}</span>
              </span>
            </div>
            
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t('analytics.anomaly.severity', 'Severity')}
              </h4>
              <span className={`text-sm px-2 py-1 rounded ${getSeverityColor(anomaly.severity)}`}>
                {t(`analytics.anomaly.${anomaly.severity}`, anomaly.severity.toUpperCase())}
              </span>
            </div>
            
            {anomaly.category && (
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('analytics.anomaly.category', 'Category')}
                </h4>
                <span className="text-sm text-gray-900 dark:text-white">
                  {getCategoryLabel(anomaly.category)}
                </span>
              </div>
            )}
            
            {anomaly.duration && (
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('analytics.anomaly.duration', 'Duration')}
                </h4>
                <span className="text-sm text-gray-900 dark:text-white">
                  {anomaly.duration} {t('analytics.anomaly.dataPoints', 'data points')}
                </span>
              </div>
            )}
          </div>
          
          {/* Values */}
          <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('analytics.anomaly.values', 'Values')}
            </h4>
            
            <div className="grid grid-cols-2 gap-2">
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {t('analytics.anomaly.actualValue', 'Actual')}
                </p>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatValue(anomaly.value, metricType)}
                </p>
              </div>
              
              {anomaly.expectedValue !== undefined && (
                <div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {t('analytics.anomaly.expectedValue', 'Expected')}
                  </p>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {formatValue(anomaly.expectedValue, metricType)}
                  </p>
                </div>
              )}
              
              {anomaly.deviation !== undefined && (
                <div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {t('analytics.anomaly.deviation', 'Deviation')}
                  </p>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {formatValue(anomaly.deviation, metricType)}
                  </p>
                </div>
              )}
              
              {anomaly.confidence !== undefined && (
                <div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {t('analytics.anomaly.confidence', 'Confidence')}
                  </p>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {(anomaly.confidence * 100).toFixed(0)}%
                  </p>
                </div>
              )}
            </div>
          </div>
          
          {/* Description */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('analytics.anomaly.description', 'Description')}
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 p-2 bg-gray-50 dark:bg-gray-700 rounded">
              {anomaly.description}
            </p>
          </div>
        </div>
        
        {/* Footer */}
        <div className="flex justify-end p-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            {t('common.close', 'Close')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AnomalyDetailPanel;
