import React from 'react';
import { useTranslation } from 'react-i18next';

export interface DateRange {
  startTime: string;
  endTime: string;
}

export interface DateRangeSelectorProps {
  value: DateRange;
  onChange: (range: DateRange) => void;
  className?: string;
}

// Helper function to convert ISO string to datetime-local format
const toDateTimeLocal = (isoString: string): string => {
  const date = new Date(isoString);
  // Convert to local timezone and format for datetime-local input
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

// Predefined date range options
const getDateRangePresets = () => {
  const now = new Date();
  const presets = [
    {
      key: 'last1h',
      label: 'analytics.dateRange.last1h',
      getValue: () => ({
        startTime: new Date(now.getTime() - 60 * 60 * 1000).toISOString(),
        endTime: now.toISOString()
      })
    },
    {
      key: 'last24h',
      label: 'analytics.dateRange.last24h',
      getValue: () => ({
        startTime: new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString(),
        endTime: now.toISOString()
      })
    },
    {
      key: 'last7d',
      label: 'analytics.dateRange.last7d',
      getValue: () => ({
        startTime: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        endTime: now.toISOString()
      })
    },
    {
      key: 'last30d',
      label: 'analytics.dateRange.last30d',
      getValue: () => ({
        startTime: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        endTime: now.toISOString()
      })
    },
    {
      key: 'today',
      label: 'analytics.dateRange.today',
      getValue: () => {
        // 使用本地时间计算今天的范围
        const now = new Date();
        const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0);
        return {
          startTime: todayStart.toISOString(),
          endTime: now.toISOString() // 使用当前时间作为结束时间
        };
      }
    },
    {
      key: 'yesterday',
      label: 'analytics.dateRange.yesterday',
      getValue: () => {
        // 使用本地时间计算昨天的范围
        const now = new Date();
        const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
        const yesterdayStart = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 0, 0, 0, 0);
        const yesterdayEnd = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59, 999);
        return {
          startTime: yesterdayStart.toISOString(),
          endTime: yesterdayEnd.toISOString()
        };
      }
    }
  ];

  return presets;
};;

const DateRangeSelector: React.FC<DateRangeSelectorProps> = ({
  value,
  onChange,
  className = ''
}) => {
  const { t } = useTranslation();
  const presets = getDateRangePresets();

  const handlePresetClick = (preset: typeof presets[0]) => {
    const range = preset.getValue();
    onChange(range);
  };

  const handleStartTimeChange = (startTime: string) => {
    // Convert datetime-local format to ISO string
    const isoString = startTime ? new Date(startTime).toISOString() : startTime;
    onChange({ ...value, startTime: isoString });
  };

  const handleEndTimeChange = (endTime: string) => {
    // Convert datetime-local format to ISO string
    const isoString = endTime ? new Date(endTime).toISOString() : endTime;
    onChange({ ...value, endTime: isoString });
  };

  const handleSetEndTimeToNow = () => {
    const now = new Date().toISOString();
    onChange({ ...value, endTime: now });
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Quick Presets */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('analytics.dateRange.quickSelect', 'Quick Select')}
        </label>
        <div className="flex flex-wrap gap-2">
          {presets.map((preset) => (
            <button
              key={preset.key}
              onClick={() => handlePresetClick(preset)}
              className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {t(preset.label)}
            </button>
          ))}
        </div>
      </div>

      {/* Custom Date Range */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('analytics.startTime', 'Start Time')}
          </label>
          <input
            type="datetime-local"
            value={toDateTimeLocal(value.startTime)}
            onChange={(e) => handleStartTimeChange(e.target.value)}
            className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('analytics.endTime', 'End Time')}
          </label>
          <div className="flex gap-2">
            <input
              type="datetime-local"
              value={toDateTimeLocal(value.endTime)}
              onChange={(e) => handleEndTimeChange(e.target.value)}
              className="flex-1 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              type="button"
              onClick={handleSetEndTimeToNow}
              className="px-3 py-2 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 whitespace-nowrap"
              title={t('analytics.dateRange.setToNow', 'Set to current time')}
            >
              {t('analytics.dateRange.now', 'Now')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DateRangeSelector;
