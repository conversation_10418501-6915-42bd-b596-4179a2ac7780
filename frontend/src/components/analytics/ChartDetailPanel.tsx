import React from 'react';
import { useTranslation } from 'react-i18next';
import { TrendPoint, AnomalyPoint } from '../../services/analyticsService';
import { useFormatters } from '../../utils/formatters';
import { localizeAnomalyDescription } from '../../utils/anomalyLocalizer';

interface ChartDetailPanelProps {
  point: TrendPoint;
  anomalies?: {
    calls?: AnomalyPoint[];
    success_rate?: AnomalyPoint[];
    response_time?: AnomalyPoint[];
  };
  onClose: () => void;
}

const ChartDetailPanel: React.FC<ChartDetailPanelProps> = ({
  point,
  anomalies,
  onClose,
}) => {
  const { t } = useTranslation();
  const { formatValue, formatDate } = useFormatters();

  // Check if this point has anomalies
  const hasAnomalies = React.useMemo(() => {
    if (!anomalies) return false;
    
    const timestamp = point.timestamp;
    return (
      (anomalies.calls?.some(a => a.timestamp === timestamp)) ||
      (anomalies.success_rate?.some(a => a.timestamp === timestamp)) ||
      (anomalies.response_time?.some(a => a.timestamp === timestamp))
    );
  }, [point, anomalies]);

  // Get anomaly details for this point
  const getAnomalyDetails = (metric: 'calls' | 'success_rate' | 'response_time') => {
    if (!anomalies || !anomalies[metric]) return null;

    const anomaly = anomalies[metric]?.find(a => a.timestamp === point.timestamp);
    if (!anomaly) return null;

    return anomaly;
  };

  // Get icon for anomaly type
  const getTypeIcon = (type?: string) => {
    if (!type) return null;

    switch (type) {
      case 'spike':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
        );
      case 'drop':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0v-8m0 8l-8-8-4 4-6-6" />
          </svg>
        );
      case 'trend_break':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'volatility':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        );
    }
  };

  // Get severity color
  const getSeverityColor = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'low':
        return 'text-yellow-500';
      case 'medium':
        return 'text-orange-500';
      case 'high':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t('analytics.chart.pointDetails', 'Data Point Details')}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Content */}
        <div className="p-4">
          {/* Timestamp */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
              {t('analytics.chart.timestamp', 'Timestamp')}
            </h4>
            <p className="text-base text-gray-900 dark:text-white">
              {formatDate(point.timestamp)}
            </p>
          </div>
          
          {/* Metrics */}
          <div className="space-y-4">
            {/* API Calls */}
            <div className="flex items-start">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('analytics.metrics.calls', 'API Calls')}
                </h4>
                <p className="text-base text-gray-900 dark:text-white">
                  {formatValue(point.calls, 'calls')}
                </p>
              </div>
              {getAnomalyDetails('calls') && (
                <div className={`px-2 py-1 rounded text-xs font-medium ${getSeverityColor(getAnomalyDetails('calls')!.severity)}`}>
                  {t(`analytics.anomaly.${getAnomalyDetails('calls')!.severity}`, getAnomalyDetails('calls')!.severity.toUpperCase())}
                </div>
              )}
            </div>
            
            {/* Success Rate */}
            <div className="flex items-start">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('analytics.metrics.successRate', 'Success Rate')}
                </h4>
                <p className="text-base text-gray-900 dark:text-white">
                  {formatValue(point.success_rate, 'success_rate')}
                </p>
              </div>
              {getAnomalyDetails('success_rate') && (
                <div className={`px-2 py-1 rounded text-xs font-medium ${getSeverityColor(getAnomalyDetails('success_rate')!.severity)}`}>
                  {t(`analytics.anomaly.${getAnomalyDetails('success_rate')!.severity}`, getAnomalyDetails('success_rate')!.severity.toUpperCase())}
                </div>
              )}
            </div>
            
            {/* Response Time */}
            <div className="flex items-start">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('analytics.metrics.responseTime', 'Response Time')}
                </h4>
                <p className="text-base text-gray-900 dark:text-white">
                  {formatValue(point.response_time, 'response_time')}
                </p>
              </div>
              {getAnomalyDetails('response_time') && (
                <div className={`px-2 py-1 rounded text-xs font-medium ${getSeverityColor(getAnomalyDetails('response_time')!.severity)}`}>
                  {t(`analytics.anomaly.${getAnomalyDetails('response_time')!.severity}`, getAnomalyDetails('response_time')!.severity.toUpperCase())}
                </div>
              )}
            </div>
          </div>
          
          {/* Anomaly Description */}
          {hasAnomalies && (
            <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('analytics.anomaly.description', 'Anomaly Description')}
              </h4>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                {getAnomalyDetails('calls') && (
                  <li>
                    <div className="flex items-center">
                      <div className="mr-1">
                        {getTypeIcon(getAnomalyDetails('calls')!.type)}
                      </div>
                      <div>
                        <span className="font-medium">{t('analytics.metrics.calls', 'API Calls')}:</span>{' '}
                        <span className={`px-2 py-0.5 rounded text-xs font-medium ${getSeverityColor(getAnomalyDetails('calls')!.severity)}`}>
                          {t(`analytics.anomaly.type.${getAnomalyDetails('calls')!.type}`, getAnomalyDetails('calls')!.type)}
                        </span>
                        <p className="mt-1">{localizeAnomalyDescription(getAnomalyDetails('calls')!.description, t)}</p>
                        {getAnomalyDetails('calls')!.category && (
                          <p className="text-xs mt-1">
                            {t('analytics.anomaly.category', 'Category')}: {t(`analytics.anomaly.category.${getAnomalyDetails('calls')!.category}`, getAnomalyDetails('calls')!.category!)}
                          </p>
                        )}
                      </div>
                    </div>
                  </li>
                )}
                {getAnomalyDetails('success_rate') && (
                  <li>
                    <div className="flex items-center">
                      <div className="mr-1">
                        {getTypeIcon(getAnomalyDetails('success_rate')!.type)}
                      </div>
                      <div>
                        <span className="font-medium">{t('analytics.metrics.successRate', 'Success Rate')}:</span>{' '}
                        <span className={`px-2 py-0.5 rounded text-xs font-medium ${getSeverityColor(getAnomalyDetails('success_rate')!.severity)}`}>
                          {t(`analytics.anomaly.type.${getAnomalyDetails('success_rate')!.type}`, getAnomalyDetails('success_rate')!.type)}
                        </span>
                        <p className="mt-1">{localizeAnomalyDescription(getAnomalyDetails('success_rate')!.description, t)}</p>
                        {getAnomalyDetails('success_rate')!.category && (
                          <p className="text-xs mt-1">
                            {t('analytics.anomaly.category', 'Category')}: {t(`analytics.anomaly.category.${getAnomalyDetails('success_rate')!.category}`, getAnomalyDetails('success_rate')!.category!)}
                          </p>
                        )}
                      </div>
                    </div>
                  </li>
                )}
                {getAnomalyDetails('response_time') && (
                  <li>
                    <div className="flex items-center">
                      <div className="mr-1">
                        {getTypeIcon(getAnomalyDetails('response_time')!.type)}
                      </div>
                      <div>
                        <span className="font-medium">{t('analytics.metrics.responseTime', 'Response Time')}:</span>{' '}
                        <span className={`px-2 py-0.5 rounded text-xs font-medium ${getSeverityColor(getAnomalyDetails('response_time')!.severity)}`}>
                          {t(`analytics.anomaly.type.${getAnomalyDetails('response_time')!.type}`, getAnomalyDetails('response_time')!.type)}
                        </span>
                        <p className="mt-1">{localizeAnomalyDescription(getAnomalyDetails('response_time')!.description, t)}</p>
                        {getAnomalyDetails('response_time')!.category && (
                          <p className="text-xs mt-1">
                            {t('analytics.anomaly.category', 'Category')}: {t(`analytics.anomaly.category.${getAnomalyDetails('response_time')!.category}`, getAnomalyDetails('response_time')!.category!)}
                          </p>
                        )}
                      </div>
                    </div>
                  </li>
                )}
              </ul>
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="flex justify-end p-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            {t('common.close', 'Close')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChartDetailPanel;
