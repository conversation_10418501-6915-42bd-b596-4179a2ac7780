import React from 'react';
import { useTranslation } from 'react-i18next';
import { useOverviewData } from '../../hooks/useOverviewData';
import { PageSpinner } from '../ui/LoadingSpinner';
import { ErrorEmptyState } from '../ui/EmptyState';
import { KPICard, StatusIndicator, TopServersCard, ToolEcosystemCard, ToolHeatmapCard, BusinessValueCard, ToolCategoriesCard } from './overview';
import InsightsPanel from './overview/InsightsPanel';

/**
 * Overview页面主组件
 * 采用2行3列的网格布局，展示6个KPI卡片
 */
const OverviewTab: React.FC = () => {
  const { t } = useTranslation();
  const { data, loading, error, refetch } = useOverviewData();

  // 加载状态
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t('pages.analytics.overview')}
          </h2>
        </div>
        <PageSpinner message={t('common.loading', 'Loading...')} />
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t('pages.analytics.overview')}
          </h2>
          <button
            onClick={refetch}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {t('common.retry', 'Retry')}
          </button>
        </div>
        <ErrorEmptyState
          title={t('analytics.overview.loadError', 'Failed to load overview data')}
          description={error}
          actionLabel={t('common.retry', 'Retry')}
          onAction={refetch}
        />
      </div>
    );
  }

  // 数据为空状态
  if (!data) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t('pages.analytics.overview')}
          </h2>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <p className="text-gray-600 dark:text-gray-400">
            {t('analytics.overview.noData', 'No overview data available')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和刷新按钮 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t('pages.analytics.overview')}
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            {t('analytics.overview.description', 'Comprehensive overview of your MCP ecosystem')}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {t('analytics.overview.lastUpdated', 'Last updated')}: {' '}
            {new Date(data.metadata.lastUpdated).toLocaleString()}
          </span>
          <button
            onClick={refetch}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center"
          >
            {loading && (
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            {t('common.refresh', 'Refresh')}
          </button>
        </div>
      </div>

      {/* KPI卡片网格布局 - 2行3列 */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 w-full">
        {/* 第一行 */}
        
        {/* 卡片1: TOP排名 - 使用TopServersCard组件 */}
        <TopServersCard
          data={data.topServers}
          loading={loading}
          error={error}
        />

        {/* 卡片2: 工具生态健康度 - 使用ToolEcosystemCard组件 */}
        <ToolEcosystemCard
          data={data.toolEcosystem}
          loading={loading}
          error={error}
        />

        {/* 卡片3: 工具热度分析 - 使用ToolHeatmapCard组件 */}
        <ToolHeatmapCard
          data={data.toolHeatmap}
          loading={loading}
          error={error}
        />

        {/* 第二行 */}
        
        {/* 卡片4: 业务价值指数 - 使用BusinessValueCard组件 */}
        <BusinessValueCard
          data={data.businessValue}
          loading={loading}
          error={error}
        />

        {/* 卡片5: 工具分类分布 - 使用ToolCategoriesCard组件 */}
        <ToolCategoriesCard
          data={data.toolCategories}
          loading={loading}
          error={error}
        />

        {/* 卡片6: 调用效率分析 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 min-h-[360px]">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              ⚡ {t('analytics.overview.callEfficiency', 'Call Efficiency')}
            </h3>
          </div>
          <div className="text-center mb-4">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {data.callEfficiency.averageResponseTime}ms
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {t('analytics.overview.avgResponseTime', 'Average Response Time')}
            </div>
          </div>
          <div className="space-y-2">
            {data.callEfficiency.efficiencyDistribution.map((dist) => (
              <div key={dist.range} className="flex items-center justify-between text-sm">
                <span className="text-gray-900 dark:text-white">
                  {dist.range}
                </span>
                <div className="flex items-center space-x-2">
                  <span className="text-gray-600 dark:text-gray-400">
                    {dist.count}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-500">
                    ({dist.percentage}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 智能洞察面板 */}
      <InsightsPanel
        insights={data.insights}
        loading={loading}
        error={error}
      />
    </div>
  );
};

export default OverviewTab;
