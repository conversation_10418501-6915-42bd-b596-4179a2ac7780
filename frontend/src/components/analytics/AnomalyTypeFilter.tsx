import React from 'react';
import { useTranslation } from 'react-i18next';

interface AnomalyTypeFilterProps {
  selectedTypes: string[];
  onChange: (types: string[]) => void;
}

const AnomalyTypeFilter: React.FC<AnomalyTypeFilterProps> = ({
  selectedTypes,
  onChange,
}) => {
  const { t } = useTranslation();

  const anomalyTypes = [
    { id: 'spike', label: t('analytics.anomaly.type.spike', 'Spike'), color: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' },
    { id: 'drop', label: t('analytics.anomaly.type.drop', 'Drop'), color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' },
    { id: 'trend_break', label: t('analytics.anomaly.type.trend_break', 'Trend Break'), color: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400' },
    { id: 'outlier', label: t('analytics.anomaly.type.outlier', 'Outlier'), color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' },
    { id: 'volatility', label: t('analytics.anomaly.type.volatility', 'Volatility'), color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' },
  ];

  const toggleType = (typeId: string) => {
    if (selectedTypes.includes(typeId)) {
      onChange(selectedTypes.filter(id => id !== typeId));
    } else {
      onChange([...selectedTypes, typeId]);
    }
  };

  const selectAll = () => {
    onChange(anomalyTypes.map(type => type.id));
  };

  const clearAll = () => {
    onChange([]);
  };

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {t('analytics.anomaly.filterByType', 'Filter by Type')}
        </h4>
        <div className="space-x-2">
          <button
            onClick={selectAll}
            className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
          >
            {t('common.selectAll', 'Select All')}
          </button>
          <button
            onClick={clearAll}
            className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
          >
            {t('common.clearAll', 'Clear All')}
          </button>
        </div>
      </div>
      
      <div className="flex flex-wrap gap-2">
        {anomalyTypes.map(type => (
          <button
            key={type.id}
            onClick={() => toggleType(type.id)}
            className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
              selectedTypes.includes(type.id)
                ? type.color
                : 'bg-gray-100 text-gray-500 dark:bg-gray-700 dark:text-gray-400'
            }`}
          >
            {type.label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default AnomalyTypeFilter;
