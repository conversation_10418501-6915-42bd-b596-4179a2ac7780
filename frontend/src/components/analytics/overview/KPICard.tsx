import React from 'react';
import { Loader2, AlertCircle } from 'lucide-react';
import { KPICardProps } from '@/types/overview';
import styles from './KPICard.module.css';

/**
 * KPI卡片基础组件
 * 提供统一的卡片容器，支持加载状态、错误状态和自定义内容
 */
export const KPICard: React.FC<KPICardProps> = ({
  title,
  loading = false,
  error = null,
  className = '',
  children
}) => {
  // 渲染加载状态
  const renderLoadingState = () => (
    <div className={styles.loadingContainer}>
      <Loader2 className={styles.loadingIcon} />
      <span className={styles.loadingText}>加载中...</span>
    </div>
  );

  // 渲染错误状态
  const renderErrorState = () => (
    <div className={styles.errorContainer}>
      <AlertCircle className={styles.errorIcon} />
      <div className={styles.errorContent}>
        <h4 className={styles.errorTitle}>加载失败</h4>
        <p className={styles.errorMessage}>{error}</p>
      </div>
    </div>
  );

  return (
    <div className={`${styles.kpiCard} ${className}`}>
      {/* 卡片头部 */}
      <div className={styles.cardHeader}>
        <h3 className={styles.cardTitle}>{title}</h3>
      </div>

      {/* 卡片内容 */}
      <div className={styles.cardContent}>
        {loading ? renderLoadingState() : 
         error ? renderErrorState() : 
         children}
      </div>
    </div>
  );
};

export default KPICard;
