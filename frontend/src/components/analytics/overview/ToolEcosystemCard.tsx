import React from 'react';
import { useTranslation } from 'react-i18next';
import { Activity, Shield, AlertTriangle } from 'lucide-react';
import { ToolEcosystemData } from '@/types/overview';
import { KPICard } from './KPICard';
import { StatusIndicator } from './StatusIndicator';
import { HealthRingChart } from './HealthRingChart';
import { HealthDistribution } from './HealthDistribution';
import styles from './ToolEcosystemCard.module.css';

/**
 * 工具生态健康度卡片组件Props
 */
export interface ToolEcosystemCardProps {
  data?: ToolEcosystemData;
  loading?: boolean;
  error?: string | null;
  className?: string;
}

/**
 * 工具生态健康度卡片组件
 * 显示工具生态系统的整体健康状况，包含健康度评分、状态分布等信息
 */
export const ToolEcosystemCard: React.FC<ToolEcosystemCardProps> = ({
  data,
  loading = false,
  error = null,
  className = ''
}) => {
  const { t } = useTranslation();

  // 计算健康状态
  const getHealthStatus = () => {
    if (!data) return 'warning';
    
    const { healthScore } = data;
    if (healthScore >= 85) return 'healthy';
    if (healthScore >= 70) return 'warning';
    return 'critical';
  };

  // 计算健康度百分比
  const getHealthPercentage = () => {
    if (!data) return 0;
    return Math.round((data.healthyTools / data.totalTools) * 100);
  };

  // 计算问题工具总数
  const getProblemToolsCount = () => {
    if (!data) return 0;
    return data.problemTools + data.offlineTools;
  };

  return (
    <KPICard
      title={`🛡️ ${t('analytics.overview.toolEcosystem', '工具生态健康度')}`}
      loading={loading}
      error={error}
      className={`${styles.toolEcosystemCard} ${className}`}
    >
      {/* 卡片头部信息 */}
      <div className={styles.cardHeader}>
        <div className="flex items-center justify-between mb-4">
          <StatusIndicator 
            status={getHealthStatus()} 
            showLabel 
          />
          <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
            <Activity className="w-4 h-4" />
            <span>
              {t('analytics.overview.healthDistribution', '健康度分布')}
            </span>
          </div>
        </div>

        {/* 统计信息 */}
        <div className={styles.statsRow}>
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900 dark:text-white">
              {data?.healthScore?.toFixed(1) || '0.0'}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t('analytics.overview.overallScore', '综合评分')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900 dark:text-white">
              {data?.totalTools?.toLocaleString() || '0'}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t('analytics.overview.totalTools', '总工具数')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600 dark:text-green-400">
              {getHealthPercentage()}%
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t('analytics.overview.healthyTools', '健康工具')}
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区 */}
      <div className={styles.contentContainer}>
        {/* 健康度环形图 */}
        <div className={styles.chartSection}>
          <HealthRingChart
            healthScore={data?.healthScore || 0}
            size={120}
            strokeWidth={8}
            className={styles.healthChart}
          />
        </div>

        {/* 健康度分布详情 */}
        <div className={styles.distributionSection}>
          <HealthDistribution
            data={data}
            showDetails={true}
            className={styles.healthDistribution}
          />
        </div>
      </div>

      {/* 底部摘要信息 */}
      {data && getProblemToolsCount() > 0 && (
        <div className={styles.summarySection}>
          <div className="flex items-center space-x-2 text-sm text-amber-600 dark:text-amber-400">
            <AlertTriangle className="w-4 h-4" />
            <span>
              {t('analytics.overview.problemToolsAlert', '发现 {{count}} 个工具需要关注', {
                count: getProblemToolsCount()
              })}
            </span>
          </div>
        </div>
      )}
    </KPICard>
  );
};

export default ToolEcosystemCard;
