import React from 'react';
import { useTranslation } from 'react-i18next';
import { PieChart } from 'lucide-react';
import { ToolCategoryItem } from '@/types/overview';
import { KPICard } from './KPICard';
import { StatusIndicator } from './StatusIndicator';

/**
 * 工具分类分布卡片组件Props
 */
export interface ToolCategoriesCardProps {
  data?: ToolCategoryItem[];
  loading?: boolean;
  error?: string | null;
  className?: string;
}

/**
 * 分类饼图组件
 */
const CategoryPieChart: React.FC<{data: ToolCategoryItem[], totalTools: number}> = ({
  data,
  totalTools
}) => {
  const { t } = useTranslation();

  // 计算饼图路径
  const createPieSlice = (startAngle: number, endAngle: number, color: string) => {
    const centerX = 60;
    const centerY = 60;
    const radius = 45;
    
    const startAngleRad = (startAngle * Math.PI) / 180;
    const endAngleRad = (endAngle * Math.PI) / 180;
    
    const x1 = centerX + radius * Math.cos(startAngleRad);
    const y1 = centerY + radius * Math.sin(startAngleRad);
    const x2 = centerX + radius * Math.cos(endAngleRad);
    const y2 = centerY + radius * Math.sin(endAngleRad);
    
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
    
    return `M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;
  };

  // 生成饼图切片
  let currentAngle = 0;
  const slices = data.map((category, index) => {
    const sliceAngle = (category.percentage / 100) * 360;
    const path = createPieSlice(currentAngle, currentAngle + sliceAngle, category.color);
    currentAngle += sliceAngle;
    
    return (
      <path
        key={category.category}
        d={path}
        fill={category.color}
        stroke="var(--color-background)"
        strokeWidth="2"
        className="transition-opacity duration-200 cursor-pointer hover:opacity-80"
      />
    );
  });

  return (
    <div className="flex items-center justify-center">
      <div className="relative w-30 h-30">
        <svg width="120" height="120" viewBox="0 0 120 120" className="w-full h-full">
          {slices}
        </svg>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center pointer-events-none">
          <div className="text-2xl font-bold text-gray-900 dark:text-white leading-none">{data.length}</div>
          <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            {t('analytics.overview.toolCategories.categories')}
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * 分类列表组件
 */
const CategoryList: React.FC<{data: ToolCategoryItem[], totalTools: number}> = ({
  data,
  totalTools
}) => {
  const { t } = useTranslation();

  // 按数量排序
  const sortedData = [...data].sort((a, b) => b.count - a.count);

  return (
    <div className="flex flex-col h-full">
      <div className="mb-3">
        <h4 className="text-sm font-semibold text-gray-900 dark:text-white m-0">
          {t('analytics.overview.toolCategories.distribution')}
        </h4>
      </div>
      <div className="flex-1 overflow-y-auto max-h-56 pr-2">
        {sortedData.map((category, index) => (
          <div key={category.category} className="py-3 border-b border-gray-100 dark:border-gray-800 last:border-b-0">
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <div 
                  className="w-3 h-3 rounded-full flex-shrink-0"
                  style={{ backgroundColor: category.color }}
                />
                <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {t(`analytics.overview.toolCategories.categoryNames.${category.category}`, category.category)}
                </span>
              </div>
              <div className="flex items-center gap-2 flex-shrink-0">
                <span className="text-sm font-semibold text-gray-900 dark:text-white">
                  {category.count.toLocaleString()}
                </span>
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  ({category.percentage.toFixed(1)}%)
                </span>
              </div>
            </div>
            <div className="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-sm overflow-hidden">
              <div 
                className="h-full rounded-sm transition-all duration-300"
                style={{ 
                  width: `${category.percentage}%`,
                  backgroundColor: category.color
                }}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * 工具分类分布卡片组件
 * 显示工具按功能类型的分布情况
 */
export const ToolCategoriesCard: React.FC<ToolCategoriesCardProps> = ({
  data = [],
  loading = false,
  error = null,
  className = ''
}) => {
  const { t } = useTranslation();

  // 计算健康状态 - 基于分类多样性
  const getHealthStatus = () => {
    if (!data || data.length === 0) return 'warning';
    
    // 如果有5个或更多分类，认为是健康的
    if (data.length >= 5) return 'healthy';
    if (data.length >= 3) return 'warning';
    return 'critical';
  };

  // 计算总工具数
  const getTotalTools = () => {
    if (!data || data.length === 0) return 0;
    return data.reduce((total, category) => total + category.count, 0);
  };

  // 获取最大分类
  const getLargestCategory = () => {
    if (!data || data.length === 0) return null;
    return data.reduce((max, category) => 
      category.count > max.count ? category : max
    );
  };

  // 计算分类多样性指数 (基于分布均匀程度)
  const getDiversityIndex = () => {
    if (!data || data.length === 0) return 0;
    
    // 计算香农多样性指数的简化版本
    const total = getTotalTools();
    if (total === 0) return 0;
    
    let diversity = 0;
    data.forEach(category => {
      const proportion = category.count / total;
      if (proportion > 0) {
        diversity -= proportion * Math.log2(proportion);
      }
    });
    
    // 标准化到0-100范围
    const maxDiversity = Math.log2(data.length);
    return Math.round((diversity / maxDiversity) * 100);
  };

  const largestCategory = getLargestCategory();
  const diversityIndex = getDiversityIndex();

  return (
    <KPICard
      title={t('analytics.overview.toolCategories.title')}
      icon={<PieChart className="w-5 h-5" />}
      status={<StatusIndicator status={getHealthStatus()} />}
      subtitle={t('analytics.overview.toolCategories.subtitle')}
      loading={loading}
      error={error}
      className={className}
    >
      <div className="flex flex-col gap-4 h-full">
        {/* 统计信息行 */}
        <div className="grid grid-cols-4 gap-4 pb-3 border-b border-gray-200 dark:border-gray-700">
          <div className="text-center">
            <div className="text-xl font-semibold text-gray-900 dark:text-white mb-1">
              {getTotalTools().toLocaleString()}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400 leading-tight">
              {t('analytics.overview.toolCategories.totalTools')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xl font-semibold text-gray-900 dark:text-white mb-1">
              {data.length}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400 leading-tight">
              {t('analytics.overview.toolCategories.totalCategories')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xl font-semibold text-gray-900 dark:text-white mb-1">
              {largestCategory?.percentage?.toFixed(1) || '0.0'}%
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400 leading-tight">
              {t('analytics.overview.toolCategories.largestCategory')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xl font-semibold text-gray-900 dark:text-white mb-1">
              {diversityIndex}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400 leading-tight">
              {t('analytics.overview.toolCategories.diversityIndex')}
            </div>
          </div>
        </div>

        {/* 主要内容区 */}
        <div className="flex gap-6 flex-1 min-h-0">
          {/* 饼图区域 */}
          <div className="flex-none w-36 flex items-center justify-center">
            <CategoryPieChart 
              data={data}
              totalTools={getTotalTools()}
            />
          </div>

          {/* 分类列表区域 */}
          <div className="flex-1 min-w-0">
            <CategoryList 
              data={data}
              totalTools={getTotalTools()}
            />
          </div>
        </div>
      </div>
    </KPICard>
  );
};

export default ToolCategoriesCard;
