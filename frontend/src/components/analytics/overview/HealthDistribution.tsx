import React from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCircle, AlertTriangle, XCircle, Wifi } from 'lucide-react';
import { ToolEcosystemData } from '@/types/overview';

/**
 * 健康度分布组件Props
 */
export interface HealthDistributionProps {
  data?: ToolEcosystemData;
  showDetails?: boolean;
  className?: string;
}

/**
 * 健康度分布组件
 * 显示工具健康状态的详细分布信息
 */
export const HealthDistribution: React.FC<HealthDistributionProps> = ({
  data,
  showDetails = true,
  className = ''
}) => {
  const { t } = useTranslation();

  if (!data) {
    return (
      <div className={`text-center py-4 ${className}`}>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          暂无健康度数据
        </p>
      </div>
    );
  }

  // 健康状态配置
  const healthItems = [
    {
      key: 'healthy',
      label: t('analytics.overview.healthyTools', '健康工具'),
      count: data.healthyTools,
      icon: CheckCircle,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      percentage: Math.round((data.healthyTools / data.totalTools) * 100)
    },
    {
      key: 'problem',
      label: t('analytics.overview.problemTools', '问题工具'),
      count: data.problemTools,
      icon: AlertTriangle,
      color: 'text-amber-600 dark:text-amber-400',
      bgColor: 'bg-amber-50 dark:bg-amber-900/20',
      percentage: Math.round((data.problemTools / data.totalTools) * 100)
    },
    {
      key: 'offline',
      label: t('analytics.overview.offlineTools', '离线工具'),
      count: data.offlineTools,
      icon: XCircle,
      color: 'text-red-600 dark:text-red-400',
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      percentage: Math.round((data.offlineTools / data.totalTools) * 100)
    }
  ];

  return (
    <div className={`space-y-3 ${className}`}>
      {healthItems.map((item) => {
        const IconComponent = item.icon;
        
        return (
          <div 
            key={item.key}
            className="flex items-center justify-between group hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg p-2 -m-2 transition-colors duration-150"
          >
            {/* 左侧：图标和标签 */}
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <div className={`p-1.5 rounded-full ${item.bgColor}`}>
                <IconComponent className={`w-4 h-4 ${item.color}`} />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {item.label}
                </p>
                {showDetails && (
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {item.count.toLocaleString()} 个工具
                    </span>
                    <span className="text-xs text-gray-400">•</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {item.percentage}%
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* 右侧：数量显示 */}
            <div className="flex-shrink-0 ml-2">
              <div className={`text-lg font-bold ${item.color}`}>
                {item.count}
              </div>
            </div>
          </div>
        );
      })}

      {/* 总计信息 */}
      {showDetails && (
        <div className="pt-3 mt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
              <Wifi className="w-4 h-4" />
              <span>{t('analytics.overview.totalTools', '总工具数')}</span>
            </div>
            <div className="font-semibold text-gray-900 dark:text-white">
              {data.totalTools.toLocaleString()}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HealthDistribution;
