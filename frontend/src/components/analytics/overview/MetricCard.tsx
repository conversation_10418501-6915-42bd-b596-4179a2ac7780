import React from 'react';
import { TrendIndicator } from './TrendIndicator';

/**
 * 指标卡片组件Props
 */
export interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  trend?: {
    direction: 'up' | 'down' | 'stable';
    value: number;
  };
  color?: 'blue' | 'green' | 'purple' | 'orange';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * 指标卡片组件
 * 用于显示关键指标数据，支持趋势显示和多种颜色主题
 */
export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  color = 'blue',
  size = 'md',
  className = ''
}) => {
  // 获取颜色主题类名
  const getColorClasses = () => {
    switch (color) {
      case 'blue':
        return {
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          border: 'border-blue-200 dark:border-blue-800',
          icon: 'text-blue-600 dark:text-blue-400',
          value: 'text-blue-900 dark:text-blue-100'
        };
      case 'green':
        return {
          bg: 'bg-green-50 dark:bg-green-900/20',
          border: 'border-green-200 dark:border-green-800',
          icon: 'text-green-600 dark:text-green-400',
          value: 'text-green-900 dark:text-green-100'
        };
      case 'purple':
        return {
          bg: 'bg-purple-50 dark:bg-purple-900/20',
          border: 'border-purple-200 dark:border-purple-800',
          icon: 'text-purple-600 dark:text-purple-400',
          value: 'text-purple-900 dark:text-purple-100'
        };
      case 'orange':
        return {
          bg: 'bg-orange-50 dark:bg-orange-900/20',
          border: 'border-orange-200 dark:border-orange-800',
          icon: 'text-orange-600 dark:text-orange-400',
          value: 'text-orange-900 dark:text-orange-100'
        };
      default:
        return {
          bg: 'bg-gray-50 dark:bg-gray-900/20',
          border: 'border-gray-200 dark:border-gray-800',
          icon: 'text-gray-600 dark:text-gray-400',
          value: 'text-gray-900 dark:text-gray-100'
        };
    }
  };

  // 获取尺寸类名
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'p-3',
          value: 'text-lg',
          title: 'text-xs',
          subtitle: 'text-xs',
          icon: 'w-4 h-4'
        };
      case 'md':
        return {
          container: 'p-4',
          value: 'text-xl',
          title: 'text-sm',
          subtitle: 'text-xs',
          icon: 'w-5 h-5'
        };
      case 'lg':
        return {
          container: 'p-5',
          value: 'text-2xl',
          title: 'text-base',
          subtitle: 'text-sm',
          icon: 'w-6 h-6'
        };
      default:
        return {
          container: 'p-4',
          value: 'text-xl',
          title: 'text-sm',
          subtitle: 'text-xs',
          icon: 'w-5 h-5'
        };
    }
  };

  const colorClasses = getColorClasses();
  const sizeClasses = getSizeClasses();

  // 格式化数值显示
  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      return val.toLocaleString();
    }
    return val;
  };

  return (
    <div 
      className={`
        ${colorClasses.bg} 
        ${colorClasses.border} 
        ${sizeClasses.container}
        border rounded-lg transition-all duration-200 
        hover:shadow-md hover:-translate-y-0.5
        ${className}
      `}
    >
      {/* 头部：标题和图标 */}
      <div className="flex items-center justify-between mb-2">
        <h4 className={`${sizeClasses.title} font-medium text-gray-600 dark:text-gray-300`}>
          {title}
        </h4>
        {icon && (
          <div className={`${colorClasses.icon} ${sizeClasses.icon} flex-shrink-0`}>
            {icon}
          </div>
        )}
      </div>

      {/* 主要数值 */}
      <div className="flex items-end justify-between">
        <div className="flex-1">
          <div className={`${sizeClasses.value} font-bold ${colorClasses.value} leading-none`}>
            {formatValue(value)}
          </div>
          {subtitle && (
            <div className={`${sizeClasses.subtitle} text-gray-500 dark:text-gray-400 mt-1`}>
              {subtitle}
            </div>
          )}
        </div>

        {/* 趋势指示器 */}
        {trend && (
          <div className="flex-shrink-0 ml-2">
            <TrendIndicator
              trend={trend.direction}
              value={trend.value}
              showValue={true}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default MetricCard;
