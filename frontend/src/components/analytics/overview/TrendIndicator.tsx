import React from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

/**
 * 趋势指示器组件Props
 */
export interface TrendIndicatorProps {
  trend: 'up' | 'down' | 'stable';
  value: number;
  className?: string;
  showValue?: boolean;
}

/**
 * 趋势指示器组件
 * 显示趋势方向和数值变化
 */
export const TrendIndicator: React.FC<TrendIndicatorProps> = ({
  trend,
  value,
  className = '',
  showValue = true
}) => {
  // 根据趋势类型获取样式类名
  const getTrendStyles = () => {
    switch (trend) {
      case 'up':
        return 'text-green-600 dark:text-green-400';
      case 'down':
        return 'text-red-600 dark:text-red-400';
      case 'stable':
        return 'text-gray-500 dark:text-gray-400';
      default:
        return 'text-gray-500 dark:text-gray-400';
    }
  };

  // 根据趋势类型获取图标
  const getTrendIcon = () => {
    const iconProps = {
      className: 'w-3 h-3',
      'aria-hidden': true
    };

    switch (trend) {
      case 'up':
        return <TrendingUp {...iconProps} />;
      case 'down':
        return <TrendingDown {...iconProps} />;
      case 'stable':
        return <Minus {...iconProps} />;
      default:
        return <Minus {...iconProps} />;
    }
  };

  // 格式化数值显示
  const formatValue = (val: number) => {
    const absValue = Math.abs(val);
    const sign = trend === 'up' ? '+' : trend === 'down' ? '-' : '';
    return `${sign}${absValue.toFixed(1)}%`;
  };

  return (
    <div className={`flex items-center space-x-1 ${getTrendStyles()} ${className}`}>
      {getTrendIcon()}
      {showValue && (
        <span className="text-xs font-medium">
          {formatValue(value)}
        </span>
      )}
    </div>
  );
};

export default TrendIndicator;
