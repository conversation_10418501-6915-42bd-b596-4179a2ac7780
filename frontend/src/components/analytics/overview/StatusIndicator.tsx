import React from 'react';
import { StatusIndicatorProps } from '@/types/overview';

/**
 * 状态指示器组件
 * 支持健康、警告、严重三种状态，可配置尺寸和标签显示
 */
export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  size = 'md',
  showLabel = false,
  className = ''
}) => {
  // 获取状态对应的颜色类
  const getStatusColor = () => {
    switch (status) {
      case 'healthy':
        return 'bg-green-500 text-green-700';
      case 'warning':
        return 'bg-yellow-500 text-yellow-700';
      case 'critical':
        return 'bg-red-500 text-red-700';
      default:
        return 'bg-gray-500 text-gray-700';
    }
  };

  // 获取尺寸类
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'w-2 h-2';
      case 'md':
        return 'w-3 h-3';
      case 'lg':
        return 'w-4 h-4';
      default:
        return 'w-3 h-3';
    }
  };

  // 获取状态标签
  const getStatusLabel = () => {
    switch (status) {
      case 'healthy':
        return '健康';
      case 'warning':
        return '警告';
      case 'critical':
        return '严重';
      default:
        return '未知';
    }
  };

  // 获取标签文字尺寸
  const getLabelSize = () => {
    switch (size) {
      case 'sm':
        return 'text-xs';
      case 'md':
        return 'text-sm';
      case 'lg':
        return 'text-base';
      default:
        return 'text-sm';
    }
  };

  const colorClass = getStatusColor();
  const sizeClass = getSizeClass();
  const labelSizeClass = getLabelSize();

  if (showLabel) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div 
          className={`rounded-full ${sizeClass} ${colorClass}`}
          aria-label={`状态: ${getStatusLabel()}`}
        />
        <span className={`font-medium ${labelSizeClass} ${colorClass.split(' ')[1]}`}>
          {getStatusLabel()}
        </span>
      </div>
    );
  }

  return (
    <div 
      className={`rounded-full ${sizeClass} ${colorClass} ${className}`}
      title={`状态: ${getStatusLabel()}`}
      aria-label={`状态: ${getStatusLabel()}`}
    />
  );
};

export default StatusIndicator;
