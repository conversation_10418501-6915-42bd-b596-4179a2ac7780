import React from 'react';
import { useTranslation } from 'react-i18next';
import { Trophy, Server, Activity, TrendingUp } from 'lucide-react';
import { TopServerItem } from '@/types/overview';
import { KPICard } from './KPICard';
import { MetricCard } from './MetricCard';
import { ServerRankingItem } from './ServerRankingItem';
import styles from './TopServersCard.module.css';

/**
 * TOP服务器排名卡片组件Props
 */
export interface TopServersCardProps {
  data?: TopServerItem[];
  loading?: boolean;
  error?: string | null;
  className?: string;
}

/**
 * TOP服务器排名卡片组件
 * 显示MCP服务器使用排名TOP 5
 */
export const TopServersCard: React.FC<TopServersCardProps> = ({
  data = [],
  loading = false,
  error = null,
  className = ''
}) => {
  const { t } = useTranslation();

  // 计算总调用次数
  const getTotalCalls = () => {
    if (!data || data.length === 0) return 0;
    return data.reduce((total, server) => total + server.callCount, 0);
  };

  // 计算活跃服务器数量
  const getActiveServersCount = () => {
    return data.length;
  };

  // 计算TOP5服务器占总调用量的百分比
  const getTop5Percentage = () => {
    const totalCalls = getTotalCalls();
    if (totalCalls === 0) return 0;
    const top5Calls = data.slice(0, 5).reduce((sum, server) => sum + server.callCount, 0);
    return (top5Calls / totalCalls) * 100;
  };

  // 计算最大调用次数（用于进度条）
  const getMaxCallCount = () => {
    if (!data || data.length === 0) return 0;
    return Math.max(...data.map(server => server.callCount));
  };

  // 计算总体趋势
  const getOverallTrend = () => {
    if (!data || data.length === 0) return { direction: 'stable' as const, value: 0 };

    const upCount = data.filter(server => server.trend === 'up').length;
    const downCount = data.filter(server => server.trend === 'down').length;

    if (upCount > downCount) {
      return { direction: 'up' as const, value: ((upCount - downCount) / data.length) * 100 };
    } else if (downCount > upCount) {
      return { direction: 'down' as const, value: ((downCount - upCount) / data.length) * 100 };
    }
    return { direction: 'stable' as const, value: 0 };
  };

  return (
    <KPICard
      title={`🏆 ${t('analytics.overview.topActiveServers', '最活跃服务器排名')}`}
      loading={loading}
      error={error}
      className={`${styles.topServersCard} ${className}`}
    >
      {/* 卡片头部：时间范围说明 */}
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {t('analytics.overview.timeRange', '过去24小时数据')}
          </p>
          <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
            <Trophy className="w-3 h-3" />
            <span>TOP 5</span>
          </div>
        </div>
      </div>

      {/* 关键指标区域 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-6">
        {/* 总调用次数 */}
        <MetricCard
          title={t('analytics.overview.totalCalls', '总调用次数')}
          value={getTotalCalls()}
          icon={<Activity />}
          trend={getOverallTrend()}
          color="blue"
          size="md"
        />

        {/* 活跃服务器数 */}
        <MetricCard
          title={t('analytics.overview.activeServers', '活跃服务器')}
          value={getActiveServersCount()}
          subtitle={t('analytics.overview.serversUnit', '个服务器')}
          icon={<Server />}
          color="green"
          size="md"
        />

        {/* TOP5占比 */}
        <MetricCard
          title={t('analytics.overview.top5Share', 'TOP5占比')}
          value={`${getTop5Percentage().toFixed(1)}%`}
          subtitle={t('analytics.overview.ofTotalCalls', '占总调用量')}
          icon={<TrendingUp />}
          color="purple"
          size="md"
        />
      </div>

      {/* 排名列表 */}
      <div className={styles.rankingContainer}>
        <div className="space-y-3">
          {data.slice(0, 5).map((server) => (
            <ServerRankingItem
              key={server.serverName}
              server={server}
              maxCallCount={getMaxCallCount()}
              showProgress={true}
              showTrend={true}
              showPercentage={true}
            />
          ))}
        </div>

        {data.length === 0 && (
          <div className="text-center py-8">
            <Server className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t('analytics.overview.noServerData', '暂无服务器数据')}
            </p>
          </div>
        )}
      </div>
    </KPICard>
  );
};

export default TopServersCard;
