import React from 'react';
import { useTranslation } from 'react-i18next';
import { ToolHeatmapData } from '@/types/overview';
import { HeatIndicator } from './HeatIndicator';

/**
 * 热度列表组件Props
 */
export interface HeatmapListProps {
  data?: ToolHeatmapData;
  maxItems?: number;
  showPercentage?: boolean;
  className?: string;
}

/**
 * 热度列表组件
 * 显示工具热度排名列表，包含热门和冷门工具
 */
export const HeatmapList: React.FC<HeatmapListProps> = ({
  data,
  maxItems = 10,
  showPercentage = true,
  className = ''
}) => {
  const { t } = useTranslation();

  if (!data) {
    return (
      <div className={`text-center text-gray-500 dark:text-gray-400 py-4 ${className}`}>
        {t('analytics.overview.noData', '暂无数据')}
      </div>
    );
  }

  // 合并热门和冷门工具，按调用次数排序
  const allTools = [
    ...data.hotTools.map(tool => ({ ...tool, heatLevel: 'hot' as const })),
    ...data.coldTools.map(tool => ({ ...tool, heatLevel: 'cold' as const }))
  ].sort((a, b) => b.callCount - a.callCount).slice(0, maxItems);

  // 从heatMatrix中获取温热工具
  const warmTools = data.heatMatrix
    .filter(tool => tool.heatLevel === 'warm')
    .map(tool => ({
      name: tool.toolName,
      callCount: tool.callFrequency,
      percentage: 0, // 需要计算
      heatLevel: 'warm' as const
    }));

  // 合并所有工具并重新排序
  const combinedTools = [
    ...allTools,
    ...warmTools
  ].sort((a, b) => b.callCount - a.callCount).slice(0, maxItems);

  // 重新计算百分比
  const totalCalls = combinedTools.reduce((sum, tool) => sum + tool.callCount, 0);
  const toolsWithPercentage = combinedTools.map(tool => ({
    ...tool,
    percentage: totalCalls > 0 ? (tool.callCount / totalCalls) * 100 : 0
  }));

  return (
    <div className={`space-y-2 ${className}`}>
      {/* 列表头部 */}
      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 px-1">
        <span>{t('analytics.overview.toolName', '工具名称')}</span>
        <span>{t('analytics.overview.callCount', '调用次数')}</span>
      </div>

      {/* 工具列表 */}
      <div className="space-y-1">
        {toolsWithPercentage.map((tool, index) => (
          <div
            key={`${tool.name}-${index}`}
            className="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
          >
            {/* 左侧：热度指示器 + 工具名称 */}
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <HeatIndicator level={tool.heatLevel} size="sm" />
              
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {tool.name}
                </div>
                {showPercentage && (
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {tool.percentage.toFixed(1)}% {t('analytics.overview.ofTotal', '占比')}
                  </div>
                )}
              </div>
            </div>

            {/* 右侧：调用次数 */}
            <div className="text-right">
              <div className="text-sm font-bold text-gray-900 dark:text-white">
                {tool.callCount.toLocaleString()}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {t('analytics.overview.calls', '次调用')}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 如果没有数据 */}
      {toolsWithPercentage.length === 0 && (
        <div className="text-center text-gray-500 dark:text-gray-400 py-8">
          <div className="text-sm">
            {t('analytics.overview.noToolData', '暂无工具数据')}
          </div>
          <div className="text-xs mt-1">
            {t('analytics.overview.noToolDataDesc', '请稍后再试或检查数据源')}
          </div>
        </div>
      )}

      {/* 显示更多提示 */}
      {data.heatMatrix.length > maxItems && (
        <div className="text-center pt-2">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {t('analytics.overview.showingTopTools', '显示前 {{count}} 个工具', { count: maxItems })}
          </div>
          <div className="text-xs text-gray-400 dark:text-gray-500">
            {t('analytics.overview.totalToolsAvailable', '共 {{total}} 个工具', { total: data.heatMatrix.length })}
          </div>
        </div>
      )}
    </div>
  );
};

export default HeatmapList;
