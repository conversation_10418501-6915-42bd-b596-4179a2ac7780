/* 工具生态健康度卡片样式 */

.toolEcosystemCard {
  /* 继承KPICard的基础样式 */
}

.cardHeader {
  margin-bottom: 1rem;
}

.statsRow {
  display: flex;
  justify-content: space-around;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
  margin-bottom: 1rem;
}

/* 深色模式下的分割线 */
:global(.dark) .statsRow {
  border-bottom-color: rgba(75, 85, 99, 0.8);
}

.contentContainer {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  flex: 1;
  overflow-y: auto;
  max-height: 220px;
}

.chartSection {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 0;
}

.healthChart {
  /* 健康度环形图样式 */
}

.distributionSection {
  flex: 1;
  min-height: 0;
}

.healthDistribution {
  /* 健康度分布组件样式 */
}

.summarySection {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(229, 231, 235, 0.8);
}

/* 深色模式下的分割线 */
:global(.dark) .summarySection {
  border-top-color: rgba(75, 85, 99, 0.8);
}

/* 滚动条样式优化 */
.contentContainer::-webkit-scrollbar {
  width: 4px;
}

.contentContainer::-webkit-scrollbar-track {
  background: rgba(229, 231, 235, 0.3);
  border-radius: 2px;
}

.contentContainer::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.contentContainer::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* 深色模式下的滚动条 */
:global(.dark) .contentContainer::-webkit-scrollbar-track {
  background: rgba(75, 85, 99, 0.3);
}

:global(.dark) .contentContainer::-webkit-scrollbar-thumb {
  background: rgba(107, 114, 128, 0.5);
}

:global(.dark) .contentContainer::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .statsRow {
    padding: 0.5rem 0;
  }
  
  .contentContainer {
    max-height: 180px;
    gap: 1rem;
  }
  
  .chartSection {
    padding: 0.5rem 0;
  }
}

/* 大屏幕优化 */
@media (min-width: 1024px) {
  .contentContainer {
    flex-direction: row;
    align-items: flex-start;
    max-height: 200px;
  }
  
  .chartSection {
    flex-shrink: 0;
    width: 140px;
  }
  
  .distributionSection {
    flex: 1;
    min-width: 0;
  }
}
