import React from 'react';

/**
 * 进度条组件Props
 */
export interface ProgressBarProps {
  value: number;
  max?: number;
  height?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'gradient';
  showLabel?: boolean;
  label?: string;
  className?: string;
  animated?: boolean;
}

/**
 * 进度条组件
 * 用于显示数据的相对比例，支持多种颜色和动画效果
 */
export const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  height = 'md',
  color = 'blue',
  showLabel = false,
  label,
  className = '',
  animated = true
}) => {
  // 计算百分比
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

  // 获取高度类名
  const getHeightClass = () => {
    switch (height) {
      case 'sm':
        return 'h-1.5';
      case 'md':
        return 'h-2';
      case 'lg':
        return 'h-3';
      default:
        return 'h-2';
    }
  };

  // 获取颜色类名
  const getColorClasses = () => {
    switch (color) {
      case 'blue':
        return {
          bg: 'bg-blue-500',
          gradient: 'from-blue-400 to-blue-600'
        };
      case 'green':
        return {
          bg: 'bg-green-500',
          gradient: 'from-green-400 to-green-600'
        };
      case 'purple':
        return {
          bg: 'bg-purple-500',
          gradient: 'from-purple-400 to-purple-600'
        };
      case 'orange':
        return {
          bg: 'bg-orange-500',
          gradient: 'from-orange-400 to-orange-600'
        };
      case 'gradient':
        return {
          bg: 'bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500',
          gradient: 'from-blue-400 via-purple-400 to-pink-400'
        };
      default:
        return {
          bg: 'bg-gray-500',
          gradient: 'from-gray-400 to-gray-600'
        };
    }
  };

  const heightClass = getHeightClass();
  const colorClasses = getColorClasses();

  return (
    <div className={`w-full ${className}`}>
      {/* 标签 */}
      {showLabel && (
        <div className="flex justify-between items-center mb-1">
          <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
            {label || `${percentage.toFixed(1)}%`}
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {value.toLocaleString()} / {max.toLocaleString()}
          </span>
        </div>
      )}

      {/* 进度条容器 */}
      <div className={`w-full bg-gray-200 dark:bg-gray-700 rounded-full ${heightClass} overflow-hidden`}>
        {/* 进度条填充 */}
        <div
          className={`
            ${heightClass} 
            ${color === 'gradient' ? 'bg-gradient-to-r' : ''} 
            ${color === 'gradient' ? colorClasses.gradient : colorClasses.bg}
            rounded-full transition-all duration-500 ease-out
            ${animated ? 'animate-pulse' : ''}
          `}
          style={{ width: `${percentage}%` }}
        >
          {/* 光泽效果 */}
          <div className="h-full w-full bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full" />
        </div>
      </div>

      {/* 百分比显示（仅在不显示标签时） */}
      {!showLabel && (
        <div className="text-right mt-1">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {percentage.toFixed(1)}%
          </span>
        </div>
      )}
    </div>
  );
};

export default ProgressBar;
