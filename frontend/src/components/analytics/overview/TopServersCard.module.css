/* TOP服务器排名卡片样式 */

.topServersCard {
  /* 继承KPICard的基础样式 */
}

.cardHeader {
  margin-bottom: 1rem;
}

.statsRow {
  display: flex;
  justify-content: space-around;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
  margin-bottom: 1rem;
}

/* 深色模式下的分割线 */
:global(.dark) .statsRow {
  border-bottom-color: rgba(75, 85, 99, 0.8);
}

.rankingContainer {
  flex: 1;
  overflow-y: auto;
  max-height: 280px; /* 增加高度以适应新的排名项设计 */
}

.rankingList {
  /* RankingList组件的容器样式 */
}

/* 滚动条样式优化 */
.rankingContainer::-webkit-scrollbar {
  width: 4px;
}

.rankingContainer::-webkit-scrollbar-track {
  background: rgba(229, 231, 235, 0.3);
  border-radius: 2px;
}

.rankingContainer::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.rankingContainer::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .topServersCard {
    min-height: 420px; /* 移动端增加高度 */
  }

  .rankingContainer {
    max-height: 240px;
  }
}

@media (max-width: 640px) {
  .topServersCard {
    min-height: 480px; /* 小屏幕进一步增加高度 */
  }
}

.rankingContainer::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* 深色模式下的滚动条 */
:global(.dark) .rankingContainer::-webkit-scrollbar-track {
  background: rgba(75, 85, 99, 0.3);
}

:global(.dark) .rankingContainer::-webkit-scrollbar-thumb {
  background: rgba(107, 114, 128, 0.5);
}

:global(.dark) .rankingContainer::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .statsRow {
    padding: 0.5rem 0;
  }
  
  .rankingContainer {
    max-height: 120px;
  }
}
