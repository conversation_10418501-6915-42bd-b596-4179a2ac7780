import React from 'react';
import { TopServersCard } from './TopServersCard';
import { TopServerItem } from '@/types/overview';

/**
 * TOP服务器卡片演示组件
 * 用于展示优化后的TOP服务器卡片效果
 */
export const TopServersCardDemo: React.FC = () => {
  // 模拟数据
  const mockData: TopServerItem[] = [
    {
      rank: 1,
      serverName: 'context7',
      callCount: 17,
      percentage: 12.1,
      trend: 'up',
      trendValue: 5.2
    },
    {
      rank: 2,
      serverName: 'playwright',
      callCount: 17,
      percentage: 12.1,
      trend: 'stable',
      trendValue: 0
    },
    {
      rank: 3,
      serverName: 'image',
      callCount: 7,
      percentage: 5.0,
      trend: 'down',
      trendValue: -2.1
    },
    {
      rank: 4,
      serverName: 'fetch',
      callCount: 7,
      percentage: 5.0,
      trend: 'up',
      trendValue: 1.8
    },
    {
      rank: 5,
      serverName: 'database',
      callCount: 5,
      percentage: 3.6,
      trend: 'stable',
      trendValue: 0.2
    }
  ];

  return (
    <div className="p-6 bg-gray-100 dark:bg-gray-900 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          TOP服务器卡片优化演示
        </h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 优化后的版本 */}
          <div>
            <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
              优化后版本
            </h2>
            <TopServersCard 
              data={mockData}
              loading={false}
              error={null}
            />
          </div>
          
          {/* 加载状态演示 */}
          <div>
            <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
              加载状态
            </h2>
            <TopServersCard 
              data={[]}
              loading={true}
              error={null}
            />
          </div>
        </div>

        {/* 空数据状态演示 */}
        <div className="mt-6">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
            空数据状态
          </h2>
          <div className="max-w-md">
            <TopServersCard 
              data={[]}
              loading={false}
              error={null}
            />
          </div>
        </div>

        {/* 错误状态演示 */}
        <div className="mt-6">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
            错误状态
          </h2>
          <div className="max-w-md">
            <TopServersCard 
              data={[]}
              loading={false}
              error="网络连接失败，请稍后重试"
            />
          </div>
        </div>

        {/* 设计说明 */}
        <div className="mt-8 p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
            优化说明
          </h2>
          <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <div>
              <strong className="text-gray-800 dark:text-gray-200">1. 明确显示目标：</strong>
              标题改为"最活跃服务器排名"，添加时间范围说明
            </div>
            <div>
              <strong className="text-gray-800 dark:text-gray-200">2. 优化数据展示：</strong>
              使用MetricCard展示关键指标，添加进度条可视化
            </div>
            <div>
              <strong className="text-gray-800 dark:text-gray-200">3. 改进布局设计：</strong>
              重新设计信息层次，使用颜色编码区分排名
            </div>
            <div>
              <strong className="text-gray-800 dark:text-gray-200">4. 增强交互体验：</strong>
              添加hover效果，优化响应式设计
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopServersCardDemo;
