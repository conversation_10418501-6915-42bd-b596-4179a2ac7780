import React from 'react';
import { useTranslation } from 'react-i18next';
import styles from './BusinessValueCard.module.css';

/**
 * 价值评分组件Props
 */
export interface ValueScoreProps {
  score: number;
  highValueTools: number;
  mediumValueTools: number;
  lowValueTools: number;
}

/**
 * 价值评分组件
 * 显示业务价值评分的可视化表示
 */
export const ValueScore: React.FC<ValueScoreProps> = ({
  score,
  highValueTools,
  mediumValueTools,
  lowValueTools
}) => {
  const { t } = useTranslation();

  // 计算总工具数
  const totalTools = highValueTools + mediumValueTools + lowValueTools;

  // 计算各级别占比
  const highPercentage = totalTools > 0 ? Math.round((highValueTools / totalTools) * 100) : 0;
  const mediumPercentage = totalTools > 0 ? Math.round((mediumValueTools / totalTools) * 100) : 0;
  const lowPercentage = totalTools > 0 ? Math.round((lowValueTools / totalTools) * 100) : 0;

  // 获取评分颜色
  const getScoreColor = (score: number) => {
    if (score >= 80) return '#10B981'; // 绿色
    if (score >= 60) return '#F59E0B'; // 橙色
    return '#EF4444'; // 红色
  };

  // 获取评分等级描述
  const getScoreDescription = (score: number) => {
    if (score >= 90) return t('analytics.overview.businessValue.excellent');
    if (score >= 80) return t('analytics.overview.businessValue.good');
    if (score >= 70) return t('analytics.overview.businessValue.average');
    if (score >= 60) return t('analytics.overview.businessValue.poor');
    return t('analytics.overview.businessValue.critical');
  };

  return (
    <div className={styles.valueScore}>
      {/* 评分圆环 */}
      <div className={styles.scoreRing}>
        <svg width="120" height="120" viewBox="0 0 120 120" className={styles.scoreSvg}>
          {/* 背景圆环 */}
          <circle
            cx="60"
            cy="60"
            r="50"
            fill="none"
            stroke="var(--color-border)"
            strokeWidth="8"
          />
          {/* 评分圆环 */}
          <circle
            cx="60"
            cy="60"
            r="50"
            fill="none"
            stroke={getScoreColor(score)}
            strokeWidth="8"
            strokeLinecap="round"
            strokeDasharray={`${(score / 100) * 314.16} 314.16`}
            strokeDashoffset="78.54"
            className={styles.scoreCircle}
          />
        </svg>
        <div className={styles.scoreText}>
          <div className={styles.scoreNumber}>{score.toFixed(1)}</div>
          <div className={styles.scoreUnit}>分</div>
        </div>
      </div>

      {/* 评分详情 */}
      <div className={styles.scoreDetails}>
        <div className={styles.scoreDescription}>
          {getScoreDescription(score)}
        </div>
        
        {/* 价值分级统计 */}
        <div className={styles.valueBreakdown}>
          <div className={styles.valueItem}>
            <div className={styles.valueIndicator} style={{ backgroundColor: '#10B981' }}></div>
            <span className={styles.valueLabel}>
              {t('analytics.overview.businessValue.highValue')}
            </span>
            <span className={styles.valueCount}>
              {highValueTools} ({highPercentage}%)
            </span>
          </div>
          
          <div className={styles.valueItem}>
            <div className={styles.valueIndicator} style={{ backgroundColor: '#F59E0B' }}></div>
            <span className={styles.valueLabel}>
              {t('analytics.overview.businessValue.mediumValue')}
            </span>
            <span className={styles.valueCount}>
              {mediumValueTools} ({mediumPercentage}%)
            </span>
          </div>
          
          <div className={styles.valueItem}>
            <div className={styles.valueIndicator} style={{ backgroundColor: '#EF4444' }}></div>
            <span className={styles.valueLabel}>
              {t('analytics.overview.businessValue.lowValue')}
            </span>
            <span className={styles.valueCount}>
              {lowValueTools} ({lowPercentage}%)
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ValueScore;
