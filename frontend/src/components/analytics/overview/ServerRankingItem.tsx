import React from 'react';
import { TopServerItem } from '@/types/overview';
import { TrendIndicator } from './TrendIndicator';
import { ProgressBar } from './ProgressBar';

/**
 * 服务器排名项组件Props
 */
export interface ServerRankingItemProps {
  server: TopServerItem;
  maxCallCount: number;
  showProgress?: boolean;
  showTrend?: boolean;
  showPercentage?: boolean;
  className?: string;
}

/**
 * 服务器排名项组件
 * 显示单个服务器的排名信息，包括进度条、趋势和详细数据
 */
export const ServerRankingItem: React.FC<ServerRankingItemProps> = ({
  server,
  maxCallCount,
  showProgress = true,
  showTrend = true,
  showPercentage = true,
  className = ''
}) => {
  // 获取排名图标
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return (
          <span className="flex items-center justify-center w-6 h-6 text-xs font-bold text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-full">
            {rank}
          </span>
        );
    }
  };

  // 获取排名对应的颜色主题
  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'gradient'; // 金色渐变
      case 2:
        return 'blue'; // 银色（用蓝色代替）
      case 3:
        return 'orange'; // 铜色
      default:
        return 'purple'; // 其他排名
    }
  };

  // 获取排名对应的背景样式
  const getRankBackground = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border-yellow-200 dark:border-yellow-800';
      case 2:
        return 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-800';
      case 3:
        return 'bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border-orange-200 dark:border-orange-800';
      default:
        return 'bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700';
    }
  };

  // 计算相对于最高调用量的百分比
  const relativePercentage = maxCallCount > 0 ? (server.callCount / maxCallCount) * 100 : 0;

  return (
    <div 
      className={`
        ${getRankBackground(server.rank)}
        border rounded-lg p-3 transition-all duration-200 
        hover:shadow-md hover:-translate-y-0.5 hover:scale-[1.02]
        ${className}
      `}
    >
      {/* 顶部：排名图标、服务器名称和趋势 */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          {/* 排名图标 */}
          <div className="text-lg flex-shrink-0">
            {getRankIcon(server.rank)}
          </div>
          
          {/* 服务器名称 */}
          <div className="min-w-0 flex-1">
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white truncate">
              {server.serverName}
            </h4>
          </div>
        </div>

        {/* 趋势指示器 */}
        {showTrend && (
          <div className="flex-shrink-0 ml-2">
            <TrendIndicator
              trend={server.trend}
              value={server.trendValue}
              showValue={true}
            />
          </div>
        )}
      </div>

      {/* 中部：调用次数和百分比 */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-lg font-bold text-gray-900 dark:text-white">
            {server.callCount.toLocaleString()}
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            次调用
          </span>
        </div>
        
        {showPercentage && (
          <div className="text-right">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {server.percentage.toFixed(1)}%
            </span>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              占总量
            </div>
          </div>
        )}
      </div>

      {/* 底部：进度条 */}
      {showProgress && (
        <div className="mt-2">
          <ProgressBar
            value={relativePercentage}
            max={100}
            height="sm"
            color={getRankColor(server.rank)}
            animated={false}
            className="w-full"
          />
        </div>
      )}
    </div>
  );
};

export default ServerRankingItem;
