/* 工具热度分析卡片样式 */

.toolHeatmapCard {
  /* 继承KPICard的基础样式 */
}

.cardHeader {
  margin-bottom: 1rem;
}

.statsRow {
  display: flex;
  justify-content: space-around;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
  margin-bottom: 1rem;
}

/* 深色模式下的分割线 */
:global(.dark) .statsRow {
  border-bottom-color: rgba(75, 85, 99, 0.8);
}

.contentContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow: hidden;
}

.heatOverview {
  flex-shrink: 0;
}

.heatmapContainer {
  flex: 1;
  overflow-y: auto;
  max-height: 220px; /* 增加高度以显示更多热度信息 */
}

.heatmapList {
  /* HeatmapList组件的容器样式 */
}

/* 滚动条样式优化 */
.heatmapContainer::-webkit-scrollbar {
  width: 4px;
}

.heatmapContainer::-webkit-scrollbar-track {
  background: rgba(229, 231, 235, 0.3);
  border-radius: 2px;
}

.heatmapContainer::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.heatmapContainer::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* 深色模式下的滚动条 */
:global(.dark) .heatmapContainer::-webkit-scrollbar-track {
  background: rgba(75, 85, 99, 0.3);
}

:global(.dark) .heatmapContainer::-webkit-scrollbar-thumb {
  background: rgba(107, 114, 128, 0.5);
}

:global(.dark) .heatmapContainer::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .statsRow {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .contentContainer {
    gap: 0.75rem;
  }
  
  .heatmapContainer {
    max-height: 180px;
  }
}

/* 热度分布概览样式 */
.heatOverview {
  padding: 0.75rem;
  background: rgba(249, 250, 251, 0.5);
  border-radius: 0.5rem;
  border: 1px solid rgba(229, 231, 235, 0.8);
}

:global(.dark) .heatOverview {
  background: rgba(31, 41, 55, 0.5);
  border-color: rgba(75, 85, 99, 0.8);
}

/* 悬停效果 */
.toolHeatmapCard:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:global(.dark) .toolHeatmapCard:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 过渡动画 */
.toolHeatmapCard {
  transition: all 0.2s ease-in-out;
}

.heatOverview {
  transition: background-color 0.2s ease-in-out;
}
