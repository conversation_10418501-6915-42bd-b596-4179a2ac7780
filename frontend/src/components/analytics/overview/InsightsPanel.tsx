import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { InsightsData, TrendInsight, WarningInsight, RecommendationInsight } from '../../../types/overview';

interface InsightsPanelProps {
  insights: InsightsData;
  loading?: boolean;
  error?: string | null;
}

/**
 * Overview页面专用的智能洞察面板组件
 * 显示最重要的洞察信息，包括趋势、警告和建议
 */
const InsightsPanel: React.FC<InsightsPanelProps> = ({ insights, loading, error }) => {
  const { t } = useTranslation();

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 max-w-[1200px] mx-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            🧠 {t('analytics.overview.insights', '智能洞察')}
          </h3>
        </div>
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded mb-1"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 max-w-[1200px] mx-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            🧠 {t('analytics.overview.insights', '智能洞察')}
          </h3>
        </div>
        <div className="text-center py-8">
          <div className="text-red-500 dark:text-red-400 mb-2">
            ⚠️ {t('analytics.overview.insightsError', '洞察数据加载失败')}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {error}
          </div>
        </div>
      </div>
    );
  }

  // 合并所有洞察并按优先级排序
  const allInsights = [
    ...insights.trends.map(insight => ({ ...insight, category: 'trend' as const })),
    ...insights.warnings.map(insight => ({ ...insight, category: 'warning' as const })),
    ...insights.recommendations.map(insight => ({ ...insight, category: 'recommendation' as const }))
  ].sort((a, b) => {
    // 按严重程度排序
    const severityOrder = { critical: 3, warning: 2, info: 1 };
    const severityDiff = (severityOrder[a.severity] || 0) - (severityOrder[b.severity] || 0);
    if (severityDiff !== 0) return -severityDiff;
    
    // 按置信度排序
    return (b.confidence || 0) - (a.confidence || 0);
  }).slice(0, 3); // 只显示前3个最重要的洞察

  return (
    <div className="w-full">
      {/* 智能洞察标题 */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
          🧠 {t('analytics.overview.insights', '智能洞察')}
        </h3>
        <Link
          to="/analytics/insights"
          className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
        >
          {t('analytics.overview.viewAllInsights', '查看全部洞察')} →
        </Link>
      </div>

      {allInsights.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-8 text-center">
          <div className="text-gray-500 dark:text-gray-400 mb-2">
            💡 {t('analytics.overview.noInsights', '暂无重要洞察')}
          </div>
          <div className="text-sm text-gray-400 dark:text-gray-500">
            {t('analytics.overview.noInsightsDesc', '系统运行正常，没有发现需要关注的问题')}
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 w-full">
          {allInsights.map((insight) => (
            <InsightCard key={insight.id} insight={insight} />
          ))}
        </div>
      )}
    </div>
  );
};

/**
 * 洞察卡片组件
 */
interface InsightCardProps {
  insight: (TrendInsight | WarningInsight | RecommendationInsight) & { category: 'trend' | 'warning' | 'recommendation' };
}

const InsightCard: React.FC<InsightCardProps> = ({ insight }) => {
  const { t } = useTranslation();

  const getInsightIcon = (category: string, severity: string) => {
    if (category === 'trend') {
      return severity === 'success' ? '📈' : severity === 'warning' ? '📊' : '🔥';
    }
    if (category === 'warning') {
      return severity === 'critical' ? '🚨' : '⚠️';
    }
    if (category === 'recommendation') return '💡';
    return '📊';
  };

  const getSeverityTheme = (severity: string) => {
    switch (severity) {
      case 'critical':
        return {
          bg: 'bg-red-50 dark:bg-red-900/10',
          border: 'border-red-200 dark:border-red-800',
          accent: 'bg-red-500',
          text: 'text-red-700 dark:text-red-300',
          badge: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
        };
      case 'warning':
        return {
          bg: 'bg-yellow-50 dark:bg-yellow-900/10',
          border: 'border-yellow-200 dark:border-yellow-800',
          accent: 'bg-yellow-500',
          text: 'text-yellow-700 dark:text-yellow-300',
          badge: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
        };
      case 'success':
        return {
          bg: 'bg-green-50 dark:bg-green-900/10',
          border: 'border-green-200 dark:border-green-800',
          accent: 'bg-green-500',
          text: 'text-green-700 dark:text-green-300',
          badge: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
        };
      case 'info':
      default:
        return {
          bg: 'bg-blue-50 dark:bg-blue-900/10',
          border: 'border-blue-200 dark:border-blue-800',
          accent: 'bg-blue-500',
          text: 'text-blue-700 dark:text-blue-300',
          badge: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
        };
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'immediate':
        return 'text-red-600 dark:text-red-400';
      case 'soon':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'monitor':
      default:
        return 'text-blue-600 dark:text-blue-400';
    }
  };



  const theme = getSeverityTheme(insight.severity);

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border ${theme.border} min-h-[400px] transition-all duration-200 hover:shadow-lg hover:scale-[1.02] relative overflow-hidden`}>
      {/* 顶部装饰条 */}
      <div className={`h-1 ${theme.accent} w-full`}></div>

      {/* 卡片头部 */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center">
            <div className="mr-3 text-3xl">
              {getInsightIcon(insight.category, insight.severity)}
            </div>
            <div>
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${theme.badge} mb-1`}>
                {t(`analytics.insights.severity.${insight.severity}`, insight.severity)}
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {insight.title}
              </h3>
            </div>
          </div>
        </div>

        {/* 关键数值展示 */}
        <div className="mb-4">
          <div className="flex items-baseline mb-2">
            <span className="text-3xl font-bold text-gray-900 dark:text-white">
              {insight.primaryValue?.toLocaleString() || 'N/A'}
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              {insight.primaryUnit}
            </span>
            {insight.changePercentage && (
              <div className={`ml-3 flex items-center text-sm font-medium ${
                insight.changePercentage > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
              }`}>
                <span className="mr-1">
                  {insight.changePercentage > 0 ? '↗️' : '↘️'}
                </span>
                <span>{Math.abs(insight.changePercentage)}%</span>
              </div>
            )}
          </div>
          {insight.comparisonPeriod && (
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {insight.comparisonPeriod}
            </p>
          )}
        </div>

        {/* 洞察描述 */}
        <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed mb-4">
          {insight.description}
        </p>

        {/* 业务影响 */}
        {insight.businessImpact && (
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">
                📊 {t('analytics.insights.businessImpact', '业务影响')}:
              </span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                insight.businessImpact === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                insight.businessImpact === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
              }`}>
                {t(`analytics.insights.impact.${insight.businessImpact}`, insight.businessImpact)}
              </span>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {insight.impactDescription}
            </p>
          </div>
        )}
      </div>

      {/* 底部行动区域 */}
      <div className={`px-6 py-4 ${theme.bg} border-t ${theme.border}`}>
        {/* 行动建议 */}
        {insight.actionRecommendation && (
          <div className="mb-3">
            <div className="flex items-center mb-1">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">
                💡 {t('analytics.insights.recommendation', '建议')}:
              </span>
              {insight.actionUrgency && (
                <span className={`text-xs font-medium ${getUrgencyColor(insight.actionUrgency)}`}>
                  {t(`analytics.insights.urgency.${insight.actionUrgency}`, insight.actionUrgency)}
                </span>
              )}
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400 leading-relaxed">
              {insight.actionRecommendation}
            </p>
          </div>
        )}

        {/* 底部信息栏 */}
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center text-gray-500 dark:text-gray-400">
            <span className="mr-1">🎯</span>
            <span>
              {t('analytics.insights.confidence', '置信度')}: {Math.round((insight.confidence || 0) * 100)}%
            </span>
          </div>
          {insight.actionable && (
            <button className="flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors">
              <span className="mr-1">👀</span>
              <span>{t('analytics.insights.viewDetails', '查看详情')}</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default InsightsPanel;
