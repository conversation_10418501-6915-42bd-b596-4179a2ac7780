import React from 'react';
import { Flame, Thermometer, Snowflake } from 'lucide-react';

/**
 * 热度指示器组件Props
 */
export interface HeatIndicatorProps {
  level: 'hot' | 'warm' | 'cold';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

/**
 * 热度指示器组件
 * 显示工具的热度级别，包含图标和颜色
 */
export const HeatIndicator: React.FC<HeatIndicatorProps> = ({
  level,
  size = 'md',
  showLabel = false,
  className = ''
}) => {
  // 获取热度级别的配置
  const getHeatConfig = (heatLevel: string) => {
    switch (heatLevel) {
      case 'hot':
        return {
          icon: Flame,
          color: 'text-red-500 dark:text-red-400',
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-800',
          label: '热门'
        };
      case 'warm':
        return {
          icon: Thermometer,
          color: 'text-orange-500 dark:text-orange-400',
          bgColor: 'bg-orange-50 dark:bg-orange-900/20',
          borderColor: 'border-orange-200 dark:border-orange-800',
          label: '温热'
        };
      case 'cold':
        return {
          icon: Snowflake,
          color: 'text-blue-500 dark:text-blue-400',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          label: '冷门'
        };
      default:
        return {
          icon: Thermometer,
          color: 'text-gray-500 dark:text-gray-400',
          bgColor: 'bg-gray-50 dark:bg-gray-900/20',
          borderColor: 'border-gray-200 dark:border-gray-800',
          label: '未知'
        };
    }
  };

  // 获取尺寸配置
  const getSizeConfig = (sizeType: string) => {
    switch (sizeType) {
      case 'sm':
        return {
          iconSize: 'w-3 h-3',
          containerSize: 'w-6 h-6',
          textSize: 'text-xs'
        };
      case 'lg':
        return {
          iconSize: 'w-5 h-5',
          containerSize: 'w-10 h-10',
          textSize: 'text-sm'
        };
      default: // md
        return {
          iconSize: 'w-4 h-4',
          containerSize: 'w-8 h-8',
          textSize: 'text-sm'
        };
    }
  };

  const heatConfig = getHeatConfig(level);
  const sizeConfig = getSizeConfig(size);
  const IconComponent = heatConfig.icon;

  return (
    <div className={`inline-flex items-center space-x-2 ${className}`}>
      {/* 热度图标 */}
      <div 
        className={`
          ${sizeConfig.containerSize} 
          ${heatConfig.bgColor} 
          ${heatConfig.borderColor} 
          border rounded-full 
          flex items-center justify-center
          transition-all duration-200
        `}
      >
        <IconComponent 
          className={`${sizeConfig.iconSize} ${heatConfig.color}`}
        />
      </div>

      {/* 可选的标签 */}
      {showLabel && (
        <span className={`${sizeConfig.textSize} font-medium text-gray-700 dark:text-gray-300`}>
          {heatConfig.label}
        </span>
      )}
    </div>
  );
};

export default HeatIndicator;
