import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { KPICard } from './KPICard';
import { StatusIndicator } from './StatusIndicator';

describe('KPICard', () => {
  it('renders with title and children', () => {
    render(
      <KPICard title="测试卡片">
        <div>测试内容</div>
      </KPICard>
    );
    
    expect(screen.getByText('测试卡片')).toBeInTheDocument();
    expect(screen.getByText('测试内容')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(
      <KPICard title="测试卡片" loading={true}>
        <div>测试内容</div>
      </KPICard>
    );
    
    expect(screen.getByText('加载中...')).toBeInTheDocument();
    expect(screen.queryByText('测试内容')).not.toBeInTheDocument();
  });

  it('shows error state', () => {
    render(
      <KPICard title="测试卡片" error="加载失败">
        <div>测试内容</div>
      </KPICard>
    );
    
    expect(screen.getByText('加载失败')).toBeInTheDocument();
    expect(screen.queryByText('测试内容')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <KPICard title="测试卡片" className="custom-class">
        <div>测试内容</div>
      </KPICard>
    );
    
    expect(container.firstChild).toHaveClass('custom-class');
  });
});

describe('StatusIndicator', () => {
  it('renders healthy status', () => {
    render(<StatusIndicator status="healthy" />);
    const indicator = screen.getByLabelText('状态: 健康');
    expect(indicator).toBeInTheDocument();
  });

  it('renders warning status', () => {
    render(<StatusIndicator status="warning" />);
    const indicator = screen.getByLabelText('状态: 警告');
    expect(indicator).toBeInTheDocument();
  });

  it('renders critical status', () => {
    render(<StatusIndicator status="critical" />);
    const indicator = screen.getByLabelText('状态: 严重');
    expect(indicator).toBeInTheDocument();
  });

  it('shows label when showLabel is true', () => {
    render(<StatusIndicator status="healthy" showLabel={true} />);
    expect(screen.getByText('健康')).toBeInTheDocument();
  });

  it('applies different sizes', () => {
    const { rerender } = render(<StatusIndicator status="healthy" size="sm" />);
    let indicator = screen.getByLabelText('状态: 健康');
    expect(indicator).toHaveClass('w-2', 'h-2');

    rerender(<StatusIndicator status="healthy" size="lg" />);
    indicator = screen.getByLabelText('状态: 健康');
    expect(indicator).toHaveClass('w-4', 'h-4');
  });

  it('applies custom className', () => {
    render(<StatusIndicator status="healthy" className="custom-status" />);
    const indicator = screen.getByLabelText('状态: 健康');
    expect(indicator).toHaveClass('custom-status');
  });
});
