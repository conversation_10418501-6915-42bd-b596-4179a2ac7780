import React from 'react';
import { TopServerItem } from '@/types/overview';
import { TrendIndicator } from './TrendIndicator';

/**
 * 排名列表组件Props
 */
export interface RankingListProps {
  data: TopServerItem[];
  maxItems?: number;
  showTrend?: boolean;
  showPercentage?: boolean;
  className?: string;
}

/**
 * 排名列表组件
 * 显示服务器排名列表，支持趋势指示和百分比显示
 */
export const RankingList: React.FC<RankingListProps> = ({
  data,
  maxItems = 5,
  showTrend = true,
  showPercentage = true,
  className = ''
}) => {
  // 获取排名图标
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return `${rank}.`;
    }
  };

  // 限制显示的数据条数
  const displayData = data.slice(0, maxItems);

  if (!data || data.length === 0) {
    return (
      <div className={`text-center py-4 ${className}`}>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          暂无排名数据
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {displayData.map((server, index) => (
        <div 
          key={server.serverName} 
          className="flex items-center justify-between group hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg p-2 -m-2 transition-colors duration-150"
        >
          {/* 左侧：排名图标和服务器名称 */}
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            <span className="text-lg flex-shrink-0">
              {getRankIcon(server.rank)}
            </span>
            <div className="min-w-0 flex-1">
              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {server.serverName}
              </p>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {server.callCount.toLocaleString()} 次调用
                </span>
                {showPercentage && (
                  <>
                    <span className="text-xs text-gray-400">•</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {server.percentage.toFixed(1)}%
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* 右侧：趋势指示器 */}
          {showTrend && (
            <div className="flex-shrink-0 ml-2">
              <TrendIndicator
                trend={server.trend}
                value={server.trendValue}
                showValue={true}
              />
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default RankingList;
