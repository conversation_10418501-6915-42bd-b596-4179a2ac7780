import React from 'react';
import { useTranslation } from 'react-i18next';
import { TrendingUp, Flame, Snowflake } from 'lucide-react';
import { ToolHeatmapData } from '@/types/overview';
import { KPICard } from './KPICard';
import { StatusIndicator } from './StatusIndicator';
import { HeatmapList } from './HeatmapList';
import { HeatIndicator } from './HeatIndicator';
import styles from './ToolHeatmapCard.module.css';

/**
 * 工具热度分析卡片组件Props
 */
export interface ToolHeatmapCardProps {
  data?: ToolHeatmapData;
  loading?: boolean;
  error?: string | null;
  className?: string;
}

/**
 * 工具热度分析卡片组件
 * 显示工具使用热度分析，包含热门工具、冷门工具分布等信息
 */
export const ToolHeatmapCard: React.FC<ToolHeatmapCardProps> = ({
  data,
  loading = false,
  error = null,
  className = ''
}) => {
  const { t } = useTranslation();

  // 计算健康状态 - 基于热门工具的分布情况
  const getHealthStatus = () => {
    if (!data) return 'warning';
    
    const { hotTools, coldTools } = data;
    const hotToolsCount = hotTools.length;
    const coldToolsCount = coldTools.length;
    
    // 如果热门工具数量充足且冷门工具不多，认为是健康的
    if (hotToolsCount >= 4 && coldToolsCount <= 5) return 'healthy';
    if (hotToolsCount >= 2 && coldToolsCount <= 10) return 'warning';
    return 'critical';
  };

  // 计算总工具数
  const getTotalTools = () => {
    if (!data) return 0;
    return data.heatMatrix.length;
  };

  // 计算热门工具总调用次数
  const getHotToolsCalls = () => {
    if (!data) return 0;
    return data.hotTools.reduce((total, tool) => total + tool.callCount, 0);
  };

  // 计算热度分布百分比
  const getHeatDistribution = () => {
    if (!data) return { hot: 0, warm: 0, cold: 0 };
    
    const total = data.heatMatrix.length;
    const hot = data.heatMatrix.filter(tool => tool.heatLevel === 'hot').length;
    const warm = data.heatMatrix.filter(tool => tool.heatLevel === 'warm').length;
    const cold = data.heatMatrix.filter(tool => tool.heatLevel === 'cold').length;
    
    return {
      hot: Math.round((hot / total) * 100),
      warm: Math.round((warm / total) * 100),
      cold: Math.round((cold / total) * 100)
    };
  };

  const heatDistribution = getHeatDistribution();

  return (
    <KPICard
      title={`🔥 ${t('analytics.overview.toolHeatmap', '工具热度分析')}`}
      loading={loading}
      error={error}
      className={`${styles.toolHeatmapCard} ${className}`}
    >
      {/* 卡片头部信息 */}
      <div className={styles.cardHeader}>
        <div className="flex items-center justify-between mb-4">
          <StatusIndicator 
            status={getHealthStatus()} 
            showLabel 
          />
          <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
            <TrendingUp className="w-4 h-4" />
            <span>
              {t('analytics.overview.heatDistribution', '热度分布')}
            </span>
          </div>
        </div>

        {/* 统计信息 */}
        <div className={styles.statsRow}>
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900 dark:text-white">
              {getTotalTools()}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t('analytics.overview.totalTools', '总工具数')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-orange-600 dark:text-orange-400">
              {data?.hotTools?.length || 0}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t('analytics.overview.hotTools', '热门工具')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
              {getHotToolsCalls().toLocaleString()}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t('analytics.overview.hotToolsCalls', '热门调用')}
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区 */}
      <div className={styles.contentContainer}>
        {/* 热度分布概览 */}
        <div className={styles.heatOverview}>
          <div className="grid grid-cols-3 gap-2 mb-4">
            <div className="text-center">
              <HeatIndicator level="hot" size="sm" />
              <div className="text-sm font-medium text-gray-900 dark:text-white mt-1">
                {heatDistribution.hot}%
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {t('analytics.overview.hotLevel', '热门')}
              </div>
            </div>
            <div className="text-center">
              <HeatIndicator level="warm" size="sm" />
              <div className="text-sm font-medium text-gray-900 dark:text-white mt-1">
                {heatDistribution.warm}%
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {t('analytics.overview.warmLevel', '温热')}
              </div>
            </div>
            <div className="text-center">
              <HeatIndicator level="cold" size="sm" />
              <div className="text-sm font-medium text-gray-900 dark:text-white mt-1">
                {heatDistribution.cold}%
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {t('analytics.overview.coldLevel', '冷门')}
              </div>
            </div>
          </div>
        </div>

        {/* 热度工具列表 */}
        <div className={styles.heatmapContainer}>
          <HeatmapList
            data={data}
            maxItems={10}
            showPercentage={true}
            className={styles.heatmapList}
          />
        </div>
      </div>
    </KPICard>
  );
};

export default ToolHeatmapCard;
