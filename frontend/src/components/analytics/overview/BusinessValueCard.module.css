/* 业务价值指数卡片样式 */

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 1rem;
}

/* 统计信息行 */
.statsRow {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  padding: 0.75rem;
  background: var(--color-background-secondary);
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
}

.statItem {
  text-align: center;
}

.statValue {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--color-text-primary);
  line-height: 1.2;
}

.statLabel {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  margin-top: 0.25rem;
  line-height: 1.3;
}

/* 主要内容区 */
.content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  flex: 1;
  overflow: hidden;
}

/* 价值评分区域 */
.scoreSection {
  flex-shrink: 0;
}

.valueScore {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

/* 评分圆环 */
.scoreRing {
  position: relative;
  flex-shrink: 0;
}

.scoreSvg {
  transform: rotate(-90deg);
}

.scoreCircle {
  transition: stroke-dasharray 0.6s ease-in-out;
}

.scoreText {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.scoreNumber {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-text-primary);
  line-height: 1;
}

.scoreUnit {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  margin-top: 0.125rem;
}

/* 评分详情 */
.scoreDetails {
  flex: 1;
}

.scoreDescription {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 1rem;
}

/* 价值分级统计 */
.valueBreakdown {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.valueItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
}

.valueIndicator {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.valueLabel {
  color: var(--color-text-secondary);
  flex: 1;
}

.valueCount {
  color: var(--color-text-primary);
  font-weight: 600;
}

/* 价值分布区域 */
.distributionSection {
  flex: 1;
  overflow: hidden;
}

.valueDistribution {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 分布标题 */
.distributionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--color-border);
}

.distributionTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.distributionSummary {
  display: flex;
  gap: 1rem;
}

.summaryItem {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

/* 分布列表 */
.distributionList {
  flex: 1;
  overflow-y: auto;
  max-height: 180px;
  padding-right: 0.25rem;
}

.distributionItem {
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: 0.375rem;
  margin-bottom: 0.5rem;
  background: var(--color-background);
  transition: all 0.2s ease;
}

.distributionItem:hover {
  border-color: var(--color-primary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.distributionItem:last-child {
  margin-bottom: 0;
}

/* 类别信息 */
.categoryInfo {
  margin-bottom: 0.5rem;
}

.categoryHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.categoryName {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.categoryMeta {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.scoreGrade {
  font-size: 0.75rem;
  font-weight: 700;
}

/* 类别统计 */
.categoryStats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.statGroup {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.statLabel {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.statValue {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

/* 评分进度条 */
.scoreProgress {
  height: 0.25rem;
  background: var(--color-background-secondary);
  border-radius: 0.125rem;
  overflow: hidden;
}

.scoreBar {
  height: 100%;
  border-radius: 0.125rem;
  transition: width 0.6s ease-in-out;
}

/* 分布总结 */
.distributionFooter {
  margin-top: 0.75rem;
  padding-top: 0.5rem;
  border-top: 1px solid var(--color-border);
}

.footerStats {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.footerStat {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statsRow {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
  
  .valueScore {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .categoryStats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .distributionHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .distributionSummary {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .footerStats {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .distributionItem:hover {
    box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
  }
}
