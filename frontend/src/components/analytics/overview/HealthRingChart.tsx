import React from 'react';

/**
 * 健康度环形图组件Props
 */
export interface HealthRingChartProps {
  healthScore: number;
  size?: number;
  strokeWidth?: number;
  className?: string;
}

/**
 * 健康度环形图组件
 * 显示健康度评分的环形进度图
 */
export const HealthRingChart: React.FC<HealthRingChartProps> = ({
  healthScore,
  size = 120,
  strokeWidth = 8,
  className = ''
}) => {
  // 计算环形图参数
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const progress = Math.min(Math.max(healthScore, 0), 100);
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  // 根据健康度评分获取颜色
  const getHealthColor = (score: number) => {
    if (score >= 85) return '#10B981'; // green-500
    if (score >= 70) return '#F59E0B'; // amber-500
    return '#EF4444'; // red-500
  };

  // 根据健康度评分获取背景颜色
  const getBackgroundColor = (score: number) => {
    if (score >= 85) return '#D1FAE5'; // green-100
    if (score >= 70) return '#FEF3C7'; // amber-100
    return '#FEE2E2'; // red-100
  };

  const healthColor = getHealthColor(progress);
  const backgroundColor = getBackgroundColor(progress);

  return (
    <div className={`relative inline-flex items-center justify-center ${className}`}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* 背景圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="none"
          className="opacity-30"
        />
        
        {/* 进度圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={healthColor}
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="transition-all duration-500 ease-in-out"
        />
      </svg>
      
      {/* 中心文字 */}
      <div className="absolute inset-0 flex flex-col items-center justify-center">
        <div 
          className="text-2xl font-bold"
          style={{ color: healthColor }}
        >
          {progress.toFixed(1)}
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          健康度
        </div>
      </div>
    </div>
  );
};

export default HealthRingChart;
