/* KPI卡片基础样式 */

.kpiCard {
  width: 100%;
  min-height: 360px;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  transition: all 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.kpiCard:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
  border-color: rgba(156, 163, 175, 0.4);
}

/* 深色模式支持 */
:global(.dark) .kpiCard {
  background: #1f2937;
  border-color: #374151;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 1px 2px 0 rgba(0, 0, 0, 0.12);
}

:global(.dark) .kpiCard:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  border-color: #4b5563;
}

/* 卡片头部 */
.cardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
}

:global(.dark) .cardHeader {
  border-bottom-color: rgba(55, 65, 81, 0.6);
}

.cardTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.4;
}

:global(.dark) .cardTitle {
  color: #f9fafb;
}

/* 卡片内容 */
.cardContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 0;
}

/* 加载状态 */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
}

:global(.dark) .loadingContainer {
  color: #9ca3af;
}

.loadingIcon {
  width: 2rem;
  height: 2rem;
  animation: spin 1s linear infinite;
  margin-bottom: 0.5rem;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loadingText {
  font-size: 0.875rem;
  font-weight: 500;
}

/* 错误状态 */
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #dc2626;
}

:global(.dark) .errorContainer {
  color: #f87171;
}

.errorIcon {
  width: 2rem;
  height: 2rem;
  margin-bottom: 0.75rem;
}

.errorContent {
  max-width: 200px;
}

.errorTitle {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.errorMessage {
  font-size: 0.75rem;
  margin: 0;
  opacity: 0.8;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .kpiCard {
    width: 100%;
    max-width: 380px;
  }
}

@media (max-width: 768px) {
  .kpiCard {
    width: 100%;
    height: auto;
    min-height: 280px;
  }
  
  .cardHeader {
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
  }
  
  .cardTitle {
    font-size: 0.875rem;
  }
}
