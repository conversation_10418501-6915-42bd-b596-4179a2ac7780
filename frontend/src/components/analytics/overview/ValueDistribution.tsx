import React from 'react';
import { useTranslation } from 'react-i18next';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import styles from './BusinessValueCard.module.css';

/**
 * 价值分布项接口
 */
export interface ValueDistributionItem {
  category: string;
  score: number;
  count: number;
}

/**
 * 价值分布组件Props
 */
export interface ValueDistributionProps {
  distribution: ValueDistributionItem[];
}

/**
 * 价值分布组件
 * 显示各类别工具的价值分布情况
 */
export const ValueDistribution: React.FC<ValueDistributionProps> = ({
  distribution
}) => {
  const { t } = useTranslation();

  // 获取评分等级
  const getScoreGrade = (score: number) => {
    if (score >= 90) return 'A+';
    if (score >= 80) return 'A';
    if (score >= 70) return 'B';
    if (score >= 60) return 'C';
    return 'D';
  };

  // 获取评分颜色
  const getScoreColor = (score: number) => {
    if (score >= 80) return '#10B981'; // 绿色
    if (score >= 60) return '#F59E0B'; // 橙色
    return '#EF4444'; // 红色
  };

  // 获取趋势图标（基于评分）
  const getTrendIcon = (score: number) => {
    if (score >= 80) return <TrendingUp className="w-3 h-3" style={{ color: '#10B981' }} />;
    if (score >= 60) return <Minus className="w-3 h-3" style={{ color: '#F59E0B' }} />;
    return <TrendingDown className="w-3 h-3" style={{ color: '#EF4444' }} />;
  };

  // 获取价值等级描述
  const getValueLevel = (score: number) => {
    if (score >= 80) return t('analytics.overview.businessValue.highValue');
    if (score >= 60) return t('analytics.overview.businessValue.mediumValue');
    return t('analytics.overview.businessValue.lowValue');
  };

  // 计算总工具数
  const totalTools = distribution.reduce((sum, item) => sum + item.count, 0);

  // 计算平均评分
  const averageScore = distribution.length > 0 
    ? distribution.reduce((sum, item) => sum + item.score, 0) / distribution.length 
    : 0;

  return (
    <div className={styles.valueDistribution}>
      {/* 分布标题 */}
      <div className={styles.distributionHeader}>
        <h4 className={styles.distributionTitle}>
          {t('analytics.overview.businessValue.categoryDistribution')}
        </h4>
        <div className={styles.distributionSummary}>
          <span className={styles.summaryItem}>
            {t('analytics.overview.businessValue.averageScore')}: {averageScore.toFixed(1)}
          </span>
          <span className={styles.summaryItem}>
            {t('analytics.overview.businessValue.totalCategories')}: {distribution.length}
          </span>
        </div>
      </div>

      {/* 分布列表 */}
      <div className={styles.distributionList}>
        {distribution.map((item, index) => {
          const percentage = totalTools > 0 ? Math.round((item.count / totalTools) * 100) : 0;
          
          return (
            <div key={index} className={styles.distributionItem}>
              <div className={styles.categoryInfo}>
                <div className={styles.categoryHeader}>
                  <span className={styles.categoryName}>{item.category}</span>
                  <div className={styles.categoryMeta}>
                    {getTrendIcon(item.score)}
                    <span className={styles.scoreGrade} style={{ color: getScoreColor(item.score) }}>
                      {getScoreGrade(item.score)}
                    </span>
                  </div>
                </div>
                
                <div className={styles.categoryStats}>
                  <div className={styles.statGroup}>
                    <span className={styles.statLabel}>
                      {t('analytics.overview.businessValue.score')}:
                    </span>
                    <span className={styles.statValue} style={{ color: getScoreColor(item.score) }}>
                      {item.score.toFixed(1)}
                    </span>
                  </div>
                  
                  <div className={styles.statGroup}>
                    <span className={styles.statLabel}>
                      {t('analytics.overview.businessValue.toolCount')}:
                    </span>
                    <span className={styles.statValue}>
                      {item.count} ({percentage}%)
                    </span>
                  </div>
                  
                  <div className={styles.statGroup}>
                    <span className={styles.statLabel}>
                      {t('analytics.overview.businessValue.valueLevel')}:
                    </span>
                    <span className={styles.statValue} style={{ color: getScoreColor(item.score) }}>
                      {getValueLevel(item.score)}
                    </span>
                  </div>
                </div>
              </div>

              {/* 评分进度条 */}
              <div className={styles.scoreProgress}>
                <div 
                  className={styles.scoreBar}
                  style={{ 
                    width: `${item.score}%`,
                    backgroundColor: getScoreColor(item.score)
                  }}
                />
              </div>
            </div>
          );
        })}
      </div>

      {/* 分布总结 */}
      <div className={styles.distributionFooter}>
        <div className={styles.footerStats}>
          <span className={styles.footerStat}>
            {t('analytics.overview.businessValue.highValueCategories')}: {
              distribution.filter(item => item.score >= 80).length
            }
          </span>
          <span className={styles.footerStat}>
            {t('analytics.overview.businessValue.totalTools')}: {totalTools.toLocaleString()}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ValueDistribution;
