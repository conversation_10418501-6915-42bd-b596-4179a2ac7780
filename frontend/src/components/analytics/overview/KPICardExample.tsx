import React, { useState } from 'react';
import { KPICard } from './KPICard';
import { StatusIndicator } from './StatusIndicator';
import { TrendingUp, TrendingDown, Activity } from 'lucide-react';

/**
 * KPI卡片示例组件
 * 用于验证KPICard和StatusIndicator组件的功能
 */
export const KPICardExample: React.FC = () => {
  const [loadingCard, setLoadingCard] = useState(false);
  const [errorCard, setErrorCard] = useState(false);

  const toggleLoading = () => {
    setLoadingCard(!loadingCard);
    setErrorCard(false);
  };

  const toggleError = () => {
    setErrorCard(!errorCard);
    setLoadingCard(false);
  };

  const resetStates = () => {
    setLoadingCard(false);
    setErrorCard(false);
  };

  return (
    <div className="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          KPI卡片组件示例
        </h1>

        {/* 控制按钮 */}
        <div className="mb-6 space-x-4">
          <button
            onClick={toggleLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            {loadingCard ? '停止加载' : '显示加载状态'}
          </button>
          <button
            onClick={toggleError}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            {errorCard ? '清除错误' : '显示错误状态'}
          </button>
          <button
            onClick={resetStates}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          >
            重置状态
          </button>
        </div>

        {/* 状态指示器示例 */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            状态指示器示例
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
              <h3 className="font-medium mb-3">不同状态</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <StatusIndicator status="healthy" showLabel />
                </div>
                <div className="flex items-center space-x-3">
                  <StatusIndicator status="warning" showLabel />
                </div>
                <div className="flex items-center space-x-3">
                  <StatusIndicator status="critical" showLabel />
                </div>
              </div>
            </div>

            <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
              <h3 className="font-medium mb-3">不同尺寸</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <StatusIndicator status="healthy" size="sm" />
                  <span className="text-sm">小尺寸</span>
                </div>
                <div className="flex items-center space-x-3">
                  <StatusIndicator status="healthy" size="md" />
                  <span className="text-sm">中尺寸</span>
                </div>
                <div className="flex items-center space-x-3">
                  <StatusIndicator status="healthy" size="lg" />
                  <span className="text-sm">大尺寸</span>
                </div>
              </div>
            </div>

            <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
              <h3 className="font-medium mb-3">带标签</h3>
              <div className="space-y-2">
                <StatusIndicator status="healthy" size="sm" showLabel />
                <StatusIndicator status="warning" size="md" showLabel />
                <StatusIndicator status="critical" size="lg" showLabel />
              </div>
            </div>
          </div>
        </div>

        {/* KPI卡片示例 */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            KPI卡片示例
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {/* 正常状态卡片 */}
            <KPICard title="服务器性能">
              <div className="flex items-center justify-between mb-4">
                <StatusIndicator status="healthy" showLabel />
                <Activity className="w-5 h-5 text-blue-500" />
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  98.5%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  系统可用性
                </div>
                <div className="flex items-center justify-center text-green-600">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  <span className="text-sm">+2.3%</span>
                </div>
              </div>
            </KPICard>

            {/* 警告状态卡片 */}
            <KPICard title="内存使用率">
              <div className="flex items-center justify-between mb-4">
                <StatusIndicator status="warning" showLabel />
                <Activity className="w-5 h-5 text-yellow-500" />
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  78.2%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  内存占用
                </div>
                <div className="flex items-center justify-center text-yellow-600">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  <span className="text-sm">+5.1%</span>
                </div>
              </div>
            </KPICard>

            {/* 动态状态卡片 */}
            <KPICard 
              title="动态状态测试" 
              loading={loadingCard}
              error={errorCard ? "网络连接失败，请稍后重试" : null}
            >
              <div className="flex items-center justify-between mb-4">
                <StatusIndicator status="critical" showLabel />
                <Activity className="w-5 h-5 text-red-500" />
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  45.8%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  错误率
                </div>
                <div className="flex items-center justify-center text-red-600">
                  <TrendingDown className="w-4 h-4 mr-1" />
                  <span className="text-sm">-1.2%</span>
                </div>
              </div>
            </KPICard>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KPICardExample;
