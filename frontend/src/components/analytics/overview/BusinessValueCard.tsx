import React from 'react';
import { useTranslation } from 'react-i18next';
import { TrendingUp } from 'lucide-react';
import { BusinessValueData } from '@/types/overview';
import { KPICard } from './KPICard';
import { StatusIndicator } from './StatusIndicator';
import { ValueDistribution } from './ValueDistribution';
import { ValueScore } from './ValueScore';
import styles from './BusinessValueCard.module.css';

/**
 * 业务价值指数卡片组件Props
 */
export interface BusinessValueCardProps {
  data?: BusinessValueData;
  loading?: boolean;
  error?: string | null;
  className?: string;
}

/**
 * 业务价值指数卡片组件
 * 显示工具的业务价值分析，按高中低价值分级展示
 */
export const BusinessValueCard: React.FC<BusinessValueCardProps> = ({
  data,
  loading = false,
  error = null,
  className = ''
}) => {
  const { t } = useTranslation();

  // 计算健康状态 - 基于整体评分
  const getHealthStatus = () => {
    if (!data) return 'warning';
    
    const score = data.overallScore;
    if (score >= 80) return 'healthy';
    if (score >= 60) return 'warning';
    return 'critical';
  };

  // 计算总工具数
  const getTotalTools = () => {
    if (!data) return 0;
    return data.highValueTools + data.mediumValueTools + data.lowValueTools;
  };

  // 计算高价值工具占比
  const getHighValuePercentage = () => {
    const total = getTotalTools();
    if (total === 0) return 0;
    return Math.round((data!.highValueTools / total) * 100);
  };

  // 获取评分等级
  const getScoreGrade = (score: number) => {
    if (score >= 90) return 'A+';
    if (score >= 80) return 'A';
    if (score >= 70) return 'B';
    if (score >= 60) return 'C';
    return 'D';
  };

  return (
    <KPICard
      title={t('analytics.overview.businessValue.title')}
      icon={<TrendingUp className="w-5 h-5" />}
      status={<StatusIndicator status={getHealthStatus()} />}
      subtitle={t('analytics.overview.businessValue.subtitle')}
      loading={loading}
      error={error}
      className={className}
    >
      <div className={styles.container}>
        {/* 统计信息行 */}
        <div className={styles.statsRow}>
          <div className={styles.statItem}>
            <div className={styles.statValue}>
              {data?.overallScore?.toFixed(1) || '0.0'}
            </div>
            <div className={styles.statLabel}>
              {t('analytics.overview.businessValue.overallScore')}
            </div>
          </div>
          <div className={styles.statItem}>
            <div className={styles.statValue}>
              {getScoreGrade(data?.overallScore || 0)}
            </div>
            <div className={styles.statLabel}>
              {t('analytics.overview.businessValue.grade')}
            </div>
          </div>
          <div className={styles.statItem}>
            <div className={styles.statValue}>
              {getTotalTools().toLocaleString()}
            </div>
            <div className={styles.statLabel}>
              {t('analytics.overview.businessValue.totalTools')}
            </div>
          </div>
          <div className={styles.statItem}>
            <div className={styles.statValue}>
              {getHighValuePercentage()}%
            </div>
            <div className={styles.statLabel}>
              {t('analytics.overview.businessValue.highValueRatio')}
            </div>
          </div>
        </div>

        {/* 主要内容区 */}
        <div className={styles.content}>
          {/* 价值评分组件 */}
          <div className={styles.scoreSection}>
            <ValueScore 
              score={data?.overallScore || 0}
              highValueTools={data?.highValueTools || 0}
              mediumValueTools={data?.mediumValueTools || 0}
              lowValueTools={data?.lowValueTools || 0}
            />
          </div>

          {/* 价值分布组件 */}
          <div className={styles.distributionSection}>
            <ValueDistribution 
              distribution={data?.valueDistribution || []}
            />
          </div>
        </div>
      </div>
    </KPICard>
  );
};

export default BusinessValueCard;
