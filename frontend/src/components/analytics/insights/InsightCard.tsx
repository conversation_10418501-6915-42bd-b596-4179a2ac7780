import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Insight } from '../../../services/analyticsService';

interface InsightCardProps {
  insight: Insight;
  onExpand?: (insight: Insight) => void;
}

const InsightCard: React.FC<InsightCardProps> = ({ insight, onExpand }) => {
  const { t } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);

  // Get icon for insight type
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'trend':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
        );
      case 'anomaly':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        );
      case 'seasonality':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        );
      case 'correlation':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
          </svg>
        );
      case 'forecast':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        );
      case 'recommendation':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  // Get color for severity
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-400';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-400';
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-400';
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-400';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300';
    }
  };

  // Get severity icon color
  const getSeverityIconColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-500';
      case 'warning':
        return 'text-yellow-500';
      case 'success':
        return 'text-green-500';
      case 'info':
        return 'text-blue-500';
      default:
        return 'text-gray-500';
    }
  };

  // Format date
  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  // Map metric names from backend format to translation keys
  const getMetricTranslationKey = (metric: string) => {
    const metricMap: Record<string, string> = {
      'success_rate': 'successRate',
      'response_time': 'responseTime',
      'calls': 'calls'
    };
    return metricMap[metric] || metric;
  };

  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded);
    if (onExpand && !isExpanded) {
      onExpand(insight);
    }
  };

  return (
    <div className={`border rounded-lg p-4 transition-all duration-200 hover:shadow-md ${getSeverityColor(insight.severity)}`}>
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-start">
          <div className={`mr-3 p-1 rounded ${getSeverityIconColor(insight.severity)}`}>
            {getTypeIcon(insight.type)}
          </div>
          <div className="flex-1">
            <h4 className="font-medium text-sm mb-1">
              {insight.title}
            </h4>
            <p className="text-xs opacity-75 mb-2">
              {insight.description}
            </p>
            
            {/* Metrics */}
            <div className="flex flex-wrap gap-1 mb-2">
              {insight.metrics.map((metric, index) => (
                <span
                  key={index}
                  className="px-2 py-0.5 bg-white dark:bg-gray-800 rounded text-xs font-medium opacity-80"
                >
                  {t(`analytics.metrics.${getMetricTranslationKey(metric)}`, metric)}
                </span>
              ))}
            </div>
          </div>
        </div>
        
        {/* Expand button */}
        <button
          onClick={handleToggleExpand}
          className="ml-2 p-1 hover:bg-white dark:hover:bg-gray-800 rounded transition-colors"
        >
          <svg
            className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>
      
      {/* Expanded content */}
      {isExpanded && (
        <div className="mt-3 pt-3 border-t border-current border-opacity-20">
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div>
              <span className="font-medium">{t('analytics.insights.type', 'Type')}:</span>
              <span className="ml-1">{t(`analytics.insights.type.${insight.type}`, insight.type)}</span>
            </div>
            
            <div>
              <span className="font-medium">{t('analytics.insights.severity', 'Severity')}:</span>
              <span className="ml-1">{t(`analytics.insights.severity.${insight.severity}`, insight.severity)}</span>
            </div>
            
            {insight.confidence && (
              <div>
                <span className="font-medium">{t('analytics.insights.confidence', 'Confidence')}:</span>
                <span className="ml-1">{(insight.confidence * 100).toFixed(0)}%</span>
              </div>
            )}
            
            <div>
              <span className="font-medium">{t('analytics.insights.timestamp', 'Generated')}:</span>
              <span className="ml-1">{formatDate(insight.timestamp)}</span>
            </div>
          </div>
          
          {insight.timeRange && (
            <div className="mt-2 text-xs">
              <span className="font-medium">{t('analytics.insights.timeRange', 'Time Range')}:</span>
              <span className="ml-1">
                {formatDate(insight.timeRange.start)} - {formatDate(insight.timeRange.end)}
              </span>
            </div>
          )}
          
          {insight.recommendation && (
            <div className="mt-3 p-2 bg-white dark:bg-gray-800 rounded text-xs">
              <div className="font-medium mb-1 flex items-center">
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                {t('analytics.insights.recommendation', 'Recommendation')}
              </div>
              <p className="opacity-90">{insight.recommendation}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default InsightCard;
