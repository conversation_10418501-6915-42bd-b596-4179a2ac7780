import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Insight } from '../../../services/analyticsService';
import InsightCard from './InsightCard';
import InsightDetails from './InsightDetails';

interface InsightsListProps {
  insights: Insight[];
  title?: string;
  showFilters?: boolean;
}

const InsightsList: React.FC<InsightsListProps> = ({
  insights,
  title = 'Insights',
  showFilters = true,
}) => {
  const { t } = useTranslation();
  const [selectedInsight, setSelectedInsight] = useState<Insight | null>(null);
  const [filterType, setFilterType] = useState<string>('all');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Filter insights
  const filteredInsights = useMemo(() => {
    return insights.filter((insight) => {
      // Filter by type
      if (filterType !== 'all' && insight.type !== filterType) return false;
      
      // Filter by severity
      if (filterSeverity !== 'all' && insight.severity !== filterSeverity) return false;
      
      // Filter by search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          insight.title.toLowerCase().includes(query) ||
          insight.description.toLowerCase().includes(query) ||
          insight.metrics.some(m => m.toLowerCase().includes(query)) ||
          (insight.recommendation && insight.recommendation.toLowerCase().includes(query))
        );
      }
      
      return true;
    });
  }, [insights, filterType, filterSeverity, searchQuery]);

  // Get insight type counts
  const typeCounts = useMemo(() => {
    const counts: Record<string, number> = { all: insights.length };
    
    insights.forEach((insight) => {
      counts[insight.type] = (counts[insight.type] || 0) + 1;
    });
    
    return counts;
  }, [insights]);

  // Get insight severity counts
  const severityCounts = useMemo(() => {
    const counts: Record<string, number> = { all: insights.length };
    
    insights.forEach((insight) => {
      counts[insight.severity] = (counts[insight.severity] || 0) + 1;
    });
    
    return counts;
  }, [insights]);

  // Handle insight expand
  const handleInsightExpand = (insight: Insight) => {
    setSelectedInsight(insight);
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          {title} ({filteredInsights.length})
        </h3>
      </div>
      
      {/* Filters */}
      {showFilters && (
        <div className="space-y-3">
          {/* Search */}
          <div>
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={t('analytics.insights.searchPlaceholder', 'Search insights...')}
                className="w-full px-3 py-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
              <div className="absolute left-3 top-2.5 text-gray-400">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>
          
          {/* Type filter */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setFilterType('all')}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                filterType === 'all'
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                  : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
              }`}
            >
              {t('common.all', 'All')} ({typeCounts.all || 0})
            </button>
            
            {Object.entries(typeCounts)
              .filter(([type]) => type !== 'all')
              .map(([type, count]) => (
                <button
                  key={type}
                  onClick={() => setFilterType(type)}
                  className={`px-3 py-1 text-xs rounded-full transition-colors ${
                    filterType === type
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                  }`}
                >
                  {t(`analytics.insights.type.${type}`, type)} ({count})
                </button>
              ))}
          </div>
          
          {/* Severity filter */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setFilterSeverity('all')}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                filterSeverity === 'all'
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                  : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
              }`}
            >
              {t('common.all', 'All')} ({severityCounts.all || 0})
            </button>
            
            {Object.entries(severityCounts)
              .filter(([severity]) => severity !== 'all')
              .map(([severity, count]) => (
                <button
                  key={severity}
                  onClick={() => setFilterSeverity(severity)}
                  className={`px-3 py-1 text-xs rounded-full transition-colors ${
                    filterSeverity === severity
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                  }`}
                >
                  {t(`analytics.insights.severity.${severity}`, severity)} ({count})
                </button>
              ))}
          </div>
        </div>
      )}
      
      {/* Insights list */}
      {filteredInsights.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">
            {t('analytics.insights.noInsights', 'No insights found')}
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {filteredInsights.map((insight) => (
            <InsightCard
              key={insight.id}
              insight={insight}
              onExpand={handleInsightExpand}
            />
          ))}
        </div>
      )}
      
      {/* Insight details modal */}
      {selectedInsight && (
        <InsightDetails
          insight={selectedInsight}
          onClose={() => setSelectedInsight(null)}
        />
      )}
    </div>
  );
};

export default InsightsList;
