import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Insight } from '../../../services/analyticsService';
import InsightCard from './InsightCard';

interface InsightsDashboardProps {
  insights: Insight[];
  onViewAll?: () => void;
}

const InsightsDashboard: React.FC<InsightsDashboardProps> = ({ insights, onViewAll }) => {
  const { t } = useTranslation();

  // Calculate statistics
  const stats = useMemo(() => {
    const total = insights.length;
    const critical = insights.filter(i => i.severity === 'critical').length;
    const warning = insights.filter(i => i.severity === 'warning').length;
    const success = insights.filter(i => i.severity === 'success').length;
    const info = insights.filter(i => i.severity === 'info').length;
    
    const actionable = insights.filter(i => i.actionable).length;
    
    const typeStats = {
      trend: insights.filter(i => i.type === 'trend').length,
      anomaly: insights.filter(i => i.type === 'anomaly').length,
      seasonality: insights.filter(i => i.type === 'seasonality').length,
      correlation: insights.filter(i => i.type === 'correlation').length,
      forecast: insights.filter(i => i.type === 'forecast').length,
      recommendation: insights.filter(i => i.type === 'recommendation').length,
    };
    
    return {
      total,
      critical,
      warning,
      success,
      info,
      actionable,
      typeStats,
    };
  }, [insights]);

  // Get top insights (critical and warning first, then by confidence)
  const topInsights = useMemo(() => {
    return insights
      .sort((a, b) => {
        // Sort by severity first
        const severityOrder = { critical: 4, warning: 3, success: 2, info: 1 };
        const severityDiff = (severityOrder[b.severity] || 0) - (severityOrder[a.severity] || 0);
        if (severityDiff !== 0) return severityDiff;
        
        // Then by confidence
        return (b.confidence || 0) - (a.confidence || 0);
      })
      .slice(0, 3);
  }, [insights]);

  if (insights.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {t('analytics.insights.dashboard', 'Insights Dashboard')}
        </h3>
        <div className="text-center py-8">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {t('analytics.insights.noInsights', 'No insights available')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          {t('analytics.insights.dashboard', 'Insights Dashboard')}
        </h3>
        {onViewAll && (
          <button
            onClick={onViewAll}
            className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
          >
            {t('analytics.insights.viewAll', 'View All')} ({stats.total})
          </button>
        )}
      </div>
      
      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t('analytics.insights.critical', 'Critical')}
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.critical}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t('analytics.insights.warning', 'Warning')}
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.warning}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t('analytics.insights.actionable', 'Actionable')}
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.actionable}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t('analytics.insights.total', 'Total')}
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.total}
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Type Distribution */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
          {t('analytics.insights.typeDistribution', 'Insight Types')}
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {Object.entries(stats.typeStats)
            .filter(([, count]) => count > 0)
            .map(([type, count]) => (
              <div key={type} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t(`analytics.insights.type.${type}`, type)}
                </span>
                <span className="text-sm font-semibold text-gray-900 dark:text-white">
                  {count}
                </span>
              </div>
            ))}
        </div>
      </div>
      
      {/* Top Insights */}
      {topInsights.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            {t('analytics.insights.topInsights', 'Key Insights')}
          </h4>
          <div className="space-y-3">
            {topInsights.map((insight) => (
              <InsightCard key={insight.id} insight={insight} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default InsightsDashboard;
