import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { TrendPoint, AnomalyPoint } from '../../services/analyticsService';
import ChartDetailPanel from './ChartDetailPanel';
import { NoDataEmptyState } from '../ui/EmptyState';
import { useFormatters } from '../../utils/formatters';

interface InteractiveTimeSeriesChartProps {
  data: TrendPoint[];
  metric: 'calls' | 'success_rate' | 'response_time';
  height?: number;
  width?: number;
  showTooltip?: boolean;
  enableZoom?: boolean;
  enablePan?: boolean;
  compact?: boolean;
  anomalies?: {
    calls?: AnomalyPoint[];
    success_rate?: AnomalyPoint[];
    response_time?: AnomalyPoint[];
};
}

interface ChartState {
  zoomLevel: number;
  panOffset: number;
  isDragging: boolean;
  dragStart: number;
  hoveredPoint: number | null;
  tooltipPosition: { x: number; y: number } | null;
  selectedPoint: number | null;
}

const InteractiveTimeSeriesChart: React.FC<InteractiveTimeSeriesChartProps> = ({
  data,
  metric,
  height = 250,
  width = 800,
  showTooltip = true,
  enableZoom = true,
  enablePan = true,
  compact = false,
  anomalies,
}) => {
  const { t } = useTranslation();
  const { formatValue, formatDate, formatTime } = useFormatters();
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [actualWidth, setActualWidth] = useState(width);
  const [chartState, setChartState] = useState<ChartState>({
    zoomLevel: 1,
    panOffset: 0,
    isDragging: false,
    dragStart: 0,
    hoveredPoint: null,
    tooltipPosition: null,
    selectedPoint: null,
  });

  // Calculate responsive width
  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current && width === 0) {
        const containerWidth = containerRef.current.offsetWidth;
        // Adjust padding based on compact mode
        const paddingAdjustment = compact ? 10 : 20;
        setActualWidth(Math.max(200, containerWidth - paddingAdjustment));
      } else {
        setActualWidth(width);
      }
    };

    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => window.removeEventListener('resize', updateWidth);
  }, [width, compact]);

if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div
        ref={containerRef}
        className="w-full"
        style={{ height }}
      >
        <NoDataEmptyState
          title={t('analytics.chart.noData', 'No data available')}
          description={t('analytics.chart.noDataDescription', 'No data points are available for the selected time range.')}
          size="sm"
        />
      </div>
    );
  }

  // Calculate chart dimensions with better spacing for 24h data
  const padding = compact
    ? { top: 20, right: 30, bottom: 50, left: 60 }
    : { top: 30, right: 40, bottom: 70, left: 80 };
  const chartWidth = Math.max(0, actualWidth - padding.left - padding.right);
  const chartHeight = Math.max(0, height - padding.top - padding.bottom);

  // Calculate data range with zoom and pan
  const visibleDataStart = Math.max(0, Math.floor(-chartState.panOffset / chartState.zoomLevel));
  const visibleDataEnd = Math.min(
    data.length,
    Math.ceil((chartWidth - chartState.panOffset) / chartState.zoomLevel) + visibleDataStart
  );
  const visibleData = (data || []).slice(visibleDataStart, visibleDataEnd);

  // Calculate value range
  const values = visibleData.map(point => point[metric]);
  const max = Math.max(...values) * 1.1; // Add 10% padding
  const min = Math.min(...values) * 0.9; // Subtract 10% padding
  const range = max - min || 1;

  // Calculate scales - ensure data points spread across full width
  const xScale = (i: number) => {
    if (data.length <= 1) return chartWidth / 2;
    return (i / (visibleData.length - 1)) * chartWidth;
  };
  const yScale = (value: number) => chartHeight - ((value - min) / range) * chartHeight;

  // Generate path for visible data
  const pathData = visibleData
    .map((point, i) => {
      const x = xScale(i);
      const y = yScale(point[metric]);
      return `${i === 0 ? 'M' : 'L'} ${x} ${y}`;
    })
    .join(' ');

  // Note: formatValue, formatDate, and formatTime are now provided by useFormatters hook

  // Get metric label with internationalization
  const getMetricLabel = (metricType: string) => {
    switch (metricType) {
      case 'calls':
        return t('analytics.metrics.calls', 'API Calls');
      case 'success_rate':
        return t('analytics.metrics.successRate', 'Success Rate');
      case 'response_time':
        return t('analytics.metrics.responseTime', 'Response Time');
      default:
        return metricType;
    }
  };

  // Handle mouse wheel for zooming
  const handleWheel = useCallback((event: React.WheelEvent) => {
    if (!enableZoom) return;
    
    event.preventDefault();
    const delta = event.deltaY > 0 ? 0.9 : 1.1;
    const newZoomLevel = Math.max(0.5, Math.min(10, chartState.zoomLevel * delta));
    
    setChartState(prev => ({
      ...prev,
      zoomLevel: newZoomLevel,
    }));
  }, [enableZoom, chartState.zoomLevel]);

  // Handle mouse down for panning
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (!enablePan) return;
    
    setChartState(prev => ({
      ...prev,
      isDragging: true,
      dragStart: event.clientX,
    }));
  }, [enablePan]);

  // Handle mouse move for panning and tooltip
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    const svgElement = svgRef.current;
    if (!svgElement) return;

    const rect = svgElement.getBoundingClientRect();
    const x = event.clientX - rect.left - padding.left;
    const y = event.clientY - rect.top - padding.top;

    // Handle panning
    if (chartState.isDragging && enablePan) {
      const deltaX = event.clientX - chartState.dragStart;
      const newPanOffset = Math.max(
        -(data.length * chartState.zoomLevel - chartWidth),
        Math.min(0, chartState.panOffset + deltaX)
      );
      
      setChartState(prev => ({
        ...prev,
        panOffset: newPanOffset,
        dragStart: event.clientX,
      }));
    }

    // Handle tooltip
    if (showTooltip && x >= 0 && x <= chartWidth && y >= 0 && y <= chartHeight) {
      // Find closest data point
      let closestIndex = -1;
      let minDistance = Infinity;
      
      visibleData.forEach((point, i) => {
        const pointX = xScale(i);
        const pointY = yScale(point[metric]);
        const distance = Math.sqrt((x - pointX) ** 2 + (y - pointY) ** 2);
        
        if (distance < minDistance && distance < 20) { // 20px threshold
          minDistance = distance;
          closestIndex = visibleDataStart + i;
        }
      });

      setChartState(prev => ({
        ...prev,
        hoveredPoint: closestIndex,
        tooltipPosition: closestIndex >= 0 ? { x: event.clientX, y: event.clientY } : null,
      }));
    } else {
      setChartState(prev => ({
        ...prev,
        hoveredPoint: null,
        tooltipPosition: null,
      }));
    }
  }, [
    chartState.isDragging,
    chartState.dragStart,
    chartState.panOffset,
    chartState.zoomLevel,
    enablePan,
    showTooltip,
    data.length,
    chartWidth,
    chartHeight,
    visibleData,
    visibleDataStart,
    xScale,
    yScale,
    metric,
  ]);

  // Handle mouse up for panning
  const handleMouseUp = useCallback(() => {
    setChartState(prev => ({
      ...prev,
      isDragging: false,
    }));
  }, []);

  // Handle mouse leave
  const handleMouseLeave = useCallback(() => {
    setChartState(prev => ({
      ...prev,
      isDragging: false,
      hoveredPoint: null,
      tooltipPosition: null,
    }));
  }, []);

  // Reset zoom and pan
  const resetView = useCallback(() => {
    setChartState(prev => ({
      ...prev,
      zoomLevel: 1,
      panOffset: 0,
    }));
  }, []);

  // Handle point click
  const handlePointClick = useCallback((pointIndex: number) => {
    setChartState(prev => ({
      ...prev,
      selectedPoint: pointIndex,
    }));
  }, []);

  // Close detail panel
  const closeDetailPanel = useCallback(() => {
    setChartState(prev => ({
      ...prev,
      selectedPoint: null,
    }));
  }, []);

  return (
    <div ref={containerRef} className="relative w-full">
      {/* 移除图表控件，简化显示 */}

      {/* SVG Chart */}
      <svg
        ref={svgRef}
        width={actualWidth}
        height={height}
        className="border border-gray-200 dark:border-gray-700 rounded block w-full"
        style={{ maxWidth: '100%' }}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
      >
        {/* Background */}
        <rect
          width={actualWidth}
          height={height}
          fill="transparent"
        />

        {/* Grid lines */}
        <g transform={`translate(${padding.left}, ${padding.top})`}>
          {/* Horizontal grid lines */}
          {(compact ? [0, 0.25, 0.5, 0.75, 1] : [0, 0.25, 0.5, 0.75, 1]).map((ratio) => (
            <line
              key={`h-grid-${ratio}`}
              x1={0}
              y1={chartHeight * ratio}
              x2={chartWidth}
              y2={chartHeight * ratio}
              stroke="#e5e7eb"
              strokeWidth={ratio === 0 || ratio === 1 ? 1 : 0.5}
              strokeOpacity={ratio === 0 || ratio === 1 ? 0.6 : 0.2}
              className="dark:stroke-gray-600"
            />
          ))}

          {/* Vertical grid lines - better spacing for 24h data */}
          {visibleData.map((_, i) => {
            const skipInterval = Math.max(1, Math.floor(visibleData.length / 6)); // Show 6-8 vertical lines
            if (i % skipInterval === 0 || i === visibleData.length - 1) {
              return (
                <line
                  key={`v-grid-${i}`}
                  x1={xScale(i)}
                  y1={0}
                  x2={xScale(i)}
                  y2={chartHeight}
                  stroke="#e5e7eb"
                  strokeWidth={0.5}
                  strokeOpacity={0.2}
                  className="dark:stroke-gray-600"
                />
              );
            }
            return null;
          })}
        </g>

        {/* Chart content */}
        <g transform={`translate(${padding.left}, ${padding.top})`}>
          {/* Clip path for chart area */}
          <defs>
            <clipPath id="chart-clip">
              <rect x={0} y={0} width={chartWidth} height={chartHeight} />
            </clipPath>
          </defs>

          <g clipPath="url(#chart-clip)">
            {/* Line */}
            <path
              d={pathData}
              fill="none"
              stroke="#3B82F6"
              strokeWidth={compact ? 3 : 2.5}
              strokeLinecap="round"
              strokeLinejoin="round"
              className="dark:stroke-blue-400"
            />

            {/* Data points */}
            {visibleData.map((point, i) => {
              const globalIndex = visibleDataStart + i;
              const isHovered = chartState.hoveredPoint === globalIndex;

              // Check if this point is an anomaly
              const timestamp = point.timestamp;
              const anomalyData = anomalies?.[metric]?.find(a => a.timestamp === timestamp);
              const isAnomaly = !!anomalyData;

              // Determine point style based on anomaly type and severity
              let pointColor = isHovered ? "#1D4ED8" : "#3B82F6";
              let pointSize = compact ? (isHovered ? 6 : 4) : (isHovered ? 7 : 5);
              let pointShape = "circle";

              if (isAnomaly) {
                // Set color based on severity with more vibrant colors
                if (anomalyData.severity === 'high') {
                  pointColor = isHovered ? "#DC2626" : "#EF4444"; // bright red
                } else if (anomalyData.severity === 'medium') {
                  pointColor = isHovered ? "#D97706" : "#F59E0B"; // bright amber
                } else {
                  pointColor = isHovered ? "#2563EB" : "#3B82F6"; // bright blue
                }

                // Significantly increase size for anomalies to make them more visible
                pointSize = compact
                  ? (isHovered ? 10 : 8)
                  : (isHovered ? 12 : 9);

                // Set shape based on type
                if (anomalyData.type === 'spike') {
                  pointShape = "triangle-up";
                } else if (anomalyData.type === 'drop') {
                  pointShape = "triangle-down";
                } else if (anomalyData.type === 'trend_break') {
                  pointShape = "diamond";
                } else if (anomalyData.type === 'volatility') {
                  pointShape = "square";
                }
              }

              // Render appropriate shape based on pointShape
              if (pointShape === "circle") {
                return (
                  <circle
                    key={globalIndex}
                    cx={xScale(i)}
                    cy={yScale(point[metric])}
                    r={pointSize}
                    fill={pointColor}
                    className={`transition-all duration-200 ${isAnomaly ? 'stroke-2 stroke-white dark:stroke-gray-800 drop-shadow-lg' : 'hover:drop-shadow-md'}`}
                    style={{ cursor: 'pointer' }}
                    onClick={() => handlePointClick(globalIndex)}
                  />
                );
              } else if (pointShape === "triangle-up") {
                const size = pointSize * 1.5;
                return (
                  <polygon
                    key={globalIndex}
                    points={`${xScale(i)},${yScale(point[metric]) - size} ${xScale(i) - size},${yScale(point[metric]) + size} ${xScale(i) + size},${yScale(point[metric]) + size}`}
                    fill={pointColor}
                    className={`transition-all duration-200 ${isAnomaly ? 'stroke-2 stroke-white dark:stroke-gray-800 drop-shadow-lg' : 'hover:drop-shadow-md'}`}
                    style={{ cursor: 'pointer' }}
                    onClick={() => handlePointClick(globalIndex)}
                  />
                );
              } else if (pointShape === "triangle-down") {
                const size = pointSize * 1.5;
                return (
                  <polygon
                    key={globalIndex}
                    points={`${xScale(i)},${yScale(point[metric]) + size} ${xScale(i) - size},${yScale(point[metric]) - size} ${xScale(i) + size},${yScale(point[metric]) - size}`}
                    fill={pointColor}
                    className={`transition-all duration-200 ${isAnomaly ? 'stroke-2 stroke-white dark:stroke-gray-800 drop-shadow-lg' : 'hover:drop-shadow-md'}`}
                    style={{ cursor: 'pointer' }}
                    onClick={() => handlePointClick(globalIndex)}
                  />
                );
              } else if (pointShape === "diamond") {
                const size = pointSize * 1.2;
                return (
                  <polygon
                    key={globalIndex}
                    points={`${xScale(i)},${yScale(point[metric]) - size} ${xScale(i) + size},${yScale(point[metric])} ${xScale(i)},${yScale(point[metric]) + size} ${xScale(i) - size},${yScale(point[metric])}`}
                    fill={pointColor}
                    className={`transition-all duration-200 ${isAnomaly ? 'stroke-2 stroke-white dark:stroke-gray-800 drop-shadow-lg' : 'hover:drop-shadow-md'}`}
                    style={{ cursor: 'pointer' }}
                    onClick={() => handlePointClick(globalIndex)}
                  />
                );
              } else if (pointShape === "square") {
                const size = pointSize * 1.2;
                return (
                  <rect
                    key={globalIndex}
                    x={xScale(i) - size}
                    y={yScale(point[metric]) - size}
                    width={size * 2}
                    height={size * 2}
                    fill={pointColor}
                    className={`transition-all duration-200 ${isAnomaly ? 'stroke-2 stroke-white dark:stroke-gray-800 drop-shadow-lg' : 'hover:drop-shadow-md'}`}
                    style={{ cursor: 'pointer' }}
                    onClick={() => handlePointClick(globalIndex)}
                  />
                );
              }
            })}
          </g>
        </g>

        {/* Y-axis labels */}
        <g transform={`translate(${padding.left - 10}, ${padding.top})`}>
          {(compact ? [0, 0.25, 0.5, 0.75, 1] : [0, 0.25, 0.5, 0.75, 1]).map((ratio) => {
            const value = min + (max - min) * (1 - ratio);
            return (
              <text
                key={`y-label-${ratio}`}
                x={0}
                y={chartHeight * ratio + 4}
                textAnchor="end"
                className={compact ? "text-sm fill-gray-600 dark:fill-gray-400" : "text-xs fill-gray-600 dark:fill-gray-400"}
              >
                {formatValue(value, metric)}
              </text>
            );
          })}
        </g>

        {/* X-axis labels - optimized for 24h display */}
        <g transform={`translate(${padding.left}, ${height - padding.bottom + 20})`}>
          {visibleData.map((point, i) => {
            const skipInterval = Math.max(1, Math.floor(visibleData.length / 6)); // Show 6-8 time labels
            if (i % skipInterval === 0 || i === visibleData.length - 1) {
              const date = new Date(point.timestamp);
              const isFirstOrLast = i === 0 || i === visibleData.length - 1;
              return (
                <text
                  key={`x-label-${i}`}
                  x={xScale(i)}
                  y={0}
                  textAnchor="middle"
                  className="text-xs fill-gray-600 dark:fill-gray-400"
                  fontWeight={isFirstOrLast ? "600" : "400"}
                >
                  {formatTime(point.timestamp)}
                </text>
              );
            }
            return null;
          })}

          {/* Add time range indicator */}
          {visibleData.length > 0 && (
            <text
              x={chartWidth / 2}
              y={25}
              textAnchor="middle"
              className="text-xs fill-gray-500 dark:fill-gray-500"
            >
              最近24小时数据
            </text>
          )}
        </g>
      </svg>

      {/* Tooltip */}
      {chartState.tooltipPosition && chartState.hoveredPoint !== null && (
        <div
          className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 pointer-events-none"
          style={{
            left: chartState.tooltipPosition.x + 10,
            top: chartState.tooltipPosition.y - 10,
            transform: 'translateY(-100%)',
          }}
        >
          <div className="text-sm font-medium text-gray-900 dark:text-white">
            {formatDate(data[chartState.hoveredPoint].timestamp)}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {getMetricLabel(metric)}: {formatValue(data[chartState.hoveredPoint][metric], metric)}
          </div>
        </div>
      )}

      {/* Detail Panel */}
      {chartState.selectedPoint !== null && (
        <ChartDetailPanel
          point={data[chartState.selectedPoint]}
          anomalies={anomalies}
          onClose={closeDetailPanel}
        />
      )}
    </div>
  );
};

export default InteractiveTimeSeriesChart;
