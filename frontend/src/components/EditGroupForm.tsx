import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Group, GroupFormData, Server } from '@/types'
import { useGroupData } from '@/hooks/useGroupData'
import { useServerData } from '@/hooks/useServerData'
import { ToggleGroup } from './ui/ToggleGroup'
import { generateSecureBearerKey } from '@/utils/key'

interface EditGroupFormProps {
  group: Group
  onEdit: () => void
  onCancel: () => void
}

const EditGroupForm = ({ group, onEdit, onCancel }: EditGroupFormProps) => {
  const { t } = useTranslation()
  const { updateGroup } = useGroupData()
  const { servers } = useServerData()
  const [availableServers, setAvailableServers] = useState<Server[]>([])
  const [error, setError] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState<GroupFormData>({
    name: group.name,
    description: group.description || '',
    servers: group.servers || [],
    bearerAuthKey: group.bearerAuthKey || ''
  })
  const [showBearerKey, setShowBearerKey] = useState(false)

  useEffect(() => {
    // Filter available servers (enabled only)
    setAvailableServers(servers.filter(server => server.enabled !== false))
  }, [servers])

  const validateBearerKeyFormat = (bearerKey: string): boolean => {
    if (!bearerKey) return true // Empty is valid (optional field)
    const bearerKeyRegex = /^[a-zA-Z0-9._-]+$/
    return bearerKey.length >= 8 && bearerKey.length <= 256 && bearerKeyRegex.test(bearerKey)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleGenerateKey = () => {
    try {
      const newKey = generateSecureBearerKey()
      setFormData(prev => ({
        ...prev,
        bearerAuthKey: newKey
      }))
      // 生成密钥后自动显示，方便用户查看
      setShowBearerKey(true)
    } catch (error) {
      console.error('Failed to generate key:', error)
      setError(t('groups.keyGenerationFailed'))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)

    try {
      if (!formData.name.trim()) {
        setError(t('groups.nameRequired'))
        setIsSubmitting(false)
        return
      }

      // Validate Bearer key format if provided
      if (formData.bearerAuthKey && !validateBearerKeyFormat(formData.bearerAuthKey)) {
        setError(t('groups.bearerAuthKeyInvalid'))
        setIsSubmitting(false)
        return
      }

      const result = await updateGroup(group.id, {
        name: formData.name,
        description: formData.description,
        servers: formData.servers,
        bearerAuthKey: formData.bearerAuthKey
      })

      if (!result) {
        setError(t('groups.updateError'))
        setIsSubmitting(false)
        return
      }

      onEdit()
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err))
      setIsSubmitting(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full">
        <div className="p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">{t('groups.edit')}</h2>

          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="name">
                {t('groups.name')} *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline form-input"
                placeholder={t('groups.namePlaceholder')}
                required
              />
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="description">
                {t('groups.description')}
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline form-input"
                placeholder={t('groups.descriptionPlaceholder')}
                rows={3}
              />
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="bearerAuthKey">
                {t('groups.bearerAuthKey')}
              </label>
              <div className="relative">
                <input
                  type={showBearerKey ? "text" : "password"}
                  id="bearerAuthKey"
                  name="bearerAuthKey"
                  value={formData.bearerAuthKey}
                  onChange={handleChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 pr-10 text-gray-700 leading-tight focus:outline-none focus:shadow-outline form-input"
                  placeholder={t('groups.bearerAuthKeyPlaceholder')}
                />
                {formData.bearerAuthKey && (
                  <button
                    type="button"
                    onClick={() => setShowBearerKey(!showBearerKey)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                  >
                    {showBearerKey ? (
                      <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                      </svg>
                    ) : (
                      <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    )}
                  </button>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {t('groups.bearerAuthKeyDescription')}
              </p>
              <button
                type="button"
                onClick={handleGenerateKey}
                className="mt-2 inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3a1 1 0 011-1h2.586l6.243-6.243A6 6 0 0121 9z" />
                </svg>
                {t('groups.generateSecureKey')}
              </button>
            </div>

            <ToggleGroup
              className="mb-6"
              label={t('groups.servers')}
              noOptionsText={t('groups.noServerOptions')}
              values={formData.servers}
              options={availableServers.map(server => ({
                value: server.name,
                label: server.name
              }))}
              onChange={(servers) => setFormData(prev => ({ ...prev, servers }))}
            />

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 btn-secondary"
                disabled={isSubmitting}
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? t('common.submitting') : t('common.save')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default EditGroupForm