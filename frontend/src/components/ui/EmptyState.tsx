import React from 'react';
import { useTranslation } from 'react-i18next';

interface EmptyStateProps {
  icon?: React.ReactNode;
  title?: string;
  description?: string;
  action?: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  action,
  className = '',
  size = 'md'
}) => {
  const { t } = useTranslation();

  // Default icon if none provided
  const defaultIcon = (
    <svg 
      className={`mx-auto ${
        size === 'sm' ? 'h-8 w-8' : 
        size === 'lg' ? 'h-16 w-16' : 'h-12 w-12'
      } text-gray-400 dark:text-gray-500`} 
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
      />
    </svg>
  );

  const sizeClasses = {
    sm: 'py-4',
    md: 'py-8',
    lg: 'py-12'
  };

  const textSizeClasses = {
    sm: {
      title: 'text-sm font-medium',
      description: 'text-xs'
    },
    md: {
      title: 'text-base font-medium',
      description: 'text-sm'
    },
    lg: {
      title: 'text-lg font-medium',
      description: 'text-base'
    }
  };

  return (
    <div className={`text-center ${sizeClasses[size]} ${className}`}>
      {/* Icon */}
      <div className="mb-4">
        {icon || defaultIcon}
      </div>

      {/* Title */}
      {title && (
        <h3 className={`${textSizeClasses[size].title} text-gray-900 dark:text-gray-100 mb-2`}>
          {title}
        </h3>
      )}

      {/* Description */}
      {description && (
        <p className={`${textSizeClasses[size].description} text-gray-500 dark:text-gray-400 mb-4`}>
          {description}
        </p>
      )}

      {/* Action */}
      {action && (
        <div className="mt-4">
          {action}
        </div>
      )}
    </div>
  );
};

// Predefined empty state variants for common scenarios
export const NoDataEmptyState: React.FC<{ 
  title?: string; 
  description?: string; 
  action?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
}> = ({ 
  title, 
  description, 
  action,
  size = 'md'
}) => {
  const { t } = useTranslation();
  
  const noDataIcon = (
    <svg 
      className={`mx-auto ${
        size === 'sm' ? 'h-8 w-8' : 
        size === 'lg' ? 'h-16 w-16' : 'h-12 w-12'
      } text-gray-400 dark:text-gray-500`} 
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" 
      />
    </svg>
  );

  return (
    <EmptyState
      icon={noDataIcon}
      title={title || t('analytics.noData', 'No data available')}
      description={description || t('analytics.noDataDescription', 'There is no data to display for the selected criteria.')}
      action={action}
      size={size}
    />
  );
};

export const ErrorEmptyState: React.FC<{ 
  title?: string; 
  description?: string; 
  action?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
}> = ({ 
  title, 
  description, 
  action,
  size = 'md'
}) => {
  const { t } = useTranslation();
  
  const errorIcon = (
    <svg 
      className={`mx-auto ${
        size === 'sm' ? 'h-8 w-8' : 
        size === 'lg' ? 'h-16 w-16' : 'h-12 w-12'
      } text-red-400 dark:text-red-500`} 
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
      />
    </svg>
  );

  return (
    <EmptyState
      icon={errorIcon}
      title={title || t('analytics.error', 'Error loading data')}
      description={description || t('analytics.errorDescription', 'An error occurred while loading the data. Please try again.')}
      action={action}
      size={size}
    />
  );
};

export const LoadingEmptyState: React.FC<{ 
  title?: string; 
  description?: string;
  size?: 'sm' | 'md' | 'lg';
}> = ({ 
  title, 
  description,
  size = 'md'
}) => {
  const { t } = useTranslation();
  
  const loadingIcon = (
    <svg 
      className={`mx-auto ${
        size === 'sm' ? 'h-8 w-8' : 
        size === 'lg' ? 'h-16 w-16' : 'h-12 w-12'
      } text-blue-400 dark:text-blue-500 animate-spin`} 
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
      />
    </svg>
  );

  return (
    <EmptyState
      icon={loadingIcon}
      title={title || t('app.loading', 'Loading...')}
      description={description || t('analytics.loadingDescription', 'Please wait while we load your data.')}
      size={size}
    />
  );
};

export default EmptyState;
