import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  pageSize?: number;
  totalItems?: number;
  onPageSizeChange?: (pageSize: number) => void;
  pageSizeOptions?: number[];
  showPageSizeSelector?: boolean;
  showDetailedInfo?: boolean;
  showJumpToPage?: boolean;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  pageSize = 20,
  totalItems = 0,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  showPageSizeSelector = false,
  showDetailedInfo = false,
  showJumpToPage = false
}) => {
  const { t } = useTranslation();
  const [jumpToPageInput, setJumpToPageInput] = useState('');

  // Calculate display range
  const startItem = totalItems > 0 ? (currentPage - 1) * pageSize + 1 : 0;
  const endItem = Math.min(currentPage * pageSize, totalItems);

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    if (onPageSizeChange) {
      onPageSizeChange(newPageSize);
    }
  };

  // Handle jump to page
  const handleJumpToPage = () => {
    const pageNumber = parseInt(jumpToPageInput);
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      onPageChange(pageNumber);
      setJumpToPageInput('');
    }
  };

  // Handle jump input key press
  const handleJumpInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage();
    }
  };

  // Generate page buttons
  const getPageButtons = () => {
    const buttons = [];
    const maxDisplayedPages = 5; // Maximum number of page buttons to display

    // Always display first page
    buttons.push(
      <button
        key="first"
        onClick={() => onPageChange(1)}
        className={`px-2 sm:px-3 py-1 mx-0.5 sm:mx-1 rounded text-sm ${currentPage === 1
          ? 'bg-blue-500 text-white btn-primary'
          : 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 btn-secondary'
          }`}
      >
        1
      </button>
    );

    // Start range
    const startPage = Math.max(2, currentPage - Math.floor(maxDisplayedPages / 2));

    // If we're showing ellipsis after first page
    if (startPage > 2) {
      buttons.push(
        <span key="ellipsis1" className="px-2 sm:px-3 py-1 text-sm text-gray-600 dark:text-gray-400">
          ...
        </span>
      );
    }

    // Middle pages
    for (let i = startPage; i <= Math.min(totalPages - 1, startPage + maxDisplayedPages - 3); i++) {
      buttons.push(
        <button
          key={i}
          onClick={() => onPageChange(i)}
          className={`px-2 sm:px-3 py-1 mx-0.5 sm:mx-1 rounded text-sm ${currentPage === i
            ? 'bg-blue-500 text-white btn-primary'
            : 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 btn-secondary'
            }`}
        >
          {i}
        </button>
      );
    }

    // If we're showing ellipsis before last page
    if (startPage + maxDisplayedPages - 3 < totalPages - 1) {
      buttons.push(
        <span key="ellipsis2" className="px-2 sm:px-3 py-1 text-sm text-gray-600 dark:text-gray-400">
          ...
        </span>
      );
    }

    // Always display last page if there's more than one page
    if (totalPages > 1) {
      buttons.push(
        <button
          key="last"
          onClick={() => onPageChange(totalPages)}
          className={`px-2 sm:px-3 py-1 mx-0.5 sm:mx-1 rounded text-sm ${currentPage === totalPages
            ? 'bg-blue-500 text-white btn-primary'
            : 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 btn-secondary'
            }`}
        >
          {totalPages}
        </button>
      );
    }

    return buttons;
  };

  // If there's only one page, don't render pagination
  if (totalPages <= 1) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* Page Size Selector and Detailed Info */}
      {(showPageSizeSelector || showDetailedInfo) && (
        <div className="flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
          {/* Page Size Selector */}
          {showPageSizeSelector && onPageSizeChange && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('pagination.itemsPerPage', 'Items per page')}:
              </span>
              <select
                value={pageSize}
                onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                {pageSizeOptions.map(option => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Detailed Info */}
          {showDetailedInfo && totalItems > 0 && (
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {t('pagination.showing', 'Showing')} {startItem}-{endItem} {t('pagination.of', 'of')} {totalItems} {t('pagination.items', 'items')}
            </div>
          )}
        </div>
      )}

      {/* Main Pagination Controls */}
      <div className="flex flex-col sm:flex-row justify-center items-center space-y-2 sm:space-y-0">
        {/* Page info for mobile */}
        <div className="text-sm text-gray-600 dark:text-gray-400 sm:hidden mb-2">
          {t('pagination.page', { page: currentPage })} {t('pagination.of', { total: totalPages })}
        </div>

        <div className="flex items-center">
          <button
            onClick={() => onPageChange(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className={`px-2 sm:px-3 py-1 rounded mr-1 sm:mr-2 text-sm ${currentPage === 1
              ? 'bg-gray-100 dark:bg-gray-600 text-gray-400 cursor-not-allowed'
              : 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 btn-secondary'
              }`}
          >
            <span className="hidden sm:inline">&laquo; {t('pagination.previous')}</span>
            <span className="sm:hidden">&laquo;</span>
          </button>

          <div className="flex">{getPageButtons()}</div>

          <button
            onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className={`px-2 sm:px-3 py-1 rounded ml-1 sm:ml-2 text-sm ${currentPage === totalPages
              ? 'bg-gray-100 dark:bg-gray-600 text-gray-400 cursor-not-allowed'
              : 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 btn-secondary'
              }`}
          >
            <span className="hidden sm:inline">{t('pagination.next')} &raquo;</span>
            <span className="sm:hidden">&raquo;</span>
          </button>
        </div>

        {/* Page info for desktop */}
        <div className="hidden sm:block text-sm text-gray-600 dark:text-gray-400 ml-4">
          {t('pagination.page', { page: currentPage })} {t('pagination.of', { total: totalPages })}
        </div>
      </div>

      {/* Jump to Page */}
      {showJumpToPage && totalPages > 5 && (
        <div className="flex justify-center items-center space-x-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {t('pagination.jumpToPage', 'Jump to page')}:
          </span>
          <input
            type="number"
            min="1"
            max={totalPages}
            value={jumpToPageInput}
            onChange={(e) => setJumpToPageInput(e.target.value)}
            onKeyPress={handleJumpInputKeyPress}
            className="w-16 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm text-center bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            placeholder="1"
          />
          <button
            onClick={handleJumpToPage}
            disabled={!jumpToPageInput || parseInt(jumpToPageInput) < 1 || parseInt(jumpToPageInput) > totalPages}
            className="px-3 py-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white disabled:text-gray-500 rounded text-sm"
          >
            {t('pagination.go', 'Go')}
          </button>
        </div>
      )}
    </div>
  );
};

export default Pagination;