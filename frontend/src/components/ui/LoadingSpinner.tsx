import React from 'react';
import { useTranslation } from 'react-i18next';

interface LoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  color?: 'blue' | 'gray' | 'green' | 'red' | 'yellow' | 'purple';
  text?: string;
  showText?: boolean;
  className?: string;
  inline?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'blue',
  text,
  showText = false,
  className = '',
  inline = false
}) => {
  const { t } = useTranslation();

  const sizeClasses = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  };

  const colorClasses = {
    blue: 'text-blue-600 dark:text-blue-400',
    gray: 'text-gray-600 dark:text-gray-400',
    green: 'text-green-600 dark:text-green-400',
    red: 'text-red-600 dark:text-red-400',
    yellow: 'text-yellow-600 dark:text-yellow-400',
    purple: 'text-purple-600 dark:text-purple-400'
  };

  const textSizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  };

  const containerClasses = inline 
    ? 'inline-flex items-center space-x-2'
    : 'flex flex-col items-center justify-center space-y-2';

  const displayText = text || t('app.loading', 'Loading...');

  return (
    <div className={`${containerClasses} ${className}`}>
      {/* Spinner */}
      <svg
        className={`animate-spin ${sizeClasses[size]} ${colorClasses[color]}`}
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>

      {/* Text */}
      {showText && (
        <span className={`${textSizeClasses[size]} ${colorClasses[color]} font-medium`}>
          {displayText}
        </span>
      )}
    </div>
  );
};

// Predefined spinner variants for common use cases
export const ButtonSpinner: React.FC<{ size?: 'xs' | 'sm' | 'md' }> = ({ 
  size = 'sm' 
}) => (
  <LoadingSpinner 
    size={size} 
    color="gray" 
    inline 
    className="mr-2" 
  />
);

export const PageSpinner: React.FC<{ 
  text?: string;
  size?: 'md' | 'lg' | 'xl';
}> = ({ 
  text, 
  size = 'lg' 
}) => {
  const { t } = useTranslation();
  
  return (
    <div className="flex items-center justify-center min-h-[200px]">
      <LoadingSpinner 
        size={size} 
        color="blue" 
        text={text || t('app.loading', 'Loading...')}
        showText 
      />
    </div>
  );
};

export const InlineSpinner: React.FC<{ 
  text?: string;
  size?: 'xs' | 'sm' | 'md';
  color?: 'blue' | 'gray' | 'green' | 'red' | 'yellow' | 'purple';
}> = ({ 
  text, 
  size = 'sm',
  color = 'blue'
}) => (
  <LoadingSpinner 
    size={size} 
    color={color}
    text={text}
    showText={!!text}
    inline 
  />
);

export const CardSpinner: React.FC<{ 
  text?: string;
  height?: string;
}> = ({ 
  text,
  height = '120px'
}) => {
  const { t } = useTranslation();
  
  return (
    <div 
      className="flex items-center justify-center bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
      style={{ minHeight: height }}
    >
      <LoadingSpinner 
        size="md" 
        color="blue" 
        text={text || t('app.loading', 'Loading...')}
        showText 
      />
    </div>
  );
};

// Overlay spinner for full-screen loading
export const OverlaySpinner: React.FC<{ 
  text?: string;
  visible?: boolean;
}> = ({ 
  text,
  visible = true
}) => {
  const { t } = useTranslation();
  
  if (!visible) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl">
        <LoadingSpinner 
          size="lg" 
          color="blue" 
          text={text || t('app.loading', 'Loading...')}
          showText 
        />
      </div>
    </div>
  );
};

export default LoadingSpinner;
