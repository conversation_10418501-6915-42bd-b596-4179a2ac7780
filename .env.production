# 生产环境配置文件
# 复制此文件为.env并根据实际环境修改配置

# 应用配置
PORT=5000
NODE_ENV=production

# 数据库配置 - 生产环境
DB_URL=******************************************************/mcphub

# 应用功能配置
REQUEST_TIMEOUT=60000
BASE_PATH=

# 智能路由配置
ENABLE_SMART_ROUTING=true
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE_URL=https://api.openai.com/v1

# Docker构建配置
HTTP_PROXY=
HTTPS_PROXY=
INSTALL_EXT=true

# 安全配置 (生产环境必须设置)
JWT_SECRET=your_jwt_secret_here
ADMIN_PASSWORD=your_admin_password_here

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
