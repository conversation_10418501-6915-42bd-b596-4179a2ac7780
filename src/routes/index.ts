import express, { Request, Response } from 'express';
import { check } from 'express-validator';
import config from '../config/index.js';
import {
  getAllServers,
  getAllSettings,
  createServer,
  updateServer,
  deleteServer,
  toggleServer,
  toggleTool,
  updateToolDescription,
  updateSystemConfig,
} from '../controllers/serverController.js';
import {
  getGroups,
  getGroup,
  createNewGroup,
  updateExistingGroup,
  deleteExistingGroup,
  addServerToExistingGroup,
  removeServerFromExistingGroup,
  getGroupServers,
  updateGroupServersBatch,
} from '../controllers/groupController.js';
import {
  getAllMarketServers,
  getMarketServer,
  getAllMarketCategories,
  getAllMarketTags,
  searchMarketServersByQuery,
  getMarketServersByCategory,
  getMarketServersByTag,
  migrateData,
  getMigrationStatus,
  rollbackMigration,
} from '../controllers/marketController.js';
import { login, register, getCurrentUser, changePassword } from '../controllers/authController.js';
import { getAllLogs, clearLogs, streamLogs } from '../controllers/logController.js';
import { getRuntimeConfig, getPublicConfig } from '../controllers/configController.js';
import { callTool } from '../controllers/toolController.js';
import { uploadDxtFile, uploadMiddleware } from '../controllers/dxtController.js';
import {
  getHistoryData,
  getHistorySummary,
  getFilterOptions,
  getTrendsData,
  exportHistoryData,
  generateRecentTestData,
  cleanupTestData,
} from '../controllers/analyticsController.js';
import {
  getOverviewData,
  getOverviewSummary,
} from '../controllers/overviewController.js';
import {
  getSystemHealth,
  getPerformanceMetrics,
  getSystemStatus,
  getDatabaseStatus,
  getServersStatus,
  getRealtimeData,
  getMonitoringHistory,
  cleanupMonitoringData,
} from '../controllers/monitoringController.js';
import { auth } from '../middlewares/auth.js';

const router = express.Router();
  // Debug endpoint for enabling/disabling client IP debugging
  if (process.env.NODE_ENV === 'development') {
    router.post('/debug/client-ip/toggle', (req: Request, res: Response) => {
      const { enabled } = req.body as { enabled: boolean };
      
      if (enabled === true) {
        process.env.DEBUG_CLIENT_IP = 'true';
      } else {
        delete process.env.DEBUG_CLIENT_IP;
      }
      
      res.json({
        success: true,
        data: {
          debugEnabled: process.env.DEBUG_CLIENT_IP === 'true',
          message: `Client IP debugging ${enabled ? 'enabled' : 'disabled'}`
        }
      });
    });
  }
  // Debug endpoint for testing client IP extraction (only in development)
  if (process.env.NODE_ENV === 'development') {
    router.get('/debug/client-ip', (req: Request, res: Response) => {
      const { getRequestClientIP, testClientIPExtraction } = require('../middlewares/clientInfo.js');
      
      const clientIP = getRequestClientIP(req);
      const headers = req.headers;
      
      // Test with current request headers
      const testResult = testClientIPExtraction({
        'x-forwarded-for': headers['x-forwarded-for'] as string,
        'x-real-ip': headers['x-real-ip'] as string,
        'x-client-ip': headers['x-client-ip'] as string,
        'cf-connecting-ip': headers['cf-connecting-ip'] as string,
        'mock-req-ip': (req as any).ip,
        'mock-connection-remote': (req as any).connection?.remoteAddress,
        'mock-socket-remote': (req as any).socket?.remoteAddress
      });

      res.json({
        success: true,
        data: {
          extractedIP: clientIP,
          requestInfo: {
            ip: (req as any).ip,
            ips: (req as any).ips,
            connection: {
              remoteAddress: (req as any).connection?.remoteAddress,
              remoteFamily: (req as any).connection?.remoteFamily,
              remotePort: (req as any).connection?.remotePort
            },
            socket: {
              remoteAddress: (req as any).socket?.remoteAddress,
              remoteFamily: (req as any).socket?.remoteFamily,
              remotePort: (req as any).socket?.remotePort
            }
          },
          headers: {
            'x-forwarded-for': headers['x-forwarded-for'],
            'x-real-ip': headers['x-real-ip'],
            'x-client-ip': headers['x-client-ip'],
            'cf-connecting-ip': headers['cf-connecting-ip'],
            'user-agent': headers['user-agent']
          },
          testResult,
          clientInfo: (req as any).clientInfo
        }
      });
    });

    // Test MCP client IP endpoint - 模拟MCP客户端连接
    router.post('/debug/mcp-client-ip', (req: Request, res: Response) => {
      const { getRequestClientIP } = require('../middlewares/clientInfo.js');
      const clientIP = getRequestClientIP(req);
      const headers = req.headers;

      console.log('[debug/mcp-client-ip] Request details:', {
        method: req.method,
        url: req.url,
        headers: {
          'x-forwarded-for': headers['x-forwarded-for'],
          'x-real-ip': headers['x-real-ip'],
          'x-client-ip': headers['x-client-ip'],
          'cf-connecting-ip': headers['cf-connecting-ip'],
          'user-agent': headers['user-agent']
        },
        connection: {
          remoteAddress: (req as any).connection?.remoteAddress,
          remoteFamily: (req as any).connection?.remoteFamily,
          remotePort: (req as any).connection?.remotePort
        },
        socket: {
          remoteAddress: (req as any).socket?.remoteAddress,
          remoteFamily: (req as any).socket?.remoteFamily,
          remotePort: (req as any).socket?.remotePort
        },
        clientInfo: (req as any).clientInfo,
        extractedIP: clientIP
      });

      res.json({
        success: true,
        message: 'MCP client IP test endpoint',
        data: {
          extractedIP: clientIP,
          timestamp: new Date().toISOString(),
          headers: Object.fromEntries(Object.entries(headers)),
          clientInfo: (req as any).clientInfo
        }
      });
    });
  }

export const initRoutes = (app: express.Application): void => {
  // API routes protected by auth middleware in middlewares/index.ts
  router.get('/servers', getAllServers);
  router.get('/settings', getAllSettings);
  router.post('/servers', createServer);
  router.put('/servers/:name', updateServer);
  router.delete('/servers/:name', deleteServer);
  router.post('/servers/:name/toggle', toggleServer);
  router.post('/servers/:serverName/tools/:toolName/toggle', toggleTool);
  router.put('/servers/:serverName/tools/:toolName/description', updateToolDescription);
  router.put('/system-config', updateSystemConfig);

  // Group management routes
  router.get('/groups', getGroups);
  router.get('/groups/:id', getGroup);
  router.post('/groups', createNewGroup);
  router.put('/groups/:id', updateExistingGroup);
  router.delete('/groups/:id', deleteExistingGroup);
  router.post('/groups/:id/servers', addServerToExistingGroup);
  router.delete('/groups/:id/servers/:serverName', removeServerFromExistingGroup);
  router.get('/groups/:id/servers', getGroupServers);
  // New route for batch updating servers in a group
  router.put('/groups/:id/servers/batch', updateGroupServersBatch);

  // Tool management routes
  router.post('/tools/call/:server', callTool);

  // DXT upload routes
  router.post('/dxt/upload', uploadMiddleware, uploadDxtFile);

  // Market routes
  router.get('/market/servers', getAllMarketServers);
  router.get('/market/servers/search', searchMarketServersByQuery);
  router.get('/market/servers/:name', getMarketServer);
  router.get('/market/categories', getAllMarketCategories);
  router.get('/market/categories/:category', getMarketServersByCategory);
  router.get('/market/tags', getAllMarketTags);
  router.get('/market/tags/:tag', getMarketServersByTag);

  // Log routes
  router.get('/logs', getAllLogs);
  router.delete('/logs', clearLogs);
  router.get('/logs/stream', streamLogs);

  // Data migration routes
  router.post('/migration/migrate', migrateData);
  router.get('/migration/status', getMigrationStatus);
  router.post('/migration/rollback', rollbackMigration);

  // Analytics routes
  router.get(
    '/analytics/history',
    [
      check(
        'startTime',
        'Start time is required and must be a valid ISO 8601 timestamp',
      ).isISO8601(),
      check('endTime', 'End time is required and must be a valid ISO 8601 timestamp').isISO8601(),
      check('page', 'Page must be a positive integer').optional().isInt({ min: 1, max: 10000 }),
      check('pageSize', 'Page size must be between 1 and 100000')
        .optional()
        .isInt({ min: 1, max: 100000 }),
      check('success', 'Success must be a boolean').optional().isBoolean(),
      check('groupBy', 'Group by must be one of: hour, day, week, month')
        .optional()
        .isIn(['hour', 'day', 'week', 'month']),
      check('serverNames', 'Server names must be a comma-separated string')
        .optional()
        .isString()
        .isLength({ max: 1000 }),
      check('toolNames', 'Tool names must be a comma-separated string')
        .optional()
        .isString()
        .isLength({ max: 1000 }),
      check('clientIps', 'Client IPs must be a comma-separated string')
        .optional()
        .isString()
        .isLength({ max: 1000 }),
      check('metrics', 'Metrics must be a comma-separated string')
        .optional()
        .matches(/^(count|success_rate|avg_response_time)(,(count|success_rate|avg_response_time))*$/),
    ],
    getHistoryData,
  );

  router.get(
    '/analytics/summary',
    [
      check(
        'startTime',
        'Start time is required and must be a valid ISO 8601 timestamp',
      ).isISO8601(),
      check('endTime', 'End time is required and must be a valid ISO 8601 timestamp').isISO8601(),
    ],
    getHistorySummary,
  );

  router.get('/analytics/filters', getFilterOptions);

  // Overview data routes
  router.get('/analytics/overview', getOverviewData);
  router.get('/analytics/overview/summary', getOverviewSummary);

  // History data export route
  router.get(
    '/analytics/history/export',
    [
      check(
        'startTime',
        'Start time is required and must be a valid ISO 8601 timestamp',
      ).isISO8601(),
      check('endTime', 'End time is required and must be a valid ISO 8601 timestamp').isISO8601(),
      check('format', 'Export format must be csv or excel').optional().isIn(['csv', 'excel']),
      check('serverNames', 'Server names must be a comma-separated string')
        .optional()
        .isString()
        .isLength({ max: 1000 }),
      check('toolNames', 'Tool names must be a comma-separated string')
        .optional()
        .isString()
        .isLength({ max: 1000 }),
      check('clientIps', 'Client IPs must be a comma-separated string')
        .optional()
        .isString()
        .isLength({ max: 1000 }),
      check('success', 'Success must be a boolean').optional().isBoolean(),
    ],
    exportHistoryData,
  );

  // Trends analysis route
  router.get(
    '/analytics/trends',
    [
      check('timeRange', 'Time range must be one of: 1h, 24h, 7d, 30d, custom')
        .optional()
        .isIn(['1h', '24h', '7d', '30d', 'custom']),
      check('granularity', 'Granularity must be one of: hour, day, week')
        .optional()
        .isIn(['hour', 'day', 'week']),
      check(
        'metrics',
        'Metrics must be a comma-separated list of: calls, success_rate, response_time',
      )
        .optional()
        .matches(/^(calls|success_rate|response_time)(,(calls|success_rate|response_time))*$/),
      check('startTime', 'Start time must be a valid ISO 8601 timestamp').optional().isISO8601(),
      check('endTime', 'End time must be a valid ISO 8601 timestamp').optional().isISO8601(),
      check('serverNames', 'Server names must be a comma-separated string')
        .optional()
        .isString()
        .isLength({ max: 1000 }),
    ],
    getTrendsData,
  );

  // Generate recent test data route
  router.post(
    '/analytics/generate-recent-test-data',
    [check('count', 'Count must be between 1 and 1000').optional().isInt({ min: 1, max: 1000 })],
    generateRecentTestData,
  );

  // Cleanup test data route
  router.delete('/analytics/test-data', cleanupTestData);

  // Monitoring routes
  router.get('/monitoring/health', getSystemHealth);
  router.get('/monitoring/metrics', getPerformanceMetrics);
  router.get('/monitoring/status', getSystemStatus);
  router.get('/monitoring/database', getDatabaseStatus);
  router.get('/monitoring/servers', getServersStatus);
  router.get('/monitoring/realtime', getRealtimeData);
  router.get('/monitoring/history', getMonitoringHistory);
  router.delete('/monitoring/cleanup', cleanupMonitoringData);

  // Auth routes - move to router instead of app directly
  router.post(
    '/auth/login',
    [
      check('username', 'Username is required').not().isEmpty(),
      check('password', 'Password is required').not().isEmpty(),
    ],
    login,
  );

  router.post(
    '/auth/register',
    [
      check('username', 'Username is required').not().isEmpty(),
      check('password', 'Password must be at least 6 characters').isLength({ min: 6 }),
    ],
    register,
  );

  router.get('/auth/user', auth, getCurrentUser);

  // Add change password route
  router.post(
    '/auth/change-password',
    [
      auth,
      check('currentPassword', 'Current password is required').not().isEmpty(),
      check('newPassword', 'New password must be at least 6 characters').isLength({ min: 6 }),
    ],
    changePassword,
  );

  // Runtime configuration endpoint (no auth required for frontend initialization)
  app.get(`${config.basePath}/config`, getRuntimeConfig);

  // Public configuration endpoint (no auth required to check skipAuth setting)
  app.get(`${config.basePath}/public-config`, getPublicConfig);

  app.use(`${config.basePath}/api`, router);
};

export default router;
