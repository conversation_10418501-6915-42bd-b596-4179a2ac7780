// WebSocket types and interfaces for real-time data communication

export interface WebSocketMessage {
  type: string;
  data?: any;
  timestamp?: string;
  id?: string;
}

// Client to server messages
export interface WSAuthMessage extends WebSocketMessage {
  type: 'auth';
  data: {
    token: string;
  };
}

export interface WSSubscribeMessage extends WebSocketMessage {
  type: 'subscribe';
  data: {
    channels: string[];
  };
}

export interface WSUnsubscribeMessage extends WebSocketMessage {
  type: 'unsubscribe';
  data: {
    channels: string[];
  };
}

export interface WSPingMessage extends WebSocketMessage {
  type: 'ping';
}

// Server to client messages
export interface WSAuthResponseMessage extends WebSocketMessage {
  type: 'auth_response';
  data: {
    success: boolean;
    message?: string;
  };
}

export interface WSDataMessage extends WebSocketMessage {
  type: 'data';
  data: {
    channel: string;
    payload: any;
  };
}

export interface WSErrorMessage extends WebSocketMessage {
  type: 'error';
  data: {
    message: string;
    code?: string;
  };
}

export interface WSPongMessage extends WebSocketMessage {
  type: 'pong';
}

export interface WSConnectionStatusMessage extends WebSocketMessage {
  type: 'connection_status';
  data: {
    status: 'connected' | 'disconnected' | 'reconnecting';
    clientCount?: number;
  };
}

// Union type for all client messages
export type ClientMessage =
  | WSAuthMessage
  | WSSubscribeMessage
  | WSUnsubscribeMessage
  | WSPingMessage;

// Union type for all server messages
export type ServerMessage =
  | WSAuthResponseMessage
  | WSDataMessage
  | WSErrorMessage
  | WSPongMessage
  | WSConnectionStatusMessage;

// WebSocket client connection info
export interface WSClientInfo {
  id: string;
  authenticated: boolean;
  userId?: string;
  subscriptions: Set<string>;
  lastPing?: Date;
  connectedAt: Date;
}

// Available subscription channels
export enum WSChannel {
  SYSTEM_HEALTH = 'system_health',
  PERFORMANCE_METRICS = 'performance_metrics',
  REALTIME_DATA = 'realtime_data',
  SERVER_STATUS = 'server_status',
  DATABASE_STATUS = 'database_status',
  MONITORING_ALERTS = 'monitoring_alerts',
}

// Real-time data types for different channels
export interface SystemHealthData {
  status: 'healthy' | 'warning' | 'critical';
  services: {
    database: { status: 'up' | 'down'; responseTime: number };
    mcpServers: { status: 'up' | 'down'; count: number; active: number };
    cache: { status: 'up' | 'down'; size: number; hitRate: number };
  };
  lastCheck: string;
}

export interface PerformanceMetricsData {
  cpu: { usage: number; cores: number };
  memory: { total: number; used: number; free: number; usage: number };
  disk: { total: number; used: number; free: number; usage: number };
  database: { connections: number; responseTime: number; queriesPerMinute: number };
  api: {
    requestsPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
    activeConnections: number;
  };
}

export interface RealtimeData {
  timestamp: string;
  system: { cpu: number; memory: number; uptime: number };
  servers: { active: number; total: number };
  performance: { totalCalls: number; requestsPerMinute: number; avgResponseTime: number };
  database: { connected: boolean; responseTime: number };
}

// WebSocket service configuration
export interface WSServiceConfig {
  port?: number;
  pingInterval?: number;
  pongTimeout?: number;
  maxClients?: number;
  dataUpdateInterval?: number;
}
