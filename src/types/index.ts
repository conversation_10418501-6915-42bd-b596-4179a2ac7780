import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { RequestOptions } from '@modelcontextprotocol/sdk/shared/protocol.js';
import { SmartRoutingConfig } from '../utils/smartRouting.js';

// User interface
export interface IUser {
  username: string;
  password: string;
  isAdmin?: boolean;
}

// Group interface for server grouping
export interface IGroup {
  id: string; // Unique UUID for the group
  name: string; // Display name of the group
  description?: string; // Optional description of the group
  servers: string[]; // Array of server names that belong to this group
  bearerAuthKey?: string; // Optional Bearer authentication key for group-level auth
}

// Market server types
export interface MarketServerRepository {
  type: string;
  url: string;
}

export interface MarketServerAuthor {
  name: string;
}

export interface MarketServerInstallation {
  type: string;
  command: string;
  args: string[];
  env?: Record<string, string>;
}

export interface MarketServerArgument {
  description: string;
  required: boolean;
  example: string;
}

export interface MarketServerExample {
  title: string;
  description: string;
  prompt: string;
}

export interface MarketServerTool {
  name: string;
  description: string;
  inputSchema: Record<string, any>;
}

export interface MarketServer {
  name: string;
  display_name: string;
  description: string;
  repository: MarketServerRepository;
  homepage: string;
  author: MarketServerAuthor;
  license: string;
  categories: string[];
  tags: string[];
  examples: MarketServerExample[];
  installations: {
    [key: string]: MarketServerInstallation;
  };
  arguments: Record<string, MarketServerArgument>;
  tools: MarketServerTool[];
  is_official?: boolean;
}

// Represents the settings for MCP servers
export interface McpSettings {
  users?: IUser[]; // Array of user credentials and permissions
  mcpServers: {
    [key: string]: ServerConfig; // Key-value pairs of server names and their configurations
  };
  groups?: IGroup[]; // Array of server groups
  systemConfig?: {
    routing?: {
      enableGlobalRoute?: boolean; // Controls whether the /sse endpoint without group is enabled
      enableGroupNameRoute?: boolean; // Controls whether group routing by name is allowed
      enableBearerAuth?: boolean; // Controls whether bearer auth is enabled for group routes
      bearerAuthKey?: string; // The bearer auth key to validate against
      skipAuth?: boolean; // Controls whether authentication is required for frontend and API access
    };
    install?: {
      pythonIndexUrl?: string; // Python package repository URL (UV_DEFAULT_INDEX)
      npmRegistry?: string; // NPM registry URL (npm_config_registry)
    };
    smartRouting?: SmartRoutingConfig;
    // Add other system configuration sections here in the future
  };
}

// Configuration details for an individual server
export interface ServerConfig {
  type?: 'stdio' | 'sse' | 'streamable-http' | 'openapi'; // Type of server
  url?: string; // URL for SSE or streamable HTTP servers
  command?: string; // Command to execute for stdio-based servers
  args?: string[]; // Arguments for the command
  env?: Record<string, string>; // Environment variables
  headers?: Record<string, string>; // HTTP headers for SSE/streamable-http/openapi servers
  enabled?: boolean; // Flag to enable/disable the server
  keepAliveInterval?: number; // Keep-alive ping interval in milliseconds (default: 60000ms for SSE servers)
  tools?: Record<string, { enabled: boolean; description?: string }>; // Tool-specific configurations with enable/disable state and custom descriptions
  options?: Partial<Pick<RequestOptions, 'timeout' | 'resetTimeoutOnProgress' | 'maxTotalTimeout'>>; // MCP request options configuration
  // OpenAPI specific configuration
  openapi?: {
    url?: string; // OpenAPI specification URL
    schema?: Record<string, any>; // Complete OpenAPI JSON schema
    version?: string; // OpenAPI version (default: '3.1.0')
    security?: OpenAPISecurityConfig; // Security configuration for API calls
  };
}

// OpenAPI Security Configuration
export interface OpenAPISecurityConfig {
  type: 'none' | 'apiKey' | 'http' | 'oauth2' | 'openIdConnect';
  // API Key authentication
  apiKey?: {
    name: string; // Header/query/cookie name
    in: 'header' | 'query' | 'cookie';
    value: string; // The API key value
  };
  // HTTP authentication (Basic, Bearer, etc.)
  http?: {
    scheme: 'basic' | 'bearer' | 'digest'; // HTTP auth scheme
    bearerFormat?: string; // Bearer token format (e.g., JWT)
    credentials?: string; // Base64 encoded credentials for basic auth or bearer token
  };
  // OAuth2 (simplified - mainly for bearer tokens)
  oauth2?: {
    tokenUrl?: string; // Token endpoint for client credentials flow
    clientId?: string;
    clientSecret?: string;
    scopes?: string[]; // Required scopes
    token?: string; // Pre-obtained access token
  };
  // OpenID Connect
  openIdConnect?: {
    url: string; // OpenID Connect discovery URL
    clientId?: string;
    clientSecret?: string;
    token?: string; // Pre-obtained ID token
  };
}

// Information about a server's status and tools
export interface ServerInfo {
  name: string; // Unique name of the server
  status: 'connected' | 'connecting' | 'disconnected'; // Current connection status
  error: string | null; // Error message if any
  tools: ToolInfo[]; // List of tools available on the server
  client?: Client; // Client instance for communication (MCP clients)
  transport?: SSEClientTransport | StdioClientTransport | StreamableHTTPClientTransport; // Transport mechanism used
  openApiClient?: any; // OpenAPI client instance for openapi type servers
  options?: RequestOptions; // Options for requests
  createTime: number; // Timestamp of when the server was created
  enabled?: boolean; // Flag to indicate if the server is enabled
  keepAliveIntervalId?: NodeJS.Timeout; // Timer ID for keep-alive ping interval
  callStats?: ServerCallStats; // API call statistics
}

// Server call statistics interface
export interface ServerCallStats {
  totalCalls: number;
  lastCallTime?: number;
  successCalls: number;
  failedCalls: number;
  avgResponseTime?: number; // Average response time in milliseconds
}

// ==================== 数据持久化相关接口 ====================

// 工具调用详细记录接口
export interface ToolCallRecord {
  id?: string;
  serverName: string;
  toolName: string;
  callTime: Date;
  success: boolean;
  responseTime?: number; // 响应时间，毫秒
  userId?: string;
  clientIp?: string; // 客户端IP地址
  errorMessage?: string;
  requestParams?: Record<string, any>;
  responseData?: Record<string, any>;
  createdAt?: Date;
}

// 聚合统计数据接口
export interface AggregateStats {
  serverName: string;
  statDate: Date;
  statHour?: number; // null表示日级别统计
  totalCalls: number;
  successCalls: number;
  failedCalls: number;
  avgResponseTime?: number;
  minResponseTime?: number;
  maxResponseTime?: number;
  uniqueUsers: number;
  updatedAt: Date;
}

// 监控指标接口
export interface MonitoringMetric {
  id?: string;
  metricName: string;
  metricValue: number;
  metricType: 'gauge' | 'counter' | 'histogram';
  labels?: Record<string, any>;
  recordedAt: Date;
}

// 数据迁移相关接口
export interface MigrationResult {
  success: boolean;
  startTime: Date;
  endTime: Date;
  duration: number;
  backupPath?: string;
  memoryStats?: MemoryMigrationResult;
  historicalData?: HistoricalMigrationResult;
  validation?: ValidationResult;
  error?: string;
  summary: {
    totalServers: number;
    totalRecords: number;
    memoryRecords: number;
    historicalRecords: number;
  };
}

export interface MemoryMigrationResult {
  serversProcessed: number;
  recordsCreated: number;
  details: Array<{
    serverName: string;
    originalStats: ServerCallStats;
    recordsGenerated: number;
  }>;
}

export interface HistoricalMigrationResult {
  recordsCreated: number;
  timeRange: {
    start: Date;
    end: Date;
  };
  details: Array<{
    serverName: string;
    recordsGenerated: number;
    timeRange: {
      start: Date;
      end: Date;
    };
  }>;
}

export interface PreMigrationCheckResult {
  canProceed: boolean;
  issues: string[];
  memoryStatsCount: number;
  existingTables: string[];
}

export interface ValidationResult {
  isValid: boolean;
  issues: string[];
  statistics: {
    totalCallLogs: number;
    totalServerStats: number;
    serversValidated: number;
  };
}

export interface RollbackResult {
  success: boolean;
  error?: string;
  restoredData: {
    memoryStats: boolean;
    callLogs: boolean;
    serverStats: boolean;
    metrics: boolean;
  };
}

export interface MigrationStatus {
  status: 'not_started' | 'in_progress' | 'completed' | 'partial';
  memoryData: {
    serversCount: number;
    totalCalls: number;
  };
  databaseData: {
    callLogs: number;
    serverStats: number;
    metrics: number;
  };
  lastChecked: Date;
}

// 历史查询参数接口
export interface HistoryQueryParams {
  timeRange: {
    startTime: string; // ISO 8601 timestamp
    endTime: string; // ISO 8601 timestamp
  };
  filters?: {
    serverNames?: string[];
    toolNames?: string[];
    clientIps?: string[];
    success?: boolean;
  };
  aggregation?: {
    groupBy: 'hour' | 'day' | 'week' | 'month';
    metrics: ('count' | 'success_rate' | 'avg_response_time')[];
  };
  pagination: {
    page: number;
    pageSize: number;
  };
}

// 历史查询响应接口
export interface HistoryQueryResponse {
  data: ToolCallRecord[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  aggregates: {
    totalCalls: number;
    successRate: number;
    avgResponseTime: number;
  };
}

/**
 * 时间序列数据点
 */
export interface TimeSeriesDataPoint {
  timestamp: string; // ISO 8601 timestamp
  metrics: {
    count?: number;
    success_rate?: number;
    avg_response_time?: number;
  };
}

/**
 * 增强的历史查询响应，支持时间序列聚合
 */
export interface EnhancedHistoryQueryResponse extends HistoryQueryResponse {
  timeSeries?: TimeSeriesDataPoint[];
  isAggregated?: boolean;
}

// 趋势分析数据接口
export interface TrendAnalysisMetrics {
  // 基础指标
  totalCalls: number;
  successRate: number;
  averageResponseTime: number;
  errorRate: number;

  // 趋势指标
  growthRate: number;
  volatility: number;
  seasonality?: SeasonalityPattern;

  // 异常指标
  anomalies: AnomalyPoint[];
  outliers: OutlierPoint[];
}

// 季节性模式接口
export interface SeasonalityPattern {
  type: 'daily' | 'weekly' | 'monthly';
  strength: number; // 0-1之间，表示季节性强度
  peaks: number[]; // 峰值时间点
  valleys: number[]; // 低谷时间点
}

// 异常点接口
export interface AnomalyPoint {
  timestamp: Date;
  value: number;
  expectedValue: number;
  deviation: number;
  severity: 'low' | 'medium' | 'high';
  description: string;
  type: 'spike' | 'drop' | 'trend_break' | 'outlier' | 'volatility';
  category?: 'performance' | 'error' | 'usage' | 'system';
  duration?: number; // 异常持续时间（数据点数量）
  relatedMetrics?: string[]; // 相关联的其他指标
  confidence?: number; // 异常检测的置信度 (0-1)
}

// 离群点接口
export interface OutlierPoint {
  timestamp: Date;
  value: number;
  threshold: number;
  type: 'upper' | 'lower';
}

// 趋势分析查询参数接口
export interface TrendAnalysisParams {
  timeRange: '1h' | '24h' | '7d' | '30d' | 'custom';
  startTime?: string; // for custom range
  endTime?: string; // for custom range
  granularity: 'hour' | 'day' | 'week';
  metrics: ('calls' | 'success_rate' | 'response_time')[];
  serverNames?: string[];
}

// 洞察类型和严重程度
export type InsightType =
  | 'trend'
  | 'anomaly'
  | 'seasonality'
  | 'correlation'
  | 'forecast'
  | 'recommendation';
export type InsightSeverity = 'info' | 'success' | 'warning' | 'critical';

// 洞察接口
export interface Insight {
  id: string; // 唯一标识
  type: InsightType; // 洞察类型
  title: string; // 标题
  description: string; // 详细描述
  severity: InsightSeverity; // 严重程度
  metrics: string[]; // 相关指标
  timestamp: string; // 生成时间
  timeRange?: {
    // 相关时间范围
    start: string;
    end: string;
  };
  relatedAnomalies?: string[]; // 相关异常ID
  confidence?: number; // 置信度 (0-1)
  actionable?: boolean; // 是否可操作
  recommendation?: string; // 建议操作
}

// 趋势分析响应接口
export interface TrendAnalysisResponse {
  timeRange: {
    start: string;
    end: string;
    granularity: string;
  };
  series: {
    [metricName: string]: {
      timestamps: string[];
      values: number[];
    };
  };
  analysis: {
    [metricName: string]: {
      trend: 'increasing' | 'decreasing' | 'stable';
      changeRate: number;
      strength?: number; // R²值，表示趋势强度 (0-1)
    };
  };
  anomalies: {
    [metricName: string]: AnomalyPoint[];
  };
  insights: Insight[];
}

// 系统状态接口
export interface SystemStatus {
  uptime: number;
  version: string;
  environment: string;
  timestamp: string;
  nodeVersion: string;
  hostname: string;
}

// 性能指标接口
export interface PerformanceMetrics {
  cpu: {
    usage: number;
    cores: number;
  };
  memory: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  database: {
    connections: number;
    responseTime: number;
    queriesPerMinute: number;
  };
  api: {
    requestsPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
    activeConnections: number;
  };
}

// 健康检查接口
export interface HealthCheck {
  status: 'healthy' | 'warning' | 'critical';
  services: {
    database: {
      status: 'up' | 'down';
      responseTime: number;
    };
    mcpServers: {
      status: 'up' | 'down';
      count: number;
      active: number;
    };
    cache: {
      status: 'up' | 'down';
      size: number;
      hitRate: number;
    };
  };
  lastCheck: string;
}

// 监控API响应接口
export interface MonitoringResponse {
  success: boolean;
  data: {
    systemStatus?: SystemStatus;
    performanceMetrics?: PerformanceMetrics;
    healthCheck?: HealthCheck;
  };
  error?: string;
}

// ==================== 数据验证工具函数 ====================

/**
 * 验证ToolCallRecord数据的完整性和有效性
 */
export function validateToolCallRecord(data: any): data is ToolCallRecord {
  if (!data || typeof data !== 'object') {
    return false;
  }

  // 必需字段验证
  if (typeof data.serverName !== 'string' || data.serverName.trim() === '') {
    return false;
  }

  if (typeof data.toolName !== 'string' || data.toolName.trim() === '') {
    return false;
  }

  if (!(data.callTime instanceof Date) && typeof data.callTime !== 'string') {
    return false;
  }

  if (typeof data.success !== 'boolean') {
    return false;
  }

  // 可选字段验证
  if (
    data.responseTime !== undefined &&
    (typeof data.responseTime !== 'number' || data.responseTime < 0)
  ) {
    return false;
  }

  if (data.userId !== undefined && typeof data.userId !== 'string') {
    return false;
  }

  if (data.errorMessage !== undefined && typeof data.errorMessage !== 'string') {
    return false;
  }

  if (
    data.requestParams !== undefined &&
    (typeof data.requestParams !== 'object' || data.requestParams === null)
  ) {
    return false;
  }

  if (
    data.responseData !== undefined &&
    (typeof data.responseData !== 'object' || data.responseData === null)
  ) {
    return false;
  }

  return true;
}

/**
 * 验证MonitoringMetric数据的完整性和有效性
 */
export function validateMonitoringMetric(data: any): data is MonitoringMetric {
  if (!data || typeof data !== 'object') {
    return false;
  }

  if (typeof data.metricName !== 'string' || data.metricName.trim() === '') {
    return false;
  }

  if (typeof data.metricValue !== 'number') {
    return false;
  }

  if (typeof data.metricType !== 'string' || data.metricType.trim() === '') {
    return false;
  }

  if (!(data.recordedAt instanceof Date) && typeof data.recordedAt !== 'string') {
    return false;
  }

  if (data.labels !== undefined && (typeof data.labels !== 'object' || data.labels === null)) {
    return false;
  }

  return true;
}

/**
 * 验证结果接口
 */
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface DetailedValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * 验证时间范围的合理性
 */
export function validateTimeRange(startTime: string | Date, endTime: string | Date): DetailedValidationResult {
  const errors: ValidationError[] = [];

  let start: Date | null = null;
  let end: Date | null = null;

  // 验证开始时间格式
  try {
    start = typeof startTime === 'string' ? new Date(startTime) : startTime;
    if (isNaN(start.getTime())) {
      errors.push({
        field: 'startTime',
        message: '开始时间格式无效，请使用ISO 8601格式',
        value: startTime
      });
      start = null;
    }
  } catch (error) {
    errors.push({
      field: 'startTime',
      message: '开始时间格式无效',
      value: startTime
    });
    start = null;
  }

  // 验证结束时间格式
  try {
    end = typeof endTime === 'string' ? new Date(endTime) : endTime;
    if (isNaN(end.getTime())) {
      errors.push({
        field: 'endTime',
        message: '结束时间格式无效，请使用ISO 8601格式',
        value: endTime
      });
      end = null;
    }
  } catch (error) {
    errors.push({
      field: 'endTime',
      message: '结束时间格式无效',
      value: endTime
    });
    end = null;
  }

  // 如果时间格式都正确，验证时间范围合理性
  if (start && end) {
    if (start >= end) {
      errors.push({
        field: 'timeRange',
        message: '开始时间不能晚于或等于结束时间',
        value: { startTime, endTime }
      });
    }

    // 验证时间范围不能超过1年
    const oneYear = 365 * 24 * 60 * 60 * 1000;
    if (end.getTime() - start.getTime() > oneYear) {
      errors.push({
        field: 'timeRange',
        message: '时间范围不能超过1年',
        value: { startTime, endTime }
      });
    }

    // 验证时间不能是未来时间
    const now = new Date();
    if (start > now) {
      errors.push({
        field: 'startTime',
        message: '开始时间不能是未来时间',
        value: startTime
      });
    }

    if (end > now) {
      errors.push({
        field: 'endTime',
        message: '结束时间不能是未来时间',
        value: endTime
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证分页参数
 */
export function validatePaginationParams(page: any, pageSize: any): DetailedValidationResult {
  const errors: ValidationError[] = [];

  // 验证页码
  const pageNum = Number(page);
  if (isNaN(pageNum) || pageNum < 1 || !Number.isInteger(pageNum)) {
    errors.push({
      field: 'page',
      message: '页码必须是大于0的整数',
      value: page
    });
  } else if (pageNum > 10000) {
    errors.push({
      field: 'page',
      message: '页码不能超过10000',
      value: page
    });
  }

  // 验证页大小
  const pageSizeNum = Number(pageSize);
  if (isNaN(pageSizeNum) || pageSizeNum < 1 || !Number.isInteger(pageSizeNum)) {
    errors.push({
      field: 'pageSize',
      message: '页大小必须是大于0的整数',
      value: pageSize
    });
  } else if (pageSizeNum > 100000) {
    errors.push({
      field: 'pageSize',
      message: '页大小不能超过100000',
      value: pageSize
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证服务器名称格式
 */
export function validateServerNames(serverNames: string[]): DetailedValidationResult {
  const errors: ValidationError[] = [];

  if (!Array.isArray(serverNames)) {
    errors.push({
      field: 'serverNames',
      message: '服务器名称必须是数组格式',
      value: serverNames
    });
    return { isValid: false, errors };
  }

  serverNames.forEach((name, index) => {
    if (typeof name !== 'string') {
      errors.push({
        field: `serverNames[${index}]`,
        message: '服务器名称必须是字符串',
        value: name
      });
    } else if (name.trim() === '') {
      errors.push({
        field: `serverNames[${index}]`,
        message: '服务器名称不能为空',
        value: name
      });
    } else if (name.length > 100) {
      errors.push({
        field: `serverNames[${index}]`,
        message: '服务器名称长度不能超过100个字符',
        value: name
      });
    } else if (!/^[a-zA-Z0-9_\-\.]+$/.test(name)) {
      errors.push({
        field: `serverNames[${index}]`,
        message: '服务器名称只能包含字母、数字、下划线、连字符和点',
        value: name
      });
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证工具名称格式
 */
export function validateToolNames(toolNames: string[]): DetailedValidationResult {
  const errors: ValidationError[] = [];

  if (!Array.isArray(toolNames)) {
    errors.push({
      field: 'toolNames',
      message: '工具名称必须是数组格式',
      value: toolNames
    });
    return { isValid: false, errors };
  }

  toolNames.forEach((name, index) => {
    if (typeof name !== 'string') {
      errors.push({
        field: `toolNames[${index}]`,
        message: '工具名称必须是字符串',
        value: name
      });
    } else if (name.trim() === '') {
      errors.push({
        field: `toolNames[${index}]`,
        message: '工具名称不能为空',
        value: name
      });
    } else if (name.length > 100) {
      errors.push({
        field: `toolNames[${index}]`,
        message: '工具名称长度不能超过100个字符',
        value: name
      });
    } else if (!/^[a-zA-Z0-9_\-\.]+$/.test(name)) {
      errors.push({
        field: `toolNames[${index}]`,
        message: '工具名称只能包含字母、数字、下划线、连字符和点',
        value: name
      });
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证HistoryQueryParams数据的完整性和有效性（增强版）
 */
export function validateHistoryQueryParams(data: any): data is HistoryQueryParams {
  if (!data || typeof data !== 'object') {
    return false;
  }

  // 验证timeRange
  if (!data.timeRange || typeof data.timeRange !== 'object') {
    return false;
  }

  if (!(data.timeRange.startTime instanceof Date) && typeof data.timeRange.startTime !== 'string') {
    return false;
  }

  if (!(data.timeRange.endTime instanceof Date) && typeof data.timeRange.endTime !== 'string') {
    return false;
  }

  // 验证pagination
  if (!data.pagination || typeof data.pagination !== 'object') {
    return false;
  }

  if (typeof data.pagination.page !== 'number' || data.pagination.page < 1) {
    return false;
  }

  // 放宽pageSize限制，支持数据导出的大批量查询
  if (
    typeof data.pagination.pageSize !== 'number' ||
    data.pagination.pageSize < 1 ||
    data.pagination.pageSize > 100000  // 增加到100000以支持大批量导出
  ) {
    return false;
  }

  // 验证可选的filters
  if (data.filters !== undefined) {
    if (typeof data.filters !== 'object' || data.filters === null) {
      return false;
    }

    if (data.filters.serverNames !== undefined && !Array.isArray(data.filters.serverNames)) {
      return false;
    }

    if (data.filters.toolNames !== undefined && !Array.isArray(data.filters.toolNames)) {
      return false;
    }

    if (data.filters.userIds !== undefined && !Array.isArray(data.filters.userIds)) {
      return false;
    }

    if (data.filters.success !== undefined && typeof data.filters.success !== 'boolean') {
      return false;
    }
  }

  return true;
}

/**
 * 详细验证HistoryQueryParams（新增）
 */
export function validateHistoryQueryParamsDetailed(data: any): DetailedValidationResult {
  const errors: ValidationError[] = [];

  if (!data || typeof data !== 'object') {
    errors.push({
      field: 'root',
      message: '查询参数必须是对象格式',
      value: data
    });
    return { isValid: false, errors };
  }

  // 验证timeRange
  if (!data.timeRange || typeof data.timeRange !== 'object') {
    errors.push({
      field: 'timeRange',
      message: '时间范围参数是必需的',
      value: data.timeRange
    });
  } else {
    const timeValidation = validateTimeRange(data.timeRange.startTime, data.timeRange.endTime);
    errors.push(...timeValidation.errors);
  }

  // 验证pagination
  if (!data.pagination || typeof data.pagination !== 'object') {
    errors.push({
      field: 'pagination',
      message: '分页参数是必需的',
      value: data.pagination
    });
  } else {
    const paginationValidation = validatePaginationParams(data.pagination.page, data.pagination.pageSize);
    errors.push(...paginationValidation.errors);
  }

  // 验证可选的filters
  if (data.filters !== undefined) {
    if (typeof data.filters !== 'object' || data.filters === null) {
      errors.push({
        field: 'filters',
        message: '筛选条件必须是对象格式',
        value: data.filters
      });
    } else {
      // 验证serverNames
      if (data.filters.serverNames !== undefined) {
        const serverNamesValidation = validateServerNames(data.filters.serverNames);
        errors.push(...serverNamesValidation.errors);
      }

      // 验证toolNames
      if (data.filters.toolNames !== undefined) {
        const toolNamesValidation = validateToolNames(data.filters.toolNames);
        errors.push(...toolNamesValidation.errors);
      }

      // 验证userIds
      if (data.filters.userIds !== undefined) {
        if (!Array.isArray(data.filters.userIds)) {
          errors.push({
            field: 'filters.userIds',
            message: '用户ID列表必须是数组格式',
            value: data.filters.userIds
          });
        } else {
          data.filters.userIds.forEach((userId: any, index: number) => {
            if (typeof userId !== 'string' || userId.trim() === '') {
              errors.push({
                field: `filters.userIds[${index}]`,
                message: '用户ID必须是非空字符串',
                value: userId
              });
            }
          });
        }
      }

      // 验证success
      if (data.filters.success !== undefined && typeof data.filters.success !== 'boolean') {
        errors.push({
          field: 'filters.success',
          message: '成功状态必须是布尔值',
          value: data.filters.success
        });
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 数据转换工具：将字符串日期转换为Date对象
 */
export function normalizeToolCallRecord(data: any): ToolCallRecord {
  const normalized = { ...data };

  // 转换日期字段
  if (typeof normalized.callTime === 'string') {
    normalized.callTime = new Date(normalized.callTime);
  }

  if (typeof normalized.createdAt === 'string') {
    normalized.createdAt = new Date(normalized.createdAt);
  }

  // 确保数值字段的类型正确
  if (normalized.responseTime !== undefined) {
    normalized.responseTime = Number(normalized.responseTime);
  }

  return normalized;
}

/**
 * 数据转换工具：将字符串日期转换为Date对象
 */
export function normalizeMonitoringMetric(data: any): MonitoringMetric {
  const normalized = { ...data };

  // 转换日期字段
  if (typeof normalized.recordedAt === 'string') {
    normalized.recordedAt = new Date(normalized.recordedAt);
  }

  // 确保数值字段的类型正确
  normalized.metricValue = Number(normalized.metricValue);

  return normalized;
}

/**
 * 数据转换工具：标准化历史查询参数（增强版）
 */
export function normalizeHistoryQueryParams(data: any): HistoryQueryParams {
  const normalized = { ...data };

  // 转换时间范围
  if (typeof normalized.timeRange.startTime === 'string') {
    normalized.timeRange.startTime = new Date(normalized.timeRange.startTime);
  }

  if (typeof normalized.timeRange.endTime === 'string') {
    normalized.timeRange.endTime = new Date(normalized.timeRange.endTime);
  }

  // 确保分页参数为数字
  normalized.pagination.page = Number(normalized.pagination.page);
  normalized.pagination.pageSize = Number(normalized.pagination.pageSize);

  return normalized;
}

/**
 * 清理和格式化查询参数
 */
export function sanitizeQueryParams(params: any): any {
  const sanitized = { ...params };

  // 清理时间范围
  if (sanitized.timeRange) {
    if (sanitized.timeRange.startTime) {
      sanitized.timeRange.startTime = sanitized.timeRange.startTime.toString().trim();
    }
    if (sanitized.timeRange.endTime) {
      sanitized.timeRange.endTime = sanitized.timeRange.endTime.toString().trim();
    }
  }

  // 清理分页参数
  if (sanitized.pagination) {
    sanitized.pagination.page = Math.max(1, Math.floor(Number(sanitized.pagination.page) || 1));
    sanitized.pagination.pageSize = Math.max(1, Math.min(100000, Math.floor(Number(sanitized.pagination.pageSize) || 20)));
  }

  // 清理筛选条件
  if (sanitized.filters) {
    // 清理服务器名称
    if (sanitized.filters.serverNames && Array.isArray(sanitized.filters.serverNames)) {
      sanitized.filters.serverNames = sanitized.filters.serverNames
        .map((name: any) => typeof name === 'string' ? name.trim() : '')
        .filter((name: string) => name.length > 0)
        .slice(0, 50); // 限制最多50个服务器
    }

    // 清理工具名称
    if (sanitized.filters.toolNames && Array.isArray(sanitized.filters.toolNames)) {
      sanitized.filters.toolNames = sanitized.filters.toolNames
        .map((name: any) => typeof name === 'string' ? name.trim() : '')
        .filter((name: string) => name.length > 0)
        .slice(0, 50); // 限制最多50个工具
    }

    // 清理用户ID
    if (sanitized.filters.userIds && Array.isArray(sanitized.filters.userIds)) {
      sanitized.filters.userIds = sanitized.filters.userIds
        .map((id: any) => typeof id === 'string' ? id.trim() : '')
        .filter((id: string) => id.length > 0)
        .slice(0, 100); // 限制最多100个用户
    }

    // 清理成功状态
    if (sanitized.filters.success !== undefined) {
      if (typeof sanitized.filters.success === 'string') {
        sanitized.filters.success = sanitized.filters.success.toLowerCase() === 'true';
      } else {
        sanitized.filters.success = Boolean(sanitized.filters.success);
      }
    }
  }

  return sanitized;
}

/**
 * 验证TrendAnalysisParams参数
 */
export function validateTrendAnalysisParams(data: any): DetailedValidationResult {
  const errors: ValidationError[] = [];

  if (!data || typeof data !== 'object') {
    errors.push({
      field: 'root',
      message: '趋势分析参数必须是对象格式',
      value: data
    });
    return { isValid: false, errors };
  }

  // 验证timeRange
  if (data.timeRange !== undefined) {
    const validTimeRanges = ['1h', '24h', '7d', '30d', 'custom'];
    if (!validTimeRanges.includes(data.timeRange)) {
      errors.push({
        field: 'timeRange',
        message: `时间范围必须是以下值之一: ${validTimeRanges.join(', ')}`,
        value: data.timeRange
      });
    }

    // 如果是自定义时间范围，验证startTime和endTime
    if (data.timeRange === 'custom') {
      if (!data.startTime || !data.endTime) {
        errors.push({
          field: 'customTimeRange',
          message: '自定义时间范围需要提供startTime和endTime参数',
          value: { startTime: data.startTime, endTime: data.endTime }
        });
      } else {
        const timeValidation = validateTimeRange(data.startTime, data.endTime);
        errors.push(...timeValidation.errors);
      }
    }
  }

  // 验证granularity
  if (data.granularity !== undefined) {
    const validGranularities = ['hour', 'day', 'week'];
    if (!validGranularities.includes(data.granularity)) {
      errors.push({
        field: 'granularity',
        message: `粒度必须是以下值之一: ${validGranularities.join(', ')}`,
        value: data.granularity
      });
    }
  }

  // 验证metrics
  if (data.metrics !== undefined) {
    if (!Array.isArray(data.metrics)) {
      errors.push({
        field: 'metrics',
        message: '指标必须是数组格式',
        value: data.metrics
      });
    } else {
      const validMetrics = ['calls', 'success_rate', 'response_time'];
      data.metrics.forEach((metric: any, index: number) => {
        if (!validMetrics.includes(metric)) {
          errors.push({
            field: `metrics[${index}]`,
            message: `指标必须是以下值之一: ${validMetrics.join(', ')}`,
            value: metric
          });
        }
      });
    }
  }

  // 验证serverNames
  if (data.serverNames !== undefined) {
    const serverNamesValidation = validateServerNames(data.serverNames);
    errors.push(...serverNamesValidation.errors);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 数据清理工具：移除敏感信息
 */
export function sanitizeToolCallRecord(record: ToolCallRecord): ToolCallRecord {
  const sanitized = { ...record };

  // 移除可能包含敏感信息的大型数据
  if (sanitized.requestParams && typeof sanitized.requestParams === 'object') {
    const params = { ...sanitized.requestParams };

    // 移除可能的密码、token等敏感字段
    const sensitiveKeys = ['password', 'token', 'key', 'secret', 'auth', 'credential'];
    for (const key of Object.keys(params)) {
      if (sensitiveKeys.some((sensitive) => key.toLowerCase().includes(sensitive))) {
        params[key] = '[REDACTED]';
      }
    }

    sanitized.requestParams = params;
  }

  if (sanitized.responseData && typeof sanitized.responseData === 'object') {
    const response = { ...sanitized.responseData };

    // 限制响应数据大小，避免存储过大的数据
    const responseStr = JSON.stringify(response);
    if (responseStr.length > 10000) {
      // 10KB限制
      sanitized.responseData = {
        ...response,
        _truncated: true,
        _originalSize: responseStr.length,
      };
    }
  }

  return sanitized;
}

// 监控指标查询参数接口
export interface MonitoringQueryParams {
  timeRange: string;
  metricNames?: string[];
}

// 监控指标响应接口
export interface MonitoringQueryResponse {
  metrics: {
    [metricName: string]: {
      current: number;
      previous: number;
      change: number;
      trend: 'up' | 'down' | 'stable';
    };
  };
  systemHealth: {
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
  };
}

// 扩展的服务器统计接口（保持向后兼容）
export interface EnhancedServerStats extends ServerCallStats {
  historical?: {
    last24h: AggregateStats;
    last7d: AggregateStats;
    last30d: AggregateStats;
  };
  trends?: {
    callsGrowthRate: number;
    successRateTrend: 'improving' | 'declining' | 'stable';
    responseTimeTrend: 'improving' | 'declining' | 'stable';
  };
}

// Details about a tool available on the server
export interface ToolInfo {
  name: string; // Name of the tool
  description: string; // Brief description of the tool
  inputSchema: Record<string, unknown>; // Input schema for the tool
  enabled?: boolean; // Whether the tool is enabled (optional, defaults to true)
}

// Standardized API response structure
export interface ApiResponse<T = unknown> {
  success: boolean; // Indicates if the operation was successful
  message?: string; // Optional message providing additional details
  data?: T; // Optional data payload
  metadata?: Record<string, any>; // Optional metadata for additional information
}

// Request payload for adding a new server
export interface AddServerRequest {
  name: string; // Name of the server to add
  config: ServerConfig; // Configuration details for the server
}
