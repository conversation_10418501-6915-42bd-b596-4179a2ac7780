import { OverviewData } from '../controllers/overviewController.js';

/**
 * 生成模拟的概览数据
 */
export function generateMockOverviewData(): OverviewData {
  const now = new Date();
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  return {
    // TOP排名数据
    topServers: [
      {
        rank: 1,
        serverName: 'github-mcp',
        callCount: 1247,
        percentage: 28.5,
        trend: 'up',
        trendValue: 12.3
      },
      {
        rank: 2,
        serverName: 'amap-maps',
        callCount: 892,
        percentage: 20.4,
        trend: 'up',
        trendValue: 8.7
      },
      {
        rank: 3,
        serverName: 'slack-mcp',
        callCount: 634,
        percentage: 14.5,
        trend: 'stable',
        trendValue: 0.2
      },
      {
        rank: 4,
        serverName: 'fetch-mcp',
        callCount: 521,
        percentage: 11.9,
        trend: 'down',
        trendValue: -5.1
      },
      {
        rank: 5,
        serverName: 'tavily-search',
        callCount: 389,
        percentage: 8.9,
        trend: 'up',
        trendValue: 15.2
      }
    ],

    // 工具生态健康度
    toolEcosystem: {
      healthScore: 87.5,
      totalTools: 156,
      healthyTools: 142,
      problemTools: 8,
      offlineTools: 6
    },

    // 工具热度分析
    toolHeatmap: {
      hotTools: [
        { name: 'github_search_repositories', callCount: 342, percentage: 15.2 },
        { name: 'amap_geocoding', callCount: 298, percentage: 13.3 },
        { name: 'slack_send_message', callCount: 267, percentage: 11.9 },
        { name: 'fetch_url', callCount: 234, percentage: 10.4 },
        { name: 'tavily_search', callCount: 189, percentage: 8.4 }
      ],
      coldTools: [
        { name: 'legacy_tool_1', callCount: 3, percentage: 0.1 },
        { name: 'debug_helper', callCount: 5, percentage: 0.2 },
        { name: 'test_utility', callCount: 8, percentage: 0.4 }
      ],
      heatMatrix: [
        { toolName: 'github_search_repositories', heatLevel: 'hot', callFrequency: 342 },
        { toolName: 'amap_geocoding', heatLevel: 'hot', callFrequency: 298 },
        { toolName: 'slack_send_message', heatLevel: 'hot', callFrequency: 267 },
        { toolName: 'fetch_url', heatLevel: 'warm', callFrequency: 234 },
        { toolName: 'tavily_search', heatLevel: 'warm', callFrequency: 189 },
        { toolName: 'legacy_tool_1', heatLevel: 'cold', callFrequency: 3 }
      ]
    },

    // 业务价值指数
    businessValue: {
      overallScore: 82.3,
      highValueTools: 45,
      mediumValueTools: 78,
      lowValueTools: 33,
      valueDistribution: [
        { category: '数据获取', score: 89.2, count: 42 },
        { category: '通信协作', score: 85.7, count: 28 },
        { category: '开发工具', score: 78.4, count: 35 },
        { category: '分析处理', score: 76.1, count: 24 },
        { category: '其他工具', score: 65.3, count: 27 }
      ]
    },

    // 工具分类分布
    toolCategories: [
      { category: '数据获取', count: 42, percentage: 26.9, color: '#3B82F6' },
      { category: '开发工具', count: 35, percentage: 22.4, color: '#10B981' },
      { category: '通信协作', count: 28, percentage: 17.9, color: '#F59E0B' },
      { category: '其他工具', count: 27, percentage: 17.3, color: '#8B5CF6' },
      { category: '分析处理', count: 24, percentage: 15.4, color: '#EF4444' }
    ],

    // 调用效率分析
    callEfficiency: {
      averageResponseTime: 287,
      highEfficiencyTools: 89,
      mediumEfficiencyTools: 52,
      lowEfficiencyTools: 15,
      efficiencyDistribution: [
        { range: '< 200ms', count: 89, percentage: 57.1 },
        { range: '200-500ms', count: 52, percentage: 33.3 },
        { range: '> 500ms', count: 15, percentage: 9.6 }
      ],
      needOptimization: [
        { toolName: 'heavy_computation_tool', avgResponseTime: 1250, callCount: 45 },
        { toolName: 'external_api_slow', avgResponseTime: 890, callCount: 78 },
        { toolName: 'database_query_complex', avgResponseTime: 650, callCount: 123 }
      ]
    },

    // 智能洞察
    insights: {
      trends: [],
      warnings: [],
      recommendations: [],
      lastUpdated: now.toISOString()
    },

    // 元数据
    metadata: {
      lastUpdated: now.toISOString(),
      dataRange: {
        startTime: yesterday.toISOString(),
        endTime: now.toISOString()
      },
      totalCalls: 4372,
      totalServers: 12
    }
  };
}

/**
 * 生成随机趋势值
 */
function generateRandomTrend(): { trend: 'up' | 'down' | 'stable'; value: number } {
  const random = Math.random();
  if (random < 0.4) {
    return { trend: 'up', value: Math.random() * 20 };
  } else if (random < 0.8) {
    return { trend: 'down', value: -(Math.random() * 15) };
  } else {
    return { trend: 'stable', value: Math.random() * 2 - 1 };
  }
}

/**
 * 生成随机颜色
 */
function generateRandomColor(): string {
  const colors = [
    '#3B82F6', '#10B981', '#F59E0B', '#8B5CF6', '#EF4444',
    '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
  ];
  return colors[Math.floor(Math.random() * colors.length)];
}
