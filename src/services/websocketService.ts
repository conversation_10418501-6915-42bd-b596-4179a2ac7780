// WebSocket service for real-time data communication
import { WebSocketServer, WebSocket } from 'ws';
import { IncomingMessage } from 'http';
import { randomUUID } from 'crypto';
import jwt from 'jsonwebtoken';
import config from '../config/index.js';
import {
  WSClientInfo,
  ClientMessage,
  ServerMessage,
  WSDataMessage,
  WSChannel,
  WSServiceConfig,
  SystemHealthData,
  PerformanceMetricsData,
  RealtimeData,
} from '../types/websocket.js';

export class WebSocketService {
  private wss: WebSocketServer | null = null;
  private clients: Map<string, { ws: WebSocket; info: WSClientInfo }> = new Map();
  private pingInterval: NodeJS.Timeout | null = null;
  private dataUpdateInterval: NodeJS.Timeout | null = null;
  private config: WSServiceConfig;

  constructor(config: WSServiceConfig = {}) {
    this.config = {
      pingInterval: 30000, // 30 seconds
      pongTimeout: 5000, // 5 seconds
      maxClients: 100,
      dataUpdateInterval: 5000, // 5 seconds
      ...config,
    };
  }

  // Initialize WebSocket server
  initialize(server: any): void {
    this.wss = new WebSocketServer({
      server,
      path: '/ws',
      verifyClient: this.verifyClient.bind(this),
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.startPingInterval();
    this.startDataUpdateInterval();

    console.log('WebSocket server initialized on /ws');
  }

  // Verify client connection
  private verifyClient(info: { origin: string; secure: boolean; req: IncomingMessage }): boolean {
    // Basic verification - can be enhanced with more security checks
    if (this.clients.size >= this.config.maxClients!) {
      console.log('WebSocket connection rejected: max clients reached');
      return false;
    }
    return true;
  }

  // Handle new WebSocket connection
  private handleConnection(ws: WebSocket, req: IncomingMessage): void {
    const clientId = randomUUID();
    const clientInfo: WSClientInfo = {
      id: clientId,
      authenticated: false,
      subscriptions: new Set(),
      connectedAt: new Date(),
    };

    this.clients.set(clientId, { ws, info: clientInfo });

    console.log(`WebSocket client connected: ${clientId}`);

    // Set up message handler
    ws.on('message', (data: Buffer) => {
      try {
        const message: ClientMessage = JSON.parse(data.toString());
        this.handleMessage(clientId, message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
        this.sendError(clientId, 'Invalid message format');
      }
    });

    // Handle client disconnect
    ws.on('close', () => {
      console.log(`WebSocket client disconnected: ${clientId}`);
      this.clients.delete(clientId);
      this.broadcastConnectionStatus();
    });

    // Handle errors
    ws.on('error', (error) => {
      console.error(`WebSocket error for client ${clientId}:`, error);
      this.clients.delete(clientId);
    });

    // Send connection status
    this.sendConnectionStatus(clientId);
    this.broadcastConnectionStatus();
  }

  // Handle incoming messages from clients
  private handleMessage(clientId: string, message: ClientMessage): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    switch (message.type) {
      case 'auth':
        this.handleAuth(clientId, message.data.token);
        break;
      case 'subscribe':
        this.handleSubscribe(clientId, message.data.channels);
        break;
      case 'unsubscribe':
        this.handleUnsubscribe(clientId, message.data.channels);
        break;
      case 'ping':
        this.handlePing(clientId);
        break;
      default:
        this.sendError(clientId, `Unknown message type: ${(message as any).type}`);
    }
  }

  // Handle authentication
  private handleAuth(clientId: string, token: string): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    try {
      // Verify JWT token
      const decoded = jwt.verify(token, config.jwtSecret) as any;
      client.info.authenticated = true;
      client.info.userId = decoded.user?.username;

      this.sendMessage(clientId, {
        type: 'auth_response',
        data: { success: true },
        timestamp: new Date().toISOString(),
      });

      console.log(`WebSocket client authenticated: ${clientId} (user: ${decoded.user?.username})`);
    } catch (error) {
      this.sendMessage(clientId, {
        type: 'auth_response',
        data: { success: false, message: 'Invalid token' },
        timestamp: new Date().toISOString(),
      });
    }
  }

  // Handle channel subscription
  private handleSubscribe(clientId: string, channels: string[]): void {
    const client = this.clients.get(clientId);
    if (!client || !client.info.authenticated) {
      this.sendError(clientId, 'Authentication required');
      return;
    }

    for (const channel of channels) {
      if (Object.values(WSChannel).includes(channel as WSChannel)) {
        client.info.subscriptions.add(channel);
        console.log(`Client ${clientId} subscribed to ${channel}`);
      } else {
        this.sendError(clientId, `Invalid channel: ${channel}`);
      }
    }
  }

  // Handle channel unsubscription
  private handleUnsubscribe(clientId: string, channels: string[]): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    for (const channel of channels) {
      client.info.subscriptions.delete(channel);
      console.log(`Client ${clientId} unsubscribed from ${channel}`);
    }
  }

  // Handle ping
  private handlePing(clientId: string): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    client.info.lastPing = new Date();
    this.sendMessage(clientId, {
      type: 'pong',
      timestamp: new Date().toISOString(),
    });
  }

  // Send message to specific client
  private sendMessage(clientId: string, message: ServerMessage): void {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== WebSocket.OPEN) return;

    try {
      client.ws.send(JSON.stringify(message));
    } catch (error) {
      console.error(`Error sending message to client ${clientId}:`, error);
      this.clients.delete(clientId);
    }
  }

  // Send error message
  private sendError(clientId: string, message: string): void {
    this.sendMessage(clientId, {
      type: 'error',
      data: { message },
      timestamp: new Date().toISOString(),
    });
  }

  // Send connection status
  private sendConnectionStatus(clientId: string): void {
    this.sendMessage(clientId, {
      type: 'connection_status',
      data: {
        status: 'connected',
        clientCount: this.clients.size,
      },
      timestamp: new Date().toISOString(),
    });
  }

  // Broadcast connection status to all clients
  private broadcastConnectionStatus(): void {
    this.broadcast(WSChannel.SYSTEM_HEALTH, {
      type: 'connection_status',
      data: {
        status: 'connected',
        clientCount: this.clients.size,
      },
      timestamp: new Date().toISOString(),
    });
  }

  // Broadcast data to all subscribed clients
  public broadcast(channel: WSChannel, data: any): void {
    const message: WSDataMessage = {
      type: 'data',
      data: {
        channel,
        payload: data,
      },
      timestamp: new Date().toISOString(),
    };

    for (const [clientId, client] of this.clients) {
      if (client.info.authenticated && client.info.subscriptions.has(channel)) {
        this.sendMessage(clientId, message);
      }
    }
  }

  // Start ping interval to keep connections alive
  private startPingInterval(): void {
    this.pingInterval = setInterval(() => {
      const now = new Date();
      for (const [clientId, client] of this.clients) {
        if (client.ws.readyState === WebSocket.OPEN) {
          // Check if client responded to last ping
          if (
            client.info.lastPing &&
            now.getTime() - client.info.lastPing.getTime() > this.config.pongTimeout!
          ) {
            console.log(`Client ${clientId} ping timeout, disconnecting`);
            client.ws.terminate();
            this.clients.delete(clientId);
          }
        } else {
          this.clients.delete(clientId);
        }
      }
    }, this.config.pingInterval);
  }

  // Start data update interval
  private startDataUpdateInterval(): void {
    this.dataUpdateInterval = setInterval(async () => {
      await this.pushRealtimeData();
    }, this.config.dataUpdateInterval);
  }

  // Push real-time data to subscribed clients
  private async pushRealtimeData(): Promise<void> {
    try {
      // Import monitoring functions dynamically to avoid circular dependencies
      const { getRealCpuUsage, getDatabaseResponseTime } = await import(
        '../controllers/monitoringController.js'
      );
      const { getServersInfo } = await import('../services/mcpService.js');
      const { isDatabaseConnected } = await import('../db/connection.js');
      const os = await import('os');

      // Get real system data
      const cpuUsage = getRealCpuUsage();
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const memoryUsage = ((totalMemory - freeMemory) / totalMemory) * 100;

      // Get server status
      const serversInfo = getServersInfo();
      const activeServers = serversInfo.filter((s) => s.status === 'connected').length;
      const totalCalls = serversInfo.reduce((sum, s) => sum + (s.callStats?.totalCalls || 0), 0);

      // Get database response time
      const dbResponseTime = await getDatabaseResponseTime();

      const realtimeData: RealtimeData = {
        timestamp: new Date().toISOString(),
        system: {
          cpu: Math.round(cpuUsage * 100) / 100,
          memory: Math.round(memoryUsage * 100) / 100,
          uptime: process.uptime(),
        },
        servers: {
          active: activeServers,
          total: serversInfo.length,
        },
        performance: {
          totalCalls,
          requestsPerMinute: 0, // 需要实现请求计数器
          avgResponseTime: Math.max(0, dbResponseTime),
        },
        database: {
          connected: isDatabaseConnected(),
          responseTime: Math.max(0, dbResponseTime),
        },
      };

      this.broadcast(WSChannel.REALTIME_DATA, realtimeData);
    } catch (error) {
      console.error('Error pushing real-time data:', error);
    }
  }

  // Get connected clients count
  public getClientCount(): number {
    return this.clients.size;
  }

  // Get authenticated clients count
  public getAuthenticatedClientCount(): number {
    return Array.from(this.clients.values()).filter((client) => client.info.authenticated).length;
  }

  // Shutdown WebSocket service
  public shutdown(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
    }
    if (this.dataUpdateInterval) {
      clearInterval(this.dataUpdateInterval);
    }
    if (this.wss) {
      this.wss.close();
    }
    this.clients.clear();
    console.log('WebSocket service shutdown');
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
