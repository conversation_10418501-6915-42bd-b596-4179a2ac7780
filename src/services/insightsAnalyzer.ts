import { OverviewData } from '../controllers/overviewController.js';

/**
 * 智能洞察数据类型定义
 */
export interface TrendInsight {
  id: string;
  type: 'usage_surge' | 'new_hotspot' | 'growth_trend' | 'performance_boost' | 'adoption_trend';
  title: string;
  description: string;
  serverName: string;
  // 关键数值
  primaryValue: number;
  primaryUnit: string;
  changePercentage: number;
  comparisonPeriod: string; // "vs yesterday", "vs last week"
  // 业务影响
  businessImpact: 'high' | 'medium' | 'low';
  impactDescription: string;
  // 行动建议
  actionRecommendation: string;
  actionUrgency: 'immediate' | 'soon' | 'monitor';
  // 元数据
  priority: 'high' | 'medium' | 'low';
  severity: 'critical' | 'warning' | 'info' | 'success';
  confidence: number;
  metrics: string[];
  timestamp: string;
  dataSource: string;
  actionable: boolean;
}

export interface WarningInsight {
  id: string;
  type: 'no_calls' | 'slow_response' | 'low_success_rate' | 'connection_issue' | 'resource_exhaustion' | 'security_risk';
  title: string;
  description: string;
  serverName: string;
  // 关键数值
  primaryValue: number;
  primaryUnit: string;
  thresholdValue: number;
  exceedPercentage: number;
  // 业务影响
  businessImpact: 'high' | 'medium' | 'low';
  impactDescription: string;
  affectedUsers: number;
  // 行动建议
  actionRecommendation: string;
  actionUrgency: 'immediate' | 'soon' | 'monitor';
  estimatedFixTime: string;
  // 元数据
  severity: 'critical' | 'warning' | 'info';
  actionRequired: boolean;
  confidence: number;
  metrics: string[];
  timestamp: string;
  dataSource: string;
  actionable: boolean;
}

export interface RecommendationInsight {
  id: string;
  category: 'performance' | 'reliability' | 'resource' | 'monitoring' | 'security' | 'cost_optimization';
  title: string;
  description: string;
  // 关键数值
  potentialImprovement: number;
  improvementUnit: string;
  currentValue: number;
  targetValue: number;
  // 业务影响
  businessImpact: 'high' | 'medium' | 'low';
  impactDescription: string;
  expectedROI: string;
  // 实施信息
  implementationSteps: string[];
  estimatedEffort: string;
  estimatedTime: string;
  requiredResources: string[];
  // 元数据
  impact: 'high' | 'medium' | 'low';
  effort: 'low' | 'medium' | 'high';
  actionable: boolean;
  confidence: number;
  metrics: string[];
  timestamp: string;
  dataSource: string;
  severity: 'info' | 'success';
}

export interface InsightsData {
  trends: TrendInsight[];
  warnings: WarningInsight[];
  recommendations: RecommendationInsight[];
  lastUpdated: string;
}

/**
 * 智能洞察分析服务
 */
export class InsightsAnalyzer {
  
  /**
   * 生成完整的智能洞察数据
   */
  public generateInsights(overviewData: OverviewData): InsightsData {
    const timestamp = new Date().toISOString();
    
    const trends = this.detectTrends(overviewData);
    const warnings = this.detectAnomalies(overviewData);
    const recommendations = this.generateRecommendations(overviewData);
    
    return {
      trends,
      warnings,
      recommendations,
      lastUpdated: timestamp
    };
  }

  /**
   * 热点趋势检测
   * 基于增长率和使用量变化检测热点
   */
  private detectTrends(data: OverviewData): TrendInsight[] {
    const trends: TrendInsight[] = [];
    const timestamp = new Date().toISOString();

    // 检测服务器使用量激增
    data.topServers.forEach((server, index) => {
      if (server.trend === 'up' && server.trendValue > 30) {
        const isHighGrowth = server.trendValue > 100;
        trends.push({
          id: `trend_surge_${server.serverName}_${Date.now()}`,
          type: 'usage_surge',
          title: `${server.serverName} 调用量激增`,
          description: `该服务器成为系统热点，需要关注性能和稳定性`,
          serverName: server.serverName,
          primaryValue: server.callCount,
          primaryUnit: '次调用',
          changePercentage: server.trendValue,
          comparisonPeriod: '相比昨天',
          businessImpact: isHighGrowth ? 'high' : 'medium',
          impactDescription: isHighGrowth ? '可能影响系统整体性能' : '需要监控资源使用情况',
          actionRecommendation: isHighGrowth ?
            '立即检查服务器负载，考虑扩容或负载均衡' :
            '持续监控性能指标，准备扩容方案',
          actionUrgency: isHighGrowth ? 'immediate' : 'soon',
          priority: isHighGrowth ? 'high' : 'medium',
          severity: isHighGrowth ? 'warning' : 'success',
          confidence: 0.92,
          metrics: ['call_count', 'growth_rate', 'server_load'],
          timestamp,
          dataSource: '过去24小时调用数据',
          actionable: true
        });
      }
    });

    // 检测新兴热点工具
    if (data.toolHeatmap.hotTools.length > 0) {
      const topHotTool = data.toolHeatmap.hotTools[0];
      if (topHotTool.percentage > 15) {
        const isDominant = topHotTool.percentage > 50;
        trends.push({
          id: `trend_hotspot_${topHotTool.name}_${Date.now()}`,
          type: 'new_hotspot',
          title: `${topHotTool.name} 成为热门工具`,
          description: `该工具使用率显著上升，显示出强劲的用户需求`,
          serverName: 'multiple',
          primaryValue: topHotTool.callCount,
          primaryUnit: '次调用',
          changePercentage: topHotTool.percentage,
          comparisonPeriod: '占总调用量',
          businessImpact: isDominant ? 'high' : 'medium',
          impactDescription: isDominant ?
            '工具使用过于集中，存在单点风险' :
            '用户偏好明显，可重点优化',
          actionRecommendation: isDominant ?
            '评估工具依赖风险，准备备选方案' :
            '优化该工具性能，提升用户体验',
          actionUrgency: isDominant ? 'soon' : 'monitor',
          priority: 'medium',
          severity: isDominant ? 'warning' : 'success',
          confidence: 0.88,
          metrics: ['tool_usage', 'popularity', 'user_preference'],
          timestamp,
          dataSource: '工具调用统计数据',
          actionable: true
        });
      }
    }

    // 检测生态系统健康度趋势
    if (data.toolEcosystem.healthScore > 85) {
      trends.push({
        id: `trend_health_${Date.now()}`,
        type: 'growth_trend',
        title: '生态系统健康度优秀',
        description: `当前生态系统健康度为 ${data.toolEcosystem.healthScore}%，系统运行状态良好`,
        serverName: 'ecosystem',
        primaryValue: data.toolEcosystem.healthScore,
        primaryUnit: '%',
        changePercentage: 0,
        comparisonPeriod: 'vs yesterday',
        businessImpact: 'low',
        impactDescription: '系统运行稳定，用户体验良好',
        actionRecommendation: '继续监控系统状态，保持当前运维策略',
        actionUrgency: 'monitor',
        priority: 'low',
        severity: 'info',
        confidence: 0.95,
        metrics: ['health_score', 'ecosystem_status'],
        timestamp,
        dataSource: '系统监控数据',
        actionable: false
      });
    }

    return trends.slice(0, 3); // 只返回前3个最重要的趋势
  }

  /**
   * 异常状态监控
   * 检测长时间未调用、响应时间异常、成功率下降等
   */
  private detectAnomalies(data: OverviewData): WarningInsight[] {
    const warnings: WarningInsight[] = [];
    const timestamp = new Date().toISOString();

    // 检测响应时间异常
    if (data.callEfficiency.averageResponseTime > 1000) {
      const isCritical = data.callEfficiency.averageResponseTime > 5000;
      const normalThreshold = 500; // 正常响应时间阈值
      const exceedPercentage = Math.round(((data.callEfficiency.averageResponseTime - normalThreshold) / normalThreshold) * 100);

      warnings.push({
        id: `warning_slow_response_${Date.now()}`,
        type: 'slow_response',
        title: '响应时间超出预期',
        description: `系统响应速度明显下降，用户体验受到影响`,
        serverName: 'system',
        primaryValue: data.callEfficiency.averageResponseTime,
        primaryUnit: 'ms',
        thresholdValue: normalThreshold,
        exceedPercentage,
        businessImpact: isCritical ? 'high' : 'medium',
        impactDescription: isCritical ?
          '严重影响用户体验，可能导致用户流失' :
          '用户体验下降，需要及时优化',
        affectedUsers: Math.floor(data.metadata.totalCalls * 0.8), // 估算受影响用户
        actionRecommendation: isCritical ?
          '立即检查服务器负载和数据库性能，必要时重启服务' :
          '分析慢查询日志，优化数据库索引和API性能',
        actionUrgency: isCritical ? 'immediate' : 'soon',
        estimatedFixTime: isCritical ? '1-2小时' : '1-2天',
        severity: isCritical ? 'critical' : 'warning',
        actionRequired: true,
        confidence: 0.94,
        metrics: ['response_time', 'performance', 'user_experience'],
        timestamp,
        dataSource: '系统性能监控数据',
        actionable: true
      });
    }

    // 检测低效率工具
    if (data.callEfficiency.needOptimization.length > 0) {
      const slowestTool = data.callEfficiency.needOptimization[0];
      warnings.push({
        id: `warning_tool_performance_${slowestTool.toolName}_${Date.now()}`,
        type: 'slow_response',
        title: `${slowestTool.toolName} 性能需要优化`,
        description: `${slowestTool.toolName} 平均响应时间为 ${slowestTool.avgResponseTime}ms，建议进行性能优化`,
        serverName: slowestTool.toolName,
        primaryValue: slowestTool.avgResponseTime,
        primaryUnit: 'ms',
        thresholdValue: 1000,
        exceedPercentage: Math.round(((slowestTool.avgResponseTime - 1000) / 1000) * 100),
        businessImpact: slowestTool.avgResponseTime > 3000 ? 'high' : 'medium',
        impactDescription: '工具响应缓慢影响用户体验',
        affectedUsers: 10,
        actionRecommendation: '优化工具性能，检查网络连接和服务器负载',
        actionUrgency: slowestTool.avgResponseTime > 3000 ? 'soon' : 'monitor',
        estimatedFixTime: '1-2天',
        severity: slowestTool.avgResponseTime > 3000 ? 'warning' : 'info',
        actionRequired: true,
        confidence: 0.85,
        metrics: ['tool_performance', 'response_time'],
        timestamp,
        dataSource: '工具性能监控数据',
        actionable: true
      });
    }

    // 检测生态系统健康问题
    if (data.toolEcosystem.problemTools > 0) {
      warnings.push({
        id: `warning_problem_tools_${Date.now()}`,
        type: 'connection_issue',
        title: '发现问题工具',
        description: `检测到 ${data.toolEcosystem.problemTools} 个工具存在问题，${data.toolEcosystem.offlineTools} 个工具离线`,
        serverName: 'ecosystem',
        primaryValue: data.toolEcosystem.problemTools,
        primaryUnit: '个',
        thresholdValue: 0,
        exceedPercentage: 100,
        businessImpact: data.toolEcosystem.problemTools > 5 ? 'high' : 'medium',
        impactDescription: '工具故障影响系统可用性',
        affectedUsers: data.toolEcosystem.problemTools * 5,
        actionRecommendation: '立即检查问题工具状态，重启或修复故障工具',
        actionUrgency: data.toolEcosystem.problemTools > 5 ? 'immediate' : 'soon',
        estimatedFixTime: '30分钟-2小时',
        severity: data.toolEcosystem.problemTools > 5 ? 'critical' : 'warning',
        actionRequired: true,
        confidence: 0.95,
        metrics: ['tool_health', 'availability'],
        timestamp,
        dataSource: '工具健康监控数据',
        actionable: true
      });
    }

    return warnings.slice(0, 2); // 只返回前2个最重要的警告
  }

  /**
   * 优化建议生成
   * 基于规则引擎生成可操作的优化建议
   */
  private generateRecommendations(data: OverviewData): RecommendationInsight[] {
    const recommendations: RecommendationInsight[] = [];
    const timestamp = new Date().toISOString();

    // 性能优化建议
    if (data.callEfficiency.lowEfficiencyTools > data.callEfficiency.highEfficiencyTools) {
      const totalTools = data.callEfficiency.lowEfficiencyTools + data.callEfficiency.mediumEfficiencyTools + data.callEfficiency.highEfficiencyTools;
      const lowEfficiencyRatio = Math.round((data.callEfficiency.lowEfficiencyTools / totalTools) * 100);
      const potentialImprovement = Math.round(data.callEfficiency.averageResponseTime * 0.3); // 预期改善30%

      recommendations.push({
        id: `rec_performance_${Date.now()}`,
        category: 'performance',
        title: '性能优化机会',
        description: `通过优化低效工具，可显著提升系统整体性能`,
        potentialImprovement: potentialImprovement,
        improvementUnit: 'ms响应时间减少',
        currentValue: data.callEfficiency.averageResponseTime,
        targetValue: data.callEfficiency.averageResponseTime - potentialImprovement,
        businessImpact: 'high',
        impactDescription: '提升用户体验，减少系统资源消耗',
        expectedROI: '预计提升30%用户满意度',
        implementationSteps: [
          '识别响应时间超过500ms的工具',
          '分析性能瓶颈（数据库查询、API调用等）',
          '优化算法和数据结构',
          '添加缓存机制',
          '监控优化效果'
        ],
        estimatedEffort: '中等',
        estimatedTime: '1-2周',
        requiredResources: ['后端开发工程师', '性能测试工具'],
        impact: 'high',
        effort: 'medium',
        actionable: true,
        confidence: 0.85,
        metrics: ['efficiency', 'performance', 'response_time'],
        timestamp,
        dataSource: '性能监控数据',
        severity: 'success'
      });
    }

    // 资源分配建议
    if (data.topServers.length > 0) {
      const topServer = data.topServers[0];
      if (topServer.percentage > 50) {
        recommendations.push({
          id: `rec_load_balance_${Date.now()}`,
          category: 'resource',
          title: '考虑负载均衡',
          description: `${topServer.serverName} 承担了 ${topServer.percentage}% 的调用量，建议考虑负载均衡以提高系统稳定性`,
          potentialImprovement: 30,
          improvementUnit: '%',
          currentValue: topServer.percentage,
          targetValue: 40,
          businessImpact: 'medium',
          impactDescription: '提高系统稳定性和可用性',
          expectedROI: '中等',
          implementationSteps: [
            '分析当前负载分布',
            '设计负载均衡策略',
            '部署负载均衡器',
            '测试和优化'
          ],
          estimatedEffort: '高',
          estimatedTime: '2-4周',
          requiredResources: ['系统架构师', '运维工程师', '负载均衡器'],
          impact: 'medium',
          effort: 'high',
          actionable: true,
          confidence: 0.75,
          metrics: ['load_distribution', 'reliability'],
          timestamp,
          dataSource: '负载监控数据',
          severity: 'info'
        });
      }
    }

    // 监控建议
    if (data.toolEcosystem.healthScore < 90) {
      recommendations.push({
        id: `rec_monitoring_${Date.now()}`,
        category: 'monitoring',
        title: '加强系统监控',
        description: `当前健康度为 ${data.toolEcosystem.healthScore}%，建议加强监控以及时发现和解决问题`,
        potentialImprovement: 15,
        improvementUnit: '%',
        currentValue: data.toolEcosystem.healthScore,
        targetValue: 95,
        businessImpact: 'medium',
        impactDescription: '提高系统稳定性和故障响应速度',
        expectedROI: '高',
        implementationSteps: [
          '部署监控告警系统',
          '设置关键指标阈值',
          '建立故障响应流程',
          '定期监控报告'
        ],
        estimatedEffort: '低',
        estimatedTime: '1-2周',
        requiredResources: ['监控工具', '运维工程师'],
        impact: 'medium',
        effort: 'low',
        actionable: true,
        confidence: 0.9,
        metrics: ['health_monitoring', 'system_stability'],
        timestamp,
        dataSource: '系统健康监控数据',
        severity: 'info'
      });
    }

    return recommendations.slice(0, 2); // 只返回前2个最重要的建议
  }
}

// 导出单例实例
export const insightsAnalyzer = new InsightsAnalyzer();
