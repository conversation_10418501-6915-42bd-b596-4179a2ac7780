import fs from 'fs';
import path from 'path';
import { MarketServer } from '../types/index.js';
import { getConfigFilePath } from '../utils/path.js';

// Get path to the servers.json file
export const getServersJsonPath = (): string => {
  return getConfigFilePath('servers.json', 'Servers');
};

// Load all market servers from servers.json
export const getMarketServers = (): Record<string, MarketServer> => {
  try {
    const serversJsonPath = getServersJsonPath();
    const data = fs.readFileSync(serversJsonPath, 'utf8');
    const serversObj = JSON.parse(data) as Record<string, MarketServer>;

    const sortedEntries = Object.entries(serversObj).sort(([, serverA], [, serverB]) => {
      if (serverA.is_official && !serverB.is_official) return -1;
      if (!serverA.is_official && serverB.is_official) return 1;
      return 0;
    });

    return Object.fromEntries(sortedEntries);
  } catch (error) {
    console.error('Failed to load servers from servers.json:', error);
    return {};
  }
};

// Get a specific market server by name
export const getMarketServerByName = (name: string): MarketServer | null => {
  const servers = getMarketServers();
  return servers[name] || null;
};

// Get all categories from market servers
export const getMarketCategories = (): string[] => {
  const servers = getMarketServers();
  const categories = new Set<string>();

  Object.values(servers).forEach((server) => {
    server.categories?.forEach((category) => {
      categories.add(category);
    });
  });

  return Array.from(categories).sort();
};

// Get all tags from market servers
export const getMarketTags = (): string[] => {
  const servers = getMarketServers();
  const tags = new Set<string>();

  Object.values(servers).forEach((server) => {
    server.tags?.forEach((tag) => {
      tags.add(tag);
    });
  });

  return Array.from(tags).sort();
};

// Search market servers by query
export const searchMarketServers = (query: string): MarketServer[] => {
  const servers = getMarketServers();
  const searchTerms = query
    .toLowerCase()
    .split(' ')
    .filter((term) => term.length > 0);

  if (searchTerms.length === 0) {
    return Object.values(servers);
  }

  return Object.values(servers).filter((server) => {
    // Search in name, display_name, description, categories, and tags
    const searchableText = [
      server.name,
      server.display_name,
      server.description,
      ...(server.categories || []),
      ...(server.tags || []),
    ]
      .join(' ')
      .toLowerCase();

    return searchTerms.some((term) => searchableText.includes(term));
  });
};
import { getAppDataSource } from '../db/connection.js';
import {
  McpCallLog,
  McpServerStats,
  McpMonitoringMetrics,
} from '../db/entities/VectorEmbedding.js';
import { Repository, IsNull } from 'typeorm';
import {
  ToolCallRecord,
  AggregateStats,
  MonitoringMetric,
  HistoryQueryParams,
  HistoryQueryResponse,
  TrendAnalysisParams,
  TrendAnalysisResponse,
  AnomalyPoint,
  Insight,
  InsightType,
  InsightSeverity,
  validateToolCallRecord,
  validateMonitoringMetric,
  validateHistoryQueryParams,
  normalizeToolCallRecord,
  normalizeMonitoringMetric,
  normalizeHistoryQueryParams,
  sanitizeToolCallRecord,
  ServerCallStats,
  MigrationResult,
  MemoryMigrationResult,
  HistoricalMigrationResult,
  PreMigrationCheckResult,
  ValidationResult,
  RollbackResult,
  MigrationStatus,
} from '../types/index.js';
import { serverCallStats } from './mcpService.js';

/**
 * 数据持久化服务
 * 负责将MCP调用数据持久化到PostgreSQL数据库
 */
// ==================== 数据持久化错误处理和日志系统 ====================

/**
 * 数据持久化专用错误类
 */
export class DataPersistenceError extends Error {
  public readonly code: string;
  public readonly operation: string;
  public readonly retryable: boolean;
  public readonly originalError?: Error;

  constructor(
    message: string,
    code: string,
    operation: string,
    retryable: boolean = false,
    originalError?: Error,
  ) {
    super(message);
    this.name = 'DataPersistenceError';
    this.code = code;
    this.operation = operation;
    this.retryable = retryable;
    this.originalError = originalError;
  }
}

/**
 * 数据持久化日志记录器
 */
class DataPersistenceLogger {
  private static instance: DataPersistenceLogger;

  public static getInstance(): DataPersistenceLogger {
    if (!DataPersistenceLogger.instance) {
      DataPersistenceLogger.instance = new DataPersistenceLogger();
    }
    return DataPersistenceLogger.instance;
  }

  private formatMessage(level: string, operation: string, message: string, metadata?: any): string {
    const timestamp = new Date().toISOString();
    const metadataStr = metadata ? ` | ${JSON.stringify(metadata)}` : '';
    return `[${timestamp}] [DataPersistence] [${level}] [${operation}] ${message}${metadataStr}`;
  }

  public info(operation: string, message: string, metadata?: any): void {
    console.log(this.formatMessage('INFO', operation, message, metadata));
  }

  public warn(operation: string, message: string, metadata?: any): void {
    console.warn(this.formatMessage('WARN', operation, message, metadata));
  }

  public error(operation: string, message: string, error?: Error, metadata?: any): void {
    const errorInfo = error
      ? {
          name: error.name,
          message: (error as Error).message,
          stack: error.stack,
          ...metadata,
        }
      : metadata;
    console.error(this.formatMessage('ERROR', operation, message, errorInfo));
  }

  public debug(operation: string, message: string, metadata?: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.debug(this.formatMessage('DEBUG', operation, message, metadata));
    }
  }
}

/**
 * 重试机制配置
 */
interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

/**
 * 带重试机制的异步操作执行器
 */
async function executeWithRetry<T>(
  operation: () => Promise<T>,
  operationName: string,
  config: RetryConfig = {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
  },
): Promise<T> {
  const logger = DataPersistenceLogger.getInstance();
  let lastError: Error | undefined;

  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      logger.debug(operationName, `Attempt ${attempt}/${config.maxAttempts}`);
      const result = await operation();

      if (attempt > 1) {
        logger.info(operationName, `Succeeded on attempt ${attempt}/${config.maxAttempts}`);
      }

      return result;
    } catch (error) {
      lastError = error as Error;

      if (attempt === config.maxAttempts) {
        logger.error(operationName, `Failed after ${config.maxAttempts} attempts`, lastError);
        break;
      }

      // 检查是否为可重试的错误
      const isRetryable = isRetryableError(error as Error);
      if (!isRetryable) {
        logger.error(operationName, `Non-retryable error on attempt ${attempt}`, lastError);
        break;
      }

      // 计算延迟时间
      const delay = Math.min(
        config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1),
        config.maxDelay,
      );

      logger.warn(operationName, `Attempt ${attempt} failed, retrying in ${delay}ms`, {
        error: lastError.message,
        nextAttempt: attempt + 1,
      });

      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw new DataPersistenceError(
    `Operation failed after ${config.maxAttempts} attempts: ${lastError?.message || 'Unknown error'}`,
    'RETRY_EXHAUSTED',
    operationName,
    false,
    lastError,
  );
}

/**
 * 判断错误是否可重试
 */
function isRetryableError(error: Error): boolean {
  // 数据库连接错误通常可重试
  if (
    (error as Error).message.includes('connection') ||
    (error as Error).message.includes('timeout') ||
    (error as Error).message.includes('ECONNRESET') ||
    (error as Error).message.includes('ENOTFOUND')
  ) {
    return true;
  }

  // 临时性数据库错误可重试
  if (
    (error as Error).message.includes('deadlock') ||
    (error as Error).message.includes('lock timeout') ||
    (error as Error).message.includes('too many connections')
  ) {
    return true;
  }

  // 数据验证错误不可重试
  if (
    (error as Error).message.includes('validation') ||
    (error as Error).message.includes('constraint') ||
    (error as Error).message.includes('duplicate key')
  ) {
    return false;
  }

  // 默认情况下，未知错误可重试一次
  return true;
}

/**
 * 性能监控装饰器
 */
function withPerformanceMonitoring<T extends any[], R>(
  target: any,
  propertyName: string,
  descriptor: TypedPropertyDescriptor<(...args: T) => Promise<R>>,
) {
  const originalMethod = descriptor.value!;
  const logger = DataPersistenceLogger.getInstance();

  descriptor.value = async function (...args: T): Promise<R> {
    const startTime = Date.now();
    const operationName = `${target.constructor.name}.${propertyName}`;

    try {
      logger.debug(operationName, 'Operation started');
      const result = await originalMethod.apply(this, args);
      const duration = Date.now() - startTime;

      logger.info(operationName, `Operation completed successfully`, {
        duration: `${duration}ms`,
        args: args.length,
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(operationName, `Operation failed`, error as Error, {
        duration: `${duration}ms`,
        args: args.length,
      });
      throw error;
    }
  };

  return descriptor;
}

export class DataPersistenceService {
  private callLogRepository: Repository<McpCallLog>;
  private serverStatsRepository: Repository<McpServerStats>;
  private monitoringRepository: Repository<McpMonitoringMetrics>;
  private logger: DataPersistenceLogger;

  constructor() {
    const dataSource = getAppDataSource();
    this.callLogRepository = dataSource.getRepository(McpCallLog);
    this.serverStatsRepository = dataSource.getRepository(McpServerStats);
    this.monitoringRepository = dataSource.getRepository(McpMonitoringMetrics);
    this.logger = DataPersistenceLogger.getInstance();
  }

  /**
   * 记录单次工具调用
   */
  @withPerformanceMonitoring
  async recordToolCall(callData: ToolCallRecord): Promise<void> {
    const operation = 'recordToolCall';

    try {
      // 数据验证
      if (!validateToolCallRecord(callData)) {
        throw new DataPersistenceError(
          'Invalid tool call record data',
          'VALIDATION_ERROR',
          operation,
          false,
        );
      }

      // 数据标准化和清理
      const normalizedData = normalizeToolCallRecord(callData);
      const sanitizedData = sanitizeToolCallRecord(normalizedData);

      await executeWithRetry(async () => {
        const callLog = new McpCallLog();
        callLog.server_name = sanitizedData.serverName;
        callLog.tool_name = sanitizedData.toolName;
        callLog.call_time = sanitizedData.callTime;
        callLog.success = sanitizedData.success;
        callLog.response_time = sanitizedData.responseTime || undefined;
        callLog.user_id = sanitizedData.userId || undefined;
        callLog.client_ip = sanitizedData.clientIp || undefined;
        callLog.error_message = sanitizedData.errorMessage || undefined;
        callLog.request_params = sanitizedData.requestParams || undefined;
        callLog.response_data = sanitizedData.responseData || undefined;

        await this.callLogRepository.save(callLog);

        this.logger.debug(operation, 'Tool call recorded successfully', {
          serverName: sanitizedData.serverName,
          toolName: sanitizedData.toolName,
          success: sanitizedData.success,
        });
      }, operation);

      // 异步更新聚合统计
      this.updateAggregateStats(sanitizedData.serverName, sanitizedData.callTime).catch((error) => {
        this.logger.error('updateAggregateStats', 'Failed to update aggregate stats', error, {
          serverName: sanitizedData.serverName,
          callTime: sanitizedData.callTime,
        });
      });
    } catch (error) {
      if (error instanceof DataPersistenceError) {
        throw error;
      }

      throw new DataPersistenceError(
        `Failed to record tool call: ${(error as Error).message}`,
        'RECORD_ERROR',
        operation,
        isRetryableError(error as Error),
        error as Error,
      );
    }
  }

  /**
   * 批量记录工具调用
   */
  @withPerformanceMonitoring
  async batchRecordCalls(callDataList: ToolCallRecord[]): Promise<void> {
    const operation = 'batchRecordCalls';

    if (callDataList.length === 0) {
      this.logger.warn(operation, 'Empty call data list provided');
      return;
    }

    try {
      // 批量数据验证
      const validatedData: ToolCallRecord[] = [];
      const invalidData: any[] = [];

      for (let i = 0; i < callDataList.length; i++) {
        const callData = callDataList[i];
        if (validateToolCallRecord(callData)) {
          const normalized = normalizeToolCallRecord(callData);
          const sanitized = sanitizeToolCallRecord(normalized);
          validatedData.push(sanitized);
        } else {
          invalidData.push({ index: i, data: callData });
        }
      }

      if (invalidData.length > 0) {
        this.logger.warn(
          operation,
          `Found ${invalidData.length} invalid records out of ${callDataList.length}`,
          {
            invalidIndices: invalidData.map((item) => item.index),
          },
        );
      }

      if (validatedData.length === 0) {
        throw new DataPersistenceError(
          'No valid records to process',
          'VALIDATION_ERROR',
          operation,
          false,
        );
      }

      await executeWithRetry(async () => {
        const callLogs = validatedData.map((callData) => {
          const callLog = new McpCallLog();
          callLog.server_name = callData.serverName;
          callLog.tool_name = callData.toolName;
          callLog.call_time = callData.callTime;
          callLog.success = callData.success;
          callLog.response_time = callData.responseTime || undefined;
          callLog.user_id = callData.userId || undefined;
          callLog.client_ip = callData.clientIp || undefined;
          callLog.error_message = callData.errorMessage || undefined;
          callLog.request_params = callData.requestParams || undefined;
          callLog.response_data = callData.responseData || undefined;
          return callLog;
        });

        await this.callLogRepository.save(callLogs);

        this.logger.info(
          operation,
          `Batch recorded ${validatedData.length} tool calls successfully`,
        );
      }, operation);

      // 异步更新聚合统计
      const serverNames = [...new Set(validatedData.map((call) => call.serverName))];
      const dates = [...new Set(validatedData.map((call) => call.callTime.toDateString()))];

      for (const serverName of serverNames) {
        for (const dateStr of dates) {
          this.updateAggregateStats(serverName, new Date(dateStr)).catch((error) => {
            this.logger.error('updateAggregateStats', 'Failed to update aggregate stats', error, {
              serverName,
              date: dateStr,
            });
          });
        }
      }
    } catch (error) {
      if (error instanceof DataPersistenceError) {
        throw error;
      }

      throw new DataPersistenceError(
        `Failed to batch record tool calls: ${(error as Error).message}`,
        'BATCH_RECORD_ERROR',
        operation,
        isRetryableError(error as Error),
        error as Error,
      );
    }
  }

  /**
   * 更新聚合统计数据
   */
  @withPerformanceMonitoring
  async updateAggregateStats(serverName: string, date: Date): Promise<void> {
    const operation = 'updateAggregateStats';

    try {
      if (!serverName || !date) {
        throw new DataPersistenceError(
          'Invalid parameters for aggregate stats update',
          'VALIDATION_ERROR',
          operation,
          false,
        );
      }

      const statDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const statHour = date.getHours();

      await executeWithRetry(async () => {
        // 计算小时级别统计
        await this.calculateAndSaveStats(serverName, statDate, statHour);

        // 计算日级别统计
        await this.calculateAndSaveStats(serverName, statDate, null);
      }, operation);
    } catch (error) {
      if (error instanceof DataPersistenceError) {
        throw error;
      }

      throw new DataPersistenceError(
        `Failed to update aggregate stats: ${(error as Error).message}`,
        'AGGREGATE_ERROR',
        operation,
        isRetryableError(error as Error),
        error as Error,
      );
    }
  }

  /**
   * 计算并保存统计数据
   */
  private async calculateAndSaveStats(
    serverName: string,
    statDate: Date,
    statHour: number | null,
  ): Promise<void> {
    const operation = 'calculateAndSaveStats';
    const dataSource = getAppDataSource();

    try {
      // 构建查询条件和参数
      let query = '';
      let queryParams: any[] = [];
      
      if (statHour !== null) {
        // 小时级别统计
        const timeParam = new Date(statDate.getFullYear(), statDate.getMonth(), statDate.getDate(), statHour);
        query = `
        SELECT 
          COUNT(*) as total_calls,
          COUNT(*) FILTER (WHERE success = true) as success_calls,
          COUNT(*) FILTER (WHERE success = false) as failed_calls,
          AVG(response_time) as avg_response_time,
          MIN(response_time) as min_response_time,
          MAX(response_time) as max_response_time,
          COUNT(DISTINCT user_id) as unique_users
        FROM mcp_call_logs 
        WHERE server_name = $1 
          AND call_time >= $2::timestamp 
          AND call_time < $2::timestamp + INTERVAL '1 hour'
        `;
        queryParams = [serverName, timeParam];
      } else {
        // 日级别统计
        query = `
        SELECT 
          COUNT(*) as total_calls,
          COUNT(*) FILTER (WHERE success = true) as success_calls,
          COUNT(*) FILTER (WHERE success = false) as failed_calls,
          AVG(response_time) as avg_response_time,
          MIN(response_time) as min_response_time,
          MAX(response_time) as max_response_time,
          COUNT(DISTINCT user_id) as unique_users
        FROM mcp_call_logs 
        WHERE server_name = $1 
          AND call_time >= $2::date 
          AND call_time < $2::date + INTERVAL '1 day'
        `;
        queryParams = [serverName, statDate];
      }

      // 查询统计数据
      const stats = await dataSource.query(query, queryParams);

      if (stats.length > 0 && parseInt(stats[0].total_calls) > 0) {
        // 使用 UPSERT 更新或插入统计数据
        await dataSource.query(
          `
          INSERT INTO mcp_server_stats (
            server_name, stat_date, stat_hour, total_calls, success_calls, 
            failed_calls, avg_response_time, min_response_time, max_response_time, 
            unique_users, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW())
          ON CONFLICT (server_name, stat_date, stat_hour) 
          DO UPDATE SET 
            total_calls = EXCLUDED.total_calls,
            success_calls = EXCLUDED.success_calls,
            failed_calls = EXCLUDED.failed_calls,
            avg_response_time = EXCLUDED.avg_response_time,
            min_response_time = EXCLUDED.min_response_time,
            max_response_time = EXCLUDED.max_response_time,
            unique_users = EXCLUDED.unique_users,
            updated_at = NOW()
        `,
          [
            serverName,
            statDate,
            statHour,
            parseInt(stats[0].total_calls),
            parseInt(stats[0].success_calls),
            parseInt(stats[0].failed_calls),
            stats[0].avg_response_time ? parseFloat(stats[0].avg_response_time) : null,
            stats[0].min_response_time ? parseInt(stats[0].min_response_time) : null,
            stats[0].max_response_time ? parseInt(stats[0].max_response_time) : null,
            parseInt(stats[0].unique_users),
          ],
        );

        this.logger.debug(operation, 'Stats calculated and saved', {
          serverName,
          statDate: statDate.toISOString(),
          statHour,
          totalCalls: parseInt(stats[0].total_calls),
        });
      }
    } catch (error) {
      this.logger.error(operation, 'Failed to calculate and save stats', error as Error, {
        serverName,
        statDate: statDate.toISOString(),
        statHour,
      });
      throw error;
    }
  }

  /**
   * 查询历史数据
   */
  @withPerformanceMonitoring
  async queryHistory(params: HistoryQueryParams): Promise<HistoryQueryResponse> {
    const operation = 'queryHistory';

    try {
      // 数据验证
      if (!validateHistoryQueryParams(params)) {
        throw new DataPersistenceError(
          'Invalid history query parameters',
          'VALIDATION_ERROR',
          operation,
          false,
        );
      }

      const normalizedParams = normalizeHistoryQueryParams(params);
      const { timeRange, filters, pagination, aggregation } = normalizedParams;

      return await executeWithRetry(async () => {
        // 构建基础查询条件
        const whereConditions = ['call_time >= $1', 'call_time <= $2'];
        const queryParams: any[] = [timeRange.startTime, timeRange.endTime];
        let paramIndex = 3;

        if (filters?.serverNames && filters.serverNames.length > 0) {
          whereConditions.push(`server_name = ANY($${paramIndex})`);
          queryParams.push(filters.serverNames);
          paramIndex++;
        }

        if (filters?.toolNames && filters.toolNames.length > 0) {
          whereConditions.push(`tool_name = ANY($${paramIndex})`);
          queryParams.push(filters.toolNames);
          paramIndex++;
        }

        if (filters?.clientIps && filters.clientIps.length > 0) {
          whereConditions.push(`client_ip = ANY($${paramIndex})`);
          queryParams.push(filters.clientIps);
          paramIndex++;
        }

        if (filters?.success !== undefined) {
          whereConditions.push(`success = $${paramIndex}`);
          queryParams.push(filters.success);
          paramIndex++;
        }

        const whereClause = whereConditions.join(' AND ');

        // 如果有聚合参数，执行时间序列聚合查询
        if (aggregation && aggregation.groupBy && aggregation.metrics) {
          return await this.executeAggregatedQuery(
            whereClause,
            queryParams,
            aggregation,
            pagination,
          );
        }

        // 否则执行常规的详细记录查询
        return await this.executeDetailedQuery(whereClause, queryParams, pagination);
      }, operation);
    } catch (error) {
      if (error instanceof DataPersistenceError) {
        throw error;
      }

      throw new DataPersistenceError(
        `Failed to query history: ${(error as Error).message}`,
        'QUERY_ERROR',
        operation,
        isRetryableError(error as Error),
        error as Error,
      );
    }
  }

  /**
   * 执行时间序列聚合查询
   */
  private async executeAggregatedQuery(
    whereClause: string,
    queryParams: any[],
    aggregation: { groupBy: string; metrics: string[] },
    _pagination: { page: number; pageSize: number },
  ): Promise<HistoryQueryResponse> {
    const operation = 'executeAggregatedQuery';

    // 确定时间分组函数
    const timeGroupFunction = this.getTimeGroupFunction(aggregation.groupBy);

    // 构建聚合查询的SELECT子句
    const selectClauses = [`${timeGroupFunction} as time_bucket`];
    const metricsMap: Record<string, string> = {};

    if (aggregation.metrics.includes('count')) {
      selectClauses.push('COUNT(*) as count');
      metricsMap.count = 'count';
    }

    if (aggregation.metrics.includes('success_rate')) {
      selectClauses.push(
        'COUNT(*) FILTER (WHERE success = true) * 100.0 / NULLIF(COUNT(*), 0) as success_rate',
      );
      metricsMap.success_rate = 'success_rate';
    }

    if (aggregation.metrics.includes('avg_response_time')) {
      selectClauses.push('AVG(response_time) as avg_response_time');
      metricsMap.avg_response_time = 'avg_response_time';
    }

    // 执行聚合查询
    const aggregatedResult = await getAppDataSource().query(
      `
      SELECT ${selectClauses.join(', ')}
      FROM mcp_call_logs
      WHERE ${whereClause}
      GROUP BY ${timeGroupFunction}
      ORDER BY time_bucket
    `,
      queryParams,
    );

    // 查询总体聚合数据
    const totalAggregateResult = await getAppDataSource().query(
      `
      SELECT 
        COUNT(*) as total_calls,
        COUNT(*) FILTER (WHERE success = true) as success_calls,
        AVG(response_time) as avg_response_time
      FROM mcp_call_logs WHERE ${whereClause}
    `,
      queryParams,
    );

    const totalAggregate = totalAggregateResult[0];
    const totalCalls = parseInt(totalAggregate.total_calls);
    const successRate =
      totalCalls > 0 ? (parseInt(totalAggregate.success_calls) / totalCalls) * 100 : 0;

    // 转换时间序列数据
    const timeSeries = aggregatedResult.map((row: any) => ({
      timestamp: row.time_bucket.toISOString(),
      metrics: Object.keys(metricsMap).reduce((acc, metricKey) => {
        const dbColumn = metricsMap[metricKey];
        if (row[dbColumn] !== null && row[dbColumn] !== undefined) {
          acc[metricKey] =
            metricKey === 'success_rate' || metricKey === 'avg_response_time'
              ? Math.round(parseFloat(row[dbColumn]) * 100) / 100
              : parseInt(row[dbColumn]);
        }
        return acc;
      }, {} as any),
    }));

    this.logger.info(operation, 'Aggregated query completed successfully', {
      timeSeriesPoints: timeSeries.length,
      groupBy: aggregation.groupBy,
      metrics: aggregation.metrics,
    });

    return {
      data: [], // 聚合模式下不返回详细记录
      pagination: {
        page: 1,
        pageSize: timeSeries.length,
        total: timeSeries.length,
        totalPages: 1,
      },
      aggregates: {
        totalCalls,
        successRate: Math.round(successRate * 100) / 100,
        avgResponseTime: totalAggregate.avg_response_time
          ? Math.round(parseFloat(totalAggregate.avg_response_time) * 100) / 100
          : 0,
      },
      timeSeries,
      isAggregated: true,
    } as any;
  }

  /**
   * 执行详细记录查询
   */
  private async executeDetailedQuery(
    whereClause: string,
    queryParams: any[],
    pagination: { page: number; pageSize: number },
  ): Promise<HistoryQueryResponse> {
    const operation = 'executeDetailedQuery';

    // 查询总数
    const countResult = await getAppDataSource().query(
      `
      SELECT COUNT(*) as total FROM mcp_call_logs WHERE ${whereClause}
    `,
      queryParams,
    );

    const total = parseInt(countResult[0].total);
    const totalPages = Math.ceil(total / pagination.pageSize);

    // 查询数据
    const offset = (pagination.page - 1) * pagination.pageSize;
    const paramIndexForLimit = queryParams.length + 1;
    const paramIndexForOffset = queryParams.length + 2;

    const dataResult = await getAppDataSource().query(
      `
      SELECT
        id, server_name, tool_name, call_time, success, response_time,
        user_id, client_ip, error_message, request_params, response_data, created_at
      FROM mcp_call_logs
      WHERE ${whereClause}
      ORDER BY call_time DESC
      LIMIT $${paramIndexForLimit} OFFSET $${paramIndexForOffset}
    `,
      [...queryParams, pagination.pageSize, offset],
    );

    // 查询聚合数据
    const aggregateResult = await getAppDataSource().query(
      `
      SELECT 
        COUNT(*) as total_calls,
        COUNT(*) FILTER (WHERE success = true) as success_calls,
        AVG(response_time) as avg_response_time
      FROM mcp_call_logs WHERE ${whereClause}
    `,
      queryParams,
    );

    const aggregate = aggregateResult[0];
    const successRate = total > 0 ? (parseInt(aggregate.success_calls) / total) * 100 : 0;

    this.logger.info(operation, 'Detailed query completed successfully', {
      total,
      pageSize: pagination.pageSize,
      page: pagination.page,
    });

    return {
      data: dataResult.map((row: any) => ({
        id: row.id,
        server_name: row.server_name,
        tool_name: row.tool_name,
        timestamp: row.call_time,
        success: row.success,
        response_time: row.response_time,
        user_id: row.user_id,
        client_ip: row.client_ip,
        error_message: row.error_message,
        request_params: row.request_params,
        response_data: row.response_data,
        created_at: row.created_at,
      })),
      pagination: {
        page: pagination.page,
        pageSize: pagination.pageSize,
        total,
        totalPages,
      },
      aggregates: {
        totalCalls: total,
        successRate: Math.round(successRate * 100) / 100,
        avgResponseTime: aggregate.avg_response_time
          ? Math.round(parseFloat(aggregate.avg_response_time) * 100) / 100
          : 0,
      },
    };
  }

  /**
   * 获取时间分组函数
   */
  private getTimeGroupFunction(groupBy: string): string {
    switch (groupBy) {
      case 'hour':
        return "date_trunc('hour', call_time)";
      case 'day':
        return "date_trunc('day', call_time)";
      case 'week':
        return "date_trunc('week', call_time)";
      case 'month':
        return "date_trunc('month', call_time)";
      default:
        throw new DataPersistenceError(
          `Unsupported groupBy value: ${groupBy}`,
          'VALIDATION_ERROR',
          'getTimeGroupFunction',
          false,
        );
    }
  }

  /**
   * 获取服务器最新统计数据
   */
  @withPerformanceMonitoring
  async getLatestStats(serverName: string): Promise<AggregateStats | null> {
    const operation = 'getLatestStats';

    try {
      if (!serverName || typeof serverName !== 'string') {
        throw new DataPersistenceError('Invalid server name', 'VALIDATION_ERROR', operation, false);
      }

      return await executeWithRetry(async () => {
        const result = await this.serverStatsRepository.findOne({
          where: { server_name: serverName, stat_hour: IsNull() },
          order: { stat_date: 'DESC' },
        });

        if (!result) {
          this.logger.debug(operation, 'No stats found for server', { serverName });
          return null;
        }

        this.logger.debug(operation, 'Latest stats retrieved successfully', {
          serverName,
          statDate: result.stat_date,
        });

        return {
          serverName: result.server_name,
          statDate: result.stat_date,
          statHour: result.stat_hour,
          totalCalls: result.total_calls,
          successCalls: result.success_calls,
          failedCalls: result.failed_calls,
          avgResponseTime: result.avg_response_time
            ? parseFloat(result.avg_response_time.toString())
            : undefined,
          minResponseTime: result.min_response_time,
          maxResponseTime: result.max_response_time,
          uniqueUsers: result.unique_users,
          updatedAt: result.updated_at,
        };
      }, operation);
    } catch (error) {
      if (error instanceof DataPersistenceError) {
        throw error;
      }

      throw new DataPersistenceError(
        `Failed to get latest stats: ${(error as Error).message}`,
        'STATS_ERROR',
        operation,
        isRetryableError(error as Error),
        error as Error,
      );
    }
  }

  /**
   * 记录监控指标
   */
  @withPerformanceMonitoring
  async recordMetric(metric: MonitoringMetric): Promise<void> {
    const operation = 'recordMetric';

    try {
      // 数据验证
      if (!validateMonitoringMetric(metric)) {
        throw new DataPersistenceError(
          'Invalid monitoring metric data',
          'VALIDATION_ERROR',
          operation,
          false,
        );
      }

      const normalizedMetric = normalizeMonitoringMetric(metric);

      await executeWithRetry(async () => {
        const monitoringMetric = new McpMonitoringMetrics();
        monitoringMetric.metric_name = normalizedMetric.metricName;
        monitoringMetric.metric_value = normalizedMetric.metricValue;
        monitoringMetric.metric_type = normalizedMetric.metricType;
        monitoringMetric.labels = normalizedMetric.labels || undefined;
        monitoringMetric.recorded_at = normalizedMetric.recordedAt;

        await this.monitoringRepository.save(monitoringMetric);

        this.logger.debug(operation, 'Metric recorded successfully', {
          metricName: normalizedMetric.metricName,
          metricType: normalizedMetric.metricType,
          metricValue: normalizedMetric.metricValue,
        });
      }, operation);
    } catch (error) {
      if (error instanceof DataPersistenceError) {
        throw error;
      }

      throw new DataPersistenceError(
        `Failed to record metric: ${(error as Error).message}`,
        'METRIC_ERROR',
        operation,
        isRetryableError(error as Error),
        error as Error,
      );
    }
  }

  /**
   * 清理过期数据
   */
  @withPerformanceMonitoring
  async cleanupExpiredData(retentionDays: number): Promise<void> {
    const operation = 'cleanupExpiredData';

    try {
      if (!retentionDays || retentionDays < 1) {
        throw new DataPersistenceError(
          'Invalid retention days',
          'VALIDATION_ERROR',
          operation,
          false,
        );
      }

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      await executeWithRetry(async () => {
        // 清理调用日志
        const callLogResult = await getAppDataSource().query(
          `
          DELETE FROM mcp_call_logs WHERE created_at < $1
        `,
          [cutoffDate],
        );

        // 清理监控指标
        const metricsResult = await getAppDataSource().query(
          `
          DELETE FROM mcp_monitoring_metrics WHERE recorded_at < $1
        `,
          [cutoffDate],
        );

        const callLogsDeleted = callLogResult.affectedRows || 0;
        const metricsDeleted = metricsResult.affectedRows || 0;

        this.logger.info(operation, 'Data cleanup completed successfully', {
          retentionDays,
          cutoffDate: cutoffDate.toISOString(),
          callLogsDeleted,
          metricsDeleted,
        });
      }, operation);
    } catch (error) {
      if (error instanceof DataPersistenceError) {
        throw error;
      }

      throw new DataPersistenceError(
        `Failed to cleanup expired data: ${(error as Error).message}`,
        'CLEANUP_ERROR',
        operation,
        isRetryableError(error as Error),
        error as Error,
      );
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; details: any }> {
    const operation = 'healthCheck';

    try {
      const dataSource = getAppDataSource();

      // 检查数据库连接
      await dataSource.query('SELECT 1');

      // 检查表是否存在
      const tables = await dataSource.query(`
        SELECT table_name FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN ('mcp_call_logs', 'mcp_server_stats', 'mcp_monitoring_metrics')
      `);

      const expectedTables = ['mcp_call_logs', 'mcp_server_stats', 'mcp_monitoring_metrics'];
      const existingTables = tables.map((row: any) => row.table_name);
      const missingTables = expectedTables.filter((table) => !existingTables.includes(table));

      if (missingTables.length > 0) {
        this.logger.error(operation, 'Missing required tables', undefined, { missingTables });
        return {
          status: 'unhealthy',
          details: {
            error: 'Missing required tables',
            missingTables,
          },
        };
      }

      this.logger.debug(operation, 'Health check passed');
      return {
        status: 'healthy',
        details: {
          tablesExist: existingTables,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      this.logger.error(operation, 'Health check failed', error as Error);
      return {
        status: 'unhealthy',
        details: {
          error: (error as Error).message,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * 收集性能指标
   */
  async collectPerformanceMetrics(): Promise<void> {
    const operation = 'collectPerformanceMetrics';

    try {
      const dataSource = getAppDataSource();
      const now = new Date();

      // 收集数据库连接池指标
      if (dataSource.driver && (dataSource.driver as any).master) {
        const pool = (dataSource.driver as any).master;

        // 记录连接池状态
        await this.recordMetric({
          metricName: 'db_connection_pool_size',
          metricValue: pool.totalCount || 0,
          metricType: 'gauge',
          recordedAt: now,
          labels: { pool: 'master' },
        });

        await this.recordMetric({
          metricName: 'db_connection_pool_used',
          metricValue: pool.usedCount || 0,
          metricType: 'gauge',
          recordedAt: now,
          labels: { pool: 'master' },
        });

        await this.recordMetric({
          metricName: 'db_connection_pool_free',
          metricValue: pool.freeCount || 0,
          metricType: 'gauge',
          recordedAt: now,
          labels: { pool: 'master' },
        });
      }

      // 收集数据库表大小指标
      const tableSizes = await dataSource.query(`
        SELECT 
          schemaname,
          tablename,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
          pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename IN ('mcp_call_logs', 'mcp_server_stats', 'mcp_monitoring_metrics')
      `);

      for (const table of tableSizes) {
        await this.recordMetric({
          metricName: 'db_table_size_bytes',
          metricValue: parseInt(table.size_bytes),
          metricType: 'gauge',
          recordedAt: now,
          labels: { table: table.tablename },
        });
      }

      // 收集数据库行数指标
      const rowCounts = await dataSource.query(`
        SELECT 
          'mcp_call_logs' as table_name,
          COUNT(*) as row_count
        FROM mcp_call_logs
        UNION ALL
        SELECT 
          'mcp_server_stats' as table_name,
          COUNT(*) as row_count
        FROM mcp_server_stats
        UNION ALL
        SELECT 
          'mcp_monitoring_metrics' as table_name,
          COUNT(*) as row_count
        FROM mcp_monitoring_metrics
      `);

      for (const row of rowCounts) {
        await this.recordMetric({
          metricName: 'db_table_row_count',
          metricValue: parseInt(row.row_count),
          metricType: 'gauge',
          recordedAt: now,
          labels: { table: row.table_name },
        });
      }

      // 收集最近24小时的调用统计
      const recentStats = await dataSource.query(`
        SELECT 
          COUNT(*) as total_calls,
          COUNT(*) FILTER (WHERE success = true) as success_calls,
          COUNT(*) FILTER (WHERE success = false) as failed_calls,
          AVG(response_time) as avg_response_time,
          MAX(response_time) as max_response_time,
          COUNT(DISTINCT server_name) as active_servers
        FROM mcp_call_logs 
        WHERE call_time >= NOW() - INTERVAL '24 hours'
      `);

      if (recentStats.length > 0) {
        const stats = recentStats[0];

        await this.recordMetric({
          metricName: 'calls_total_24h',
          metricValue: parseInt(stats.total_calls),
          metricType: 'counter',
          recordedAt: now,
        });

        await this.recordMetric({
          metricName: 'calls_success_24h',
          metricValue: parseInt(stats.success_calls),
          metricType: 'counter',
          recordedAt: now,
        });

        await this.recordMetric({
          metricName: 'calls_failed_24h',
          metricValue: parseInt(stats.failed_calls),
          metricType: 'counter',
          recordedAt: now,
        });

        if (stats.avg_response_time) {
          await this.recordMetric({
            metricName: 'response_time_avg_24h',
            metricValue: parseFloat(stats.avg_response_time),
            metricType: 'gauge',
            recordedAt: now,
          });
        }

        if (stats.max_response_time) {
          await this.recordMetric({
            metricName: 'response_time_max_24h',
            metricValue: parseInt(stats.max_response_time),
            metricType: 'gauge',
            recordedAt: now,
          });
        }

        await this.recordMetric({
          metricName: 'active_servers_24h',
          metricValue: parseInt(stats.active_servers),
          metricType: 'gauge',
          recordedAt: now,
        });
      }

      // 收集系统内存使用情况
      const memUsage = process.memoryUsage();

      await this.recordMetric({
        metricName: 'nodejs_memory_heap_used_bytes',
        metricValue: memUsage.heapUsed,
        metricType: 'gauge',
        recordedAt: now,
      });

      await this.recordMetric({
        metricName: 'nodejs_memory_heap_total_bytes',
        metricValue: memUsage.heapTotal,
        metricType: 'gauge',
        recordedAt: now,
      });

      await this.recordMetric({
        metricName: 'nodejs_memory_rss_bytes',
        metricValue: memUsage.rss,
        metricType: 'gauge',
        recordedAt: now,
      });

      this.logger.info(operation, 'Performance metrics collected successfully');
    } catch (error) {
      this.logger.error(operation, 'Failed to collect performance metrics', error as Error);
      throw new DataPersistenceError(
        `Failed to collect performance metrics: ${(error as Error).message}`,
        'METRICS_COLLECTION_ERROR',
        operation,
        true,
        error as Error,
      );
    }
  }

  /**
   * 启动定期性能监控
   */
  startPerformanceMonitoring(intervalMinutes: number = 5): NodeJS.Timeout {
    const operation = 'startPerformanceMonitoring';

    this.logger.info(
      operation,
      `Starting performance monitoring with ${intervalMinutes} minute intervals`,
    );

    return setInterval(
      async () => {
        try {
          await this.collectPerformanceMetrics();
        } catch (error) {
          this.logger.error(operation, 'Performance monitoring cycle failed', error as Error);
        }
      },
      intervalMinutes * 60 * 1000,
    );
  }

  /**
   * 获取性能监控报告
   */
  async getPerformanceReport(hours: number = 24): Promise<any> {
    const operation = 'getPerformanceReport';

    try {
      const dataSource = getAppDataSource();
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - hours);

      // 获取最近的性能指标
      const metrics = await dataSource.query(
        `
        SELECT 
          metric_name,
          metric_type,
          AVG(metric_value) as avg_value,
          MIN(metric_value) as min_value,
          MAX(metric_value) as max_value,
          COUNT(*) as sample_count,
          labels
        FROM mcp_monitoring_metrics 
        WHERE recorded_at >= $1
        GROUP BY metric_name, metric_type, labels
        ORDER BY metric_name
      `,
        [cutoffTime],
      );

      // 组织报告数据
      const report = {
        timeRange: {
          start: cutoffTime.toISOString(),
          end: new Date().toISOString(),
          hours,
        },
        database: {
          connectionPool: {} as Record<string, any>,
          tableSizes: {} as Record<string, any>,
          rowCounts: {} as Record<string, any>,
        },
        application: {
          calls: {} as Record<string, any>,
          performance: {} as Record<string, any>,
          memory: {} as Record<string, any>,
        },
        summary: {
          totalMetrics: metrics.length,
          generatedAt: new Date().toISOString(),
        },
      };

      // 分类整理指标
      for (const metric of metrics) {
        const metricData = {
          type: metric.metric_type,
          average: parseFloat(metric.avg_value),
          minimum: parseFloat(metric.min_value),
          maximum: parseFloat(metric.max_value),
          samples: parseInt(metric.sample_count),
          labels: metric.labels,
        };

        if (metric.metric_name.startsWith('db_connection_pool_')) {
          report.database.connectionPool[metric.metric_name] = metricData;
        } else if (metric.metric_name.startsWith('db_table_size_')) {
          report.database.tableSizes[metric.metric_name] = metricData;
        } else if (metric.metric_name.startsWith('db_table_row_')) {
          report.database.rowCounts[metric.metric_name] = metricData;
        } else if (metric.metric_name.startsWith('calls_')) {
          report.application.calls[metric.metric_name] = metricData;
        } else if (metric.metric_name.startsWith('response_time_')) {
          report.application.performance[metric.metric_name] = metricData;
        } else if (metric.metric_name.startsWith('nodejs_memory_')) {
          report.application.memory[metric.metric_name] = metricData;
        }
      }

      this.logger.info(operation, 'Performance report generated successfully', {
        hours,
        metricsCount: metrics.length,
      });

      return report;
    } catch (error) {
      this.logger.error(operation, 'Failed to generate performance report', error as Error);
      throw new DataPersistenceError(
        `Failed to generate performance report: ${(error as Error).message}`,
        'REPORT_ERROR',
        operation,
        isRetryableError(error as Error),
        error as Error,
      );
    }
  }

  /**
   * 趋势分析查询
   */
  @withPerformanceMonitoring
  async queryTrends(params: TrendAnalysisParams): Promise<TrendAnalysisResponse> {
    const operation = 'queryTrends';

    try {
      // 参数验证
      if (
        !params.timeRange ||
        !params.granularity ||
        !params.metrics ||
        params.metrics.length === 0
      ) {
        throw new DataPersistenceError(
          'Invalid trend analysis parameters',
          'VALIDATION_ERROR',
          operation,
          false,
        );
      }

      // 计算时间范围
      const timeRange = this.calculateTimeRange(params.timeRange, params.startTime, params.endTime);

      return await executeWithRetry(async () => {
        // 构建查询条件
        const whereConditions = ['call_time >= $1', 'call_time <= $2'];
        const queryParams: any[] = [timeRange.start, timeRange.end];
        let paramIndex = 3;

        if (params.serverNames && params.serverNames.length > 0) {
          whereConditions.push(`server_name = ANY($${paramIndex})`);
          queryParams.push(params.serverNames);
          paramIndex++;
        }

        const whereClause = whereConditions.join(' AND ');

        // 获取时间分组函数
        const timeGroupFunction = this.getTimeGroupFunction(params.granularity);

        // 构建聚合查询
        const selectClauses = [`${timeGroupFunction} as time_bucket`];
        const metricsMap: Record<string, string> = {};

        if (params.metrics.includes('calls')) {
          selectClauses.push('COUNT(*) as calls');
          metricsMap.calls = 'calls';
        }

        if (params.metrics.includes('success_rate')) {
          selectClauses.push(
            'COUNT(*) FILTER (WHERE success = true) * 100.0 / NULLIF(COUNT(*), 0) as success_rate',
          );
          metricsMap.success_rate = 'success_rate';
        }

        if (params.metrics.includes('response_time')) {
          selectClauses.push('AVG(response_time) as response_time');
          metricsMap.response_time = 'response_time';
        }

        // 执行时间序列查询
        const timeSeriesResult = await getAppDataSource().query(
          `
          SELECT ${selectClauses.join(', ')}
          FROM mcp_call_logs
          WHERE ${whereClause}
          GROUP BY ${timeGroupFunction}
          ORDER BY time_bucket
        `,
          queryParams,
        );

        // 构建时间序列数据
        const series: Record<string, { timestamps: string[]; values: number[] }> = {};

        // 初始化每个指标的数组
        params.metrics.forEach((metric) => {
          series[metric] = {
            timestamps: [],
            values: [],
          };
        });

        // 填充时间序列数据
        timeSeriesResult.forEach((row: any) => {
          const timestamp = row.time_bucket.toISOString();

          params.metrics.forEach((metric) => {
            const dbColumn = metricsMap[metric];
            if (dbColumn && row[dbColumn] !== null && row[dbColumn] !== undefined) {
              series[metric].timestamps.push(timestamp);
              const value =
                metric === 'success_rate' || metric === 'response_time'
                  ? Math.round(parseFloat(row[dbColumn]) * 100) / 100
                  : parseInt(row[dbColumn]);
              series[metric].values.push(value);
            }
          });
        });

        // 检查数据点数量是否足够进行分析
        const minDataPoints = 5; // 趋势分析至少需要5个数据点
        const hasInsufficientData = params.metrics.some(metric =>
          !series[metric] || series[metric].values.length < minDataPoints
        );

        if (hasInsufficientData) {
          this.logger.warn(operation, 'Insufficient data points for trend analysis', {
            timeRange: params.timeRange,
            granularity: params.granularity,
            metrics: params.metrics,
            dataPoints: timeSeriesResult.length,
            minRequired: minDataPoints,
          });
        }

        // 计算趋势洞察
        const insightsResult = await this.calculateTrendInsights(series, params.metrics);

        this.logger.info(operation, 'Trend analysis completed successfully', {
          timeRange: params.timeRange,
          granularity: params.granularity,
          metrics: params.metrics,
          dataPoints: timeSeriesResult.length,
          insightsCount: insightsResult.insights.length,
        });

        return {
          timeRange: {
            start: timeRange.start.toISOString(),
            end: timeRange.end.toISOString(),
            granularity: params.granularity,
          },
          series,
          analysis: insightsResult.analysis,
          anomalies: insightsResult.anomalies,
          insights: insightsResult.insights,
        };
      }, operation);
    } catch (error) {
      if (error instanceof DataPersistenceError) {
        throw error;
      }

      throw new DataPersistenceError(
        `Failed to query trends: ${(error as Error).message}`,
        'TREND_QUERY_ERROR',
        operation,
        isRetryableError(error as Error),
        error as Error,
      );
    }
  }

  /**
   * 计算时间范围
   */
  private calculateTimeRange(
    timeRange: string,
    startTime?: string,
    endTime?: string,
  ): { start: Date; end: Date } {
    const now = new Date();
    let start: Date;
    let end: Date = now;

    switch (timeRange) {
      case '1h':
        start = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '24h':
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'custom':
        if (!startTime || !endTime) {
          throw new DataPersistenceError(
            'Custom time range requires startTime and endTime',
            'VALIDATION_ERROR',
            'calculateTimeRange',
            false,
          );
        }
        start = new Date(startTime);
        end = new Date(endTime);
        break;
      default:
        throw new DataPersistenceError(
          `Unsupported time range: ${timeRange}`,
          'VALIDATION_ERROR',
          'calculateTimeRange',
          false,
        );
    }

    return { start, end };
  }

  /**
   * 计算趋势洞察
   */
  private async calculateTrendInsights(
    series: Record<string, { timestamps: string[]; values: number[] }>,
    metrics: string[],
  ): Promise<{
    analysis: Record<string, { trend: 'increasing' | 'decreasing' | 'stable'; changeRate: number; strength?: number }>; 
    anomalies: Record<string, AnomalyPoint[]>;
    insights: Insight[];
  }> {
    const operation = 'calculateTrendInsights';

    try {
      const analysis: Record<
        string,
        { trend: 'increasing' | 'decreasing' | 'stable'; changeRate: number; strength?: number }
      > = {};
      const anomaliesMap: Record<string, AnomalyPoint[]> = {};

      // 计算每个指标的趋势和异常
      for (const metric of metrics) {
        if (!series[metric] || series[metric].values.length < 2) {
          this.logger.warn(operation, `Insufficient data points for ${metric} trend analysis`, {
            metric,
            dataPoints: series[metric]?.values.length || 0,
            minRequired: 2,
          });
          analysis[metric] = { trend: 'stable', changeRate: 0, strength: 0 };
          anomaliesMap[metric] = [];
          continue;
        }

        // 对于异常检测，需要更多数据点
        const minAnomalyDetectionPoints = 10;
        if (series[metric].values.length < minAnomalyDetectionPoints) {
          this.logger.info(operation, `Limited data points for ${metric} anomaly detection`, {
            metric,
            dataPoints: series[metric].values.length,
            minRecommended: minAnomalyDetectionPoints,
          });
        }

        const metricSeries = series[metric];
        const values = metricSeries.values;
        const n = values.length;

        // 计算线性回归
        const xSum = (n * (n - 1)) / 2; // 0 + 1 + 2 + ... + (n-1)
        const ySum = values.reduce((sum, val) => sum + val, 0);
        const xySum = values.reduce((sum, val, index) => sum + val * index, 0);
        const x2Sum = (n * (n - 1) * (2 * n - 1)) / 6; // 0² + 1² + 2² + ... + (n-1)²

        const slope = (n * xySum - xSum * ySum) / (n * x2Sum - xSum * xSum);
        const intercept = (ySum - slope * xSum) / n;

        // 计算R²值（决定系数）来衡量趋势强度
        const yMean = ySum / n;
        const ssTotal = values.reduce((sum, val) => sum + Math.pow(val - yMean, 2), 0);
        const ssResidual = values.reduce((sum, val, index) => {
          const predicted = slope * index + intercept;
          return sum + Math.pow(val - predicted, 2);
        }, 0);
        
        const rSquared = ssTotal > 0 ? 1 - (ssResidual / ssTotal) : 0;
        const strength = Math.max(0, Math.min(1, rSquared)); // 确保在0-1范围内

        // 使用线性回归斜率计算变化率
        // 将斜率转换为百分比变化率：斜率相对于平均值的百分比
        const avgValue = yMean;
        const changeRate = avgValue !== 0 ? (slope * (n - 1) / avgValue) * 100 : 0;

        // 添加调试日志
        this.logger.debug(operation, `Trend calculation for ${metric}`, {
          slope: Math.round(slope * 1000) / 1000,
          intercept: Math.round(intercept * 100) / 100,
          rSquared: Math.round(rSquared * 1000) / 1000,
          changeRate: Math.round(changeRate * 100) / 100,
          dataPoints: n,
          avgValue: Math.round(avgValue * 100) / 100,
          timeRange: `${metricSeries.timestamps[0]} to ${metricSeries.timestamps[n - 1]}`
        });

        // 确定趋势方向 - 使用更智能的阈值判断
        let trend: 'increasing' | 'decreasing' | 'stable';
        const absChangeRate = Math.abs(changeRate);
        
        // 根据R²值调整阈值：R²越高，越容易判断为有趋势
        const baseThreshold = 5; // 基础阈值5%
        const adjustedThreshold = baseThreshold * (1 - strength * 0.5); // R²高时降低阈值
        
        if (absChangeRate < adjustedThreshold) {
          trend = 'stable';
        } else if (changeRate > 0) {
          trend = 'increasing';
        } else {
          trend = 'decreasing';
        }

        // 检测异常点
        const anomalies = await this.detectAnomalies(metricSeries, slope, intercept);

        analysis[metric] = {
          trend,
          changeRate: Math.round(changeRate * 100) / 100,
          strength: Math.round(strength * 1000) / 1000, // 保留3位小数
        };
        anomaliesMap[metric] = anomalies;
      }

      // 生成洞察
      const timeRange = {
        start: new Date(series[metrics[0]].timestamps[0]),
        end: new Date(series[metrics[0]].timestamps[series[metrics[0]].timestamps.length - 1]),
      };

      const insights = await this.generateInsights(series, metrics, timeRange, anomaliesMap);

      this.logger.debug(operation, 'Trend insights calculated', {
        metrics,
        analysisCount: Object.keys(analysis).length,
        anomaliesCount: Object.values(anomaliesMap).reduce((sum, arr) => sum + arr.length, 0),
        insightsCount: insights.length,
      });

      return {
        analysis,
        anomalies: anomaliesMap,
        insights,
      };
    } catch (error) {
      this.logger.error(operation, 'Failed to calculate trend insights', error as Error);
      return {
        analysis: metrics.reduce(
          (acc, metric) => ({ ...acc, [metric]: { trend: 'stable', changeRate: 0, strength: 0 } }),
          {},
        ),
        anomalies: metrics.reduce((acc, metric) => ({ ...acc, [metric]: [] }), {}),
        insights: [],
      };
    }
  }

  /**
   * 生成洞察
   */
  private async generateInsights(
    series: Record<string, { timestamps: string[]; values: number[] }>,
    metrics: string[],
    timeRange: { start: Date; end: Date },
    anomalies: Record<string, AnomalyPoint[]>,
  ): Promise<Insight[]> {
    const operation = 'generateInsights';

    try {
      const insights: Insight[] = [];

      // 生成趋势洞察
      const trendInsights = await this.generateTrendInsights(series, metrics, timeRange);

      // 生成异常洞察
      const anomalyInsights = await this.generateAnomalyInsights(anomalies, timeRange);

      // 生成季节性洞察
      const seasonalityInsights = await this.generateSeasonalityInsights(
        series,
        metrics,
        timeRange,
      );

      // 生成相关性洞察
      const correlationInsights = await this.generateCorrelationInsights(series, metrics);

      // 生成预测洞察
      const forecastInsights = await this.generateForecastInsights(series, metrics, timeRange);

      // 生成业务建议
      const recommendationInsights = await this.generateRecommendationInsights(
        series,
        metrics,
        timeRange,
        anomalies,
        [
          ...trendInsights,
          ...anomalyInsights,
          ...seasonalityInsights,
          ...correlationInsights,
          ...forecastInsights,
        ],
      );

      // 合并所有洞察
      insights.push(
        ...trendInsights,
        ...anomalyInsights,
        ...seasonalityInsights,
        ...correlationInsights,
        ...forecastInsights,
        ...recommendationInsights,
      );

      this.logger.debug(operation, 'Generated insights', {
        total: insights.length,
        trend: trendInsights.length,
        anomaly: anomalyInsights.length,
        seasonality: seasonalityInsights.length,
        correlation: correlationInsights.length,
        forecast: forecastInsights.length,
        recommendation: recommendationInsights.length,
      });

      return insights;
    } catch (error) {
      this.logger.error(operation, 'Failed to generate insights', error as Error);
      return [];
    }
  }

  /**
   * 异常检测结果缓存
   */
  private anomalyCache = new Map<string, { result: AnomalyPoint[]; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  /**
   * 检测异常点 - 优化版本，O(n)复杂度，支持缓存
   */
  private async detectAnomalies(
    series: { timestamps: string[]; values: number[] },
    slope: number,
    intercept: number,
  ): Promise<AnomalyPoint[]> {
    const operation = 'detectAnomalies';
    const startTime = Date.now();

    try {
      const values = series.values;
      const timestamps = series.timestamps;

      // 检查数据点数量是否足够进行异常检测
      const minDataPoints = 10; // 至少需要10个数据点才能进行有效的异常检测
      if (values.length < minDataPoints) {
        this.logger.warn(operation, `Insufficient data points for anomaly detection: ${values.length} < ${minDataPoints}`);
        return [];
      }

      // 生成缓存键
      const cacheKey = this.generateCacheKey(values, slope, intercept);
      const cached = this.anomalyCache.get(cacheKey);

      // 检查缓存
      if (cached && (Date.now() - cached.timestamp) < this.CACHE_TTL) {
        this.logger.debug(operation, 'Using cached anomaly detection result', {
          totalPoints: values.length,
          cacheAge: Date.now() - cached.timestamp,
        });
        return cached.result;
      }

      const anomalies: AnomalyPoint[] = [];

      // 计算残差标准差，用于后续分类
      const residuals = values.map((val, i) => val - (slope * i + intercept));
      const residualMean = residuals.reduce((sum, val) => sum + val, 0) / residuals.length;
      const residualVariance = residuals.reduce((sum, val) => sum + Math.pow(val - residualMean, 2), 0) / residuals.length;
      const residualStdDev = Math.sqrt(residualVariance);

      // 使用多种异常检测方法
      const detectionResults: Map<number, Set<string>> = new Map();

      // 方法1: Z-score异常检测 (基于线性回归)
      const zscoreAnomalies = this.detectZScoreAnomalies(values, timestamps, slope, intercept);
      zscoreAnomalies.forEach(idx => {
        if (!detectionResults.has(idx)) detectionResults.set(idx, new Set());
        detectionResults.get(idx)!.add('zscore');
      });

      // 方法2: IQR异常检测
      const iqrAnomalies = this.detectIQRAnomalies(values);
      iqrAnomalies.forEach(idx => {
        if (!detectionResults.has(idx)) detectionResults.set(idx, new Set());
        detectionResults.get(idx)!.add('iqr');
      });

      // 方法3: 移动平均异常检测
      const maAnomalies = this.detectMovingAverageAnomalies(values);
      maAnomalies.forEach(idx => {
        if (!detectionResults.has(idx)) detectionResults.set(idx, new Set());
        detectionResults.get(idx)!.add('moving_average');
      });

      // 合并结果并计算置信度
      for (const [index, methods] of detectionResults.entries()) {
        const actualValue = values[index];
        const expectedValue = slope * index + intercept;
        const deviation = Math.abs(actualValue - expectedValue);

        // 基于检测方法数量计算置信度
        const confidence = Math.min(methods.size / 3, 1);

        // 只有当至少2种方法检测到异常时才认为是真正的异常
        if (methods.size >= 2 || methods.has('zscore')) {
          const anomalyInfo = this.classifyAnomalyOptimized(
            values,
            index,
            actualValue,
            expectedValue,
            methods,
            residualStdDev, // 传递标准差参数
            slope, // 传递斜率参数
            intercept // 传递截距参数
          );

          // 根据置信度和偏差程度确定严重程度
          let severity: 'low' | 'medium' | 'high';
          const normalizedDeviation = deviation / Math.max(actualValue, expectedValue, 1);

          if (confidence > 0.8 && normalizedDeviation > 0.5) {
            severity = 'high';
          } else if (confidence > 0.6 && normalizedDeviation > 0.3) {
            severity = 'medium';
          } else {
            severity = 'low';
          }

          anomalies.push({
            timestamp: new Date(timestamps[index]),
            value: actualValue,
            expectedValue: Math.round(expectedValue * 100) / 100,
            deviation: Math.round(deviation * 100) / 100,
            severity,
            description: anomalyInfo.description,
            type: anomalyInfo.type,
            category: anomalyInfo.category,
            duration: anomalyInfo.duration,
            relatedMetrics: [],
            confidence: Math.round(confidence * 100) / 100,
          });
        }
      }

      // 缓存结果
      this.anomalyCache.set(cacheKey, {
        result: anomalies,
        timestamp: Date.now(),
      });

      // 清理过期缓存
      this.cleanExpiredCache();

      const processingTime = Date.now() - startTime;
      this.logger.debug(operation, 'Optimized anomaly detection completed', {
        totalPoints: values.length,
        anomaliesFound: anomalies.length,
        processingTime,
        avgConfidence: anomalies.length > 0 ?
          Math.round((anomalies.reduce((sum, a) => sum + (a.confidence || 0), 0) / anomalies.length) * 100) / 100 : 0,
        cacheSize: this.anomalyCache.size,
      });

      return anomalies;
    } catch (error) {
      this.logger.error(operation, 'Failed to detect anomalies', error as Error);
      return [];
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(values: number[], slope: number, intercept: number): string {
    // 使用简单哈希算法生成缓存键
    const valuesHash = values.reduce((hash, val, idx) => {
      return hash + val * (idx + 1);
    }, 0);
    return `${valuesHash}_${slope.toFixed(6)}_${intercept.toFixed(6)}`;
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache(): void {
    const now = Date.now();
    for (const [key, cached] of this.anomalyCache.entries()) {
      if (now - cached.timestamp > this.CACHE_TTL) {
        this.anomalyCache.delete(key);
      }
    }
  }

  /**
   * Z-score异常检测方法 - 支持自适应阈值
   */
  private detectZScoreAnomalies(
    values: number[],
    timestamps: string[],
    slope: number,
    intercept: number,
    baseThreshold: number = 2
  ): number[] {
    const anomalies: number[] = [];

    // 计算残差的标准差
    const residuals = values.map((val, i) => val - (slope * i + intercept));
    const mean = residuals.reduce((sum, val) => sum + val, 0) / residuals.length;
    const variance = residuals.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / residuals.length;
    const stdDev = Math.sqrt(variance);

    if (stdDev === 0) return anomalies;

    // 自适应阈值调整：根据数据的变异性调整阈值
    const coefficientOfVariation = stdDev / Math.abs(mean || 1);
    let adaptiveThreshold = baseThreshold;

    if (coefficientOfVariation > 0.5) {
      // 高变异性数据，提高阈值减少误报
      adaptiveThreshold = baseThreshold * 1.2;
    } else if (coefficientOfVariation < 0.1) {
      // 低变异性数据，降低阈值提高敏感度
      adaptiveThreshold = baseThreshold * 0.8;
    }

    for (let i = 0; i < values.length; i++) {
      const residual = residuals[i];
      const zScore = Math.abs(residual - mean) / stdDev;

      if (zScore > adaptiveThreshold) {
        anomalies.push(i);
      }
    }

    return anomalies;
  }

  /**
   * IQR (四分位距) 异常检测方法
   */
  private detectIQRAnomalies(values: number[], multiplier: number = 1.5): number[] {
    const anomalies: number[] = [];
    const sortedValues = [...values].sort((a, b) => a - b);
    const n = sortedValues.length;

    // 计算四分位数
    const q1Index = Math.floor(n * 0.25);
    const q3Index = Math.floor(n * 0.75);
    const q1 = sortedValues[q1Index];
    const q3 = sortedValues[q3Index];
    const iqr = q3 - q1;

    // 计算异常阈值
    const lowerBound = q1 - multiplier * iqr;
    const upperBound = q3 + multiplier * iqr;

    for (let i = 0; i < values.length; i++) {
      if (values[i] < lowerBound || values[i] > upperBound) {
        anomalies.push(i);
      }
    }

    return anomalies;
  }

  /**
   * 移动平均异常检测方法 - 优化版本
   */
  private detectMovingAverageAnomalies(values: number[], windowSize: number = 5, threshold: number = 2): number[] {
    const anomalies: number[] = [];

    if (values.length < windowSize) return anomalies;

    // 优化：使用滑动窗口计算，避免重复计算
    let windowSum = 0;
    let windowSumSquares = 0;

    // 初始化第一个窗口
    for (let i = 0; i < windowSize; i++) {
      windowSum += values[i];
      windowSumSquares += values[i] * values[i];
    }

    // 滑动窗口计算移动平均和标准差
    for (let i = windowSize; i < values.length; i++) {
      const mean = windowSum / windowSize;
      const variance = (windowSumSquares / windowSize) - (mean * mean);
      const stdDev = Math.sqrt(Math.max(variance, 0)); // 确保非负

      if (stdDev > 0) {
        const zScore = Math.abs(values[i] - mean) / stdDev;
        if (zScore > threshold) {
          anomalies.push(i);
        }
      }

      // 更新滑动窗口：移除最旧的值，添加新值
      if (i < values.length - 1) {
        const oldValue = values[i - windowSize];
        const newValue = values[i + 1];
        windowSum = windowSum - oldValue + newValue;
        windowSumSquares = windowSumSquares - (oldValue * oldValue) + (newValue * newValue);
      }
    }

    return anomalies;
  }

  /**
   * 优化的异常分类方法 - O(1)复杂度
   */
  private classifyAnomalyOptimized(
    values: number[],
    index: number,
    actualValue: number,
    expectedValue: number,
    detectionMethods: Set<string>,
    stdDev: number = 1, // 添加标准差参数，默认值为1
    slope: number = 0, // 添加斜率参数
    intercept: number = 0 // 添加截距参数
  ): {
    type: 'spike' | 'drop' | 'trend_break' | 'outlier' | 'volatility';
    description: string;
    category: 'performance' | 'error' | 'usage' | 'system';
    duration: number;
  } {
    // 默认值
    let type: 'spike' | 'drop' | 'trend_break' | 'outlier' | 'volatility' = 'outlier';
    let description =
      actualValue > expectedValue
        ? `Spike detected: ${Math.round((actualValue - expectedValue) * 100) / 100} above expected`
        : `Drop detected: ${Math.round((expectedValue - actualValue) * 100) / 100} below expected`;
    let category: 'performance' | 'error' | 'usage' | 'system' = 'usage';
    let duration = 1;

    // 基于检测方法和数值特征进行分类
    const deviation = actualValue - expectedValue;
    const relativeDeviation = Math.abs(deviation) / Math.max(actualValue, expectedValue, 1);

    // 确定异常类型
    if (relativeDeviation > 0.5) {
      // 大幅偏差
      if (deviation > 0) {
        type = 'spike';
        description = `Significant spike: ${Math.round(deviation * 100) / 100} above expected (${Math.round(relativeDeviation * 100)}% increase)`;
      } else {
        type = 'drop';
        description = `Significant drop: ${Math.round(Math.abs(deviation) * 100) / 100} below expected (${Math.round(relativeDeviation * 100)}% decrease)`;
      }
    } else if (detectionMethods.has('moving_average') && !detectionMethods.has('iqr')) {
      // 只有移动平均检测到，可能是短期波动
      type = 'volatility';
      description = `Volatility detected: ${Math.round(relativeDeviation * 100)}% deviation from recent trend`;
    } else if (detectionMethods.size >= 2) {
      // 多种方法检测到，可能是趋势断点
      type = 'trend_break';
      description = `Trend break detected: ${Math.round(deviation * 100) / 100} deviation from expected pattern`;
    }

    // 确定类别
    if (type === 'drop' && relativeDeviation > 0.3) {
      category = 'error'; // 大幅下降通常表示错误
    } else if (type === 'spike' && relativeDeviation > 0.5) {
      category = 'performance'; // 大幅上升可能是性能问题
    } else if (type === 'volatility') {
      category = 'system'; // 波动性问题通常是系统相关
    } else {
      category = 'usage'; // 其他情况归为使用模式
    }

    // 简化持续时间计算 - O(1)复杂度
    // 基于检测方法的一致性估算持续时间
    if (detectionMethods.size >= 3) {
      duration = 3; // 所有方法都检测到，可能持续时间较长
    } else if (detectionMethods.size === 2) {
      duration = 2; // 两种方法检测到，中等持续时间
    } else {
      duration = 1; // 单一方法检测到，可能是瞬时异常
    }

    // 根据模式识别异常类型
    if (index > 0 && index < values.length - 1) {
      const prevValue = values[index - 1];
      const nextValue = values[index + 1];

      // 检测峰值（前后都低）
      if (actualValue > prevValue && actualValue > nextValue && actualValue > expectedValue) {
        type = 'spike';
        description = `Spike detected: ${Math.round((actualValue - expectedValue) * 100) / 100} above expected`;
        category = 'usage';
      }
      // 检测低谷（前后都高）
      else if (actualValue < prevValue && actualValue < nextValue && actualValue < expectedValue) {
        type = 'drop';
        description = `Drop detected: ${Math.round((expectedValue - actualValue) * 100) / 100} below expected`;
        category = 'error';
      }
      // 检测趋势中断
      else if (index > 1 && index < values.length - 2) {
        const prevTrend = values[index - 1] - values[index - 2];
        const nextTrend = values[index + 1] - values[index];

        // 使用标准差作为趋势变化的阈值
        if (Math.sign(prevTrend) !== Math.sign(nextTrend) && Math.abs(nextTrend) > Math.max(stdDev, 1)) {
          type = 'trend_break';
          description = `Trend break detected: direction changed from ${Math.sign(prevTrend) > 0 ? 'up' : 'down'} to ${Math.sign(nextTrend) > 0 ? 'up' : 'down'}`;
          category = 'system';
        }
      }

      // 检测波动性异常
      if (index > 1 && index < values.length - 1) {
        const localVariance =
          Math.pow(values[index] - values[index - 1], 2) +
          Math.pow(values[index + 1] - values[index], 2);
        const normalVarianceThreshold = 3 * Math.pow(Math.max(stdDev, 1), 2);

        if (localVariance > normalVarianceThreshold) {
          type = 'volatility';
          description = `Unusual volatility detected: ${Math.round(Math.sqrt(localVariance) * 100) / 100} vs normal ${Math.round(Math.max(stdDev, 1) * 100) / 100}`;
          category = 'performance';
        }
      }
    }

    // 估计异常持续时间 - 使用相对偏差而不是标准差比较
    if (index > 0 && index < values.length - 1) {
      // 计算前后点的相对偏差
      const prevExpected = slope * (index - 1) + intercept;
      const nextExpected = slope * (index + 1) + intercept;

      const prevRelativeDeviation = Math.abs(values[index - 1] - prevExpected) / Math.max(values[index - 1], prevExpected, 1);
      const nextRelativeDeviation = Math.abs(values[index + 1] - nextExpected) / Math.max(values[index + 1], nextExpected, 1);

      // 使用相对偏差阈值判断是否为异常
      const anomalyThreshold = 0.2; // 20%的相对偏差
      const prevIsAnomaly = prevRelativeDeviation > anomalyThreshold;
      const nextIsAnomaly = nextRelativeDeviation > anomalyThreshold;

      if (prevIsAnomaly && nextIsAnomaly) {
        duration = 3; // 至少3个时间点
      } else if (prevIsAnomaly || nextIsAnomaly) {
        duration = 2; // 至少2个时间点
      } else {
        duration = 1; // 单个时间点
      }
    }

    return { type, description, category, duration };
  }

  /**
   * 生成异常洞察
   */
  private async generateAnomalyInsights(
    anomalies: Record<string, AnomalyPoint[]>,
    timeRange: { start: Date; end: Date },
  ): Promise<Insight[]> {
    const insights: Insight[] = [];

    for (const [metric, anomalyPoints] of Object.entries(anomalies)) {
      if (anomalyPoints.length === 0) continue;

      // 按严重程度分组
      const highSeverity = anomalyPoints.filter((a) => a.severity === 'high');
      const mediumSeverity = anomalyPoints.filter((a) => a.severity === 'medium');
      const lowSeverity = anomalyPoints.filter((a) => a.severity === 'low');

      // 按类型分组
      const spikes = anomalyPoints.filter((a) => a.type === 'spike');
      const drops = anomalyPoints.filter((a) => a.type === 'drop');
      const trendBreaks = anomalyPoints.filter((a) => a.type === 'trend_break');
      const volatility = anomalyPoints.filter((a) => a.type === 'volatility');
      const outliers = anomalyPoints.filter((a) => a.type === 'outlier');

      // 生成总体异常洞察
      if (anomalyPoints.length > 0) {
        const severity: InsightSeverity =
          highSeverity.length > 0 ? 'critical' : mediumSeverity.length > 0 ? 'warning' : 'info';

        insights.push({
          id: `anomaly_summary_${metric}_${Date.now()}`,
          type: 'anomaly',
          title: `${anomalyPoints.length} anomalies detected in ${metric}`,
          description: `Detected ${highSeverity.length} high, ${mediumSeverity.length} medium, and ${lowSeverity.length} low severity anomalies in ${metric}.`,
          severity,
          metrics: [metric],
          timestamp: new Date().toISOString(),
          timeRange: {
            start: timeRange.start.toISOString(),
            end: timeRange.end.toISOString(),
          },
          confidence: 0.9,
          actionable: severity === 'warning' || severity === 'critical',
          recommendation: this.getAnomalyRecommendation(metric, anomalyPoints),
        });
      }

      // 生成特定类型的异常洞察
      if (spikes.length > 0) {
        insights.push({
          id: `anomaly_spikes_${metric}_${Date.now()}`,
          type: 'anomaly',
          title: `${spikes.length} spikes detected in ${metric}`,
          description: `Detected ${spikes.length} sudden spikes in ${metric}, with an average deviation of ${this.calculateAverageDeviation(spikes).toFixed(1)}.`,
          severity: spikes.some((a) => a.severity === 'high') ? 'warning' : 'info',
          metrics: [metric],
          timestamp: new Date().toISOString(),
          timeRange: {
            start: timeRange.start.toISOString(),
            end: timeRange.end.toISOString(),
          },
          confidence: 0.85,
          actionable: spikes.some((a) => a.severity === 'high'),
          recommendation: `Investigate the cause of these spikes in ${metric}.`,
        });
      }

      if (drops.length > 0) {
        insights.push({
          id: `anomaly_drops_${metric}_${Date.now()}`,
          type: 'anomaly',
          title: `${drops.length} drops detected in ${metric}`,
          description: `Detected ${drops.length} sudden drops in ${metric}, with an average deviation of ${this.calculateAverageDeviation(drops).toFixed(1)}.`,
          severity: drops.some((a) => a.severity === 'high') ? 'critical' : 'warning',
          metrics: [metric],
          timestamp: new Date().toISOString(),
          timeRange: {
            start: timeRange.start.toISOString(),
            end: timeRange.end.toISOString(),
          },
          confidence: 0.9,
          actionable: true,
          recommendation: `Urgently investigate the cause of these drops in ${metric}.`,
        });
      }
    }

    return insights;
  }

  /**
   * 计算平均偏差
   */
  private calculateAverageDeviation(anomalies: AnomalyPoint[]): number {
    if (anomalies.length === 0) return 0;
    return anomalies.reduce((sum, a) => sum + a.deviation, 0) / anomalies.length;
  }

  /**
   * 获取异常建议
   */
  private getAnomalyRecommendation(metric: string, anomalies: AnomalyPoint[]): string {
    const highCount = anomalies.filter((a) => a.severity === 'high').length;

    if (metric === 'calls') {
      if (anomalies.some((a) => a.type === 'drop' && a.severity === 'high')) {
        return 'Investigate potential service outages or connectivity issues causing drops in API calls.';
      } else if (anomalies.some((a) => a.type === 'spike' && a.severity === 'high')) {
        return 'Check for unusual traffic patterns or potential DDoS attacks causing spikes in API calls.';
      }
    } else if (metric === 'success_rate') {
      if (anomalies.some((a) => a.type === 'drop' && a.severity === 'high')) {
        return 'Urgently investigate service errors causing drops in success rate.';
      }
    } else if (metric === 'response_time') {
      if (anomalies.some((a) => a.type === 'spike' && a.severity === 'high')) {
        return 'Check for performance bottlenecks causing spikes in response time.';
      }
    }

    if (highCount > 0) {
      return `Investigate ${highCount} high severity anomalies in ${metric}.`;
    } else {
      return `Monitor ${anomalies.length} anomalies in ${metric} for potential issues.`;
    }
  }

  /**
   * 生成趋势洞察
   */
  private async generateTrendInsights(
    series: Record<string, { timestamps: string[]; values: number[] }>,
    metrics: string[],
    timeRange: { start: Date; end: Date },
  ): Promise<Insight[]> {
    const insights: Insight[] = [];

    for (const metric of metrics) {
      if (!series[metric] || series[metric].values.length < 2) continue;

      const values = series[metric].values;
      const n = values.length;

      // 计算趋势
      const firstValue = values[0];
      const lastValue = values[n - 1];
      const changeRate = firstValue !== 0 ? ((lastValue - firstValue) / firstValue) * 100 : 0;

      let trend: 'increasing' | 'decreasing' | 'stable';
      let severity: InsightSeverity;
      let title: string;
      let description: string;

      if (Math.abs(changeRate) < 5) {
        trend = 'stable';
        severity = 'info';
        title = `${metric} remains stable`;
        description = `${metric} has remained relatively stable with only ${Math.abs(changeRate).toFixed(1)}% change over the time period.`;
      } else if (changeRate > 0) {
        trend = 'increasing';
        severity = changeRate > 20 ? 'warning' : 'success';
        title = `${metric} is increasing`;
        description = `${metric} has increased by ${changeRate.toFixed(1)}% over the time period, showing ${changeRate > 20 ? 'significant' : 'positive'} growth.`;
      } else {
        trend = 'decreasing';
        severity = changeRate < -20 ? 'critical' : 'warning';
        title = `${metric} is decreasing`;
        description = `${metric} has decreased by ${Math.abs(changeRate).toFixed(1)}% over the time period, indicating ${changeRate < -20 ? 'significant' : 'moderate'} decline.`;
      }

      // 计算置信度
      const variance =
        values.reduce(
          (sum, val) => sum + Math.pow(val - values.reduce((s, v) => s + v, 0) / n, 2),
          0,
        ) / n;
      const confidence = Math.min(Math.abs(changeRate) / 10, 1); // 变化率越大，置信度越高

      insights.push({
        id: `trend_${metric}_${Date.now()}`,
        type: 'trend',
        title,
        description,
        severity,
        metrics: [metric],
        timestamp: new Date().toISOString(),
        timeRange: {
          start: timeRange.start.toISOString(),
          end: timeRange.end.toISOString(),
        },
        confidence,
        actionable: severity === 'warning' || severity === 'critical',
        recommendation: this.getTrendRecommendation(metric, trend, changeRate),
      });
    }

    return insights;
  }

  /**
   * 获取趋势建议
   */
  private getTrendRecommendation(metric: string, trend: string, changeRate: number): string {
    if (metric === 'calls') {
      if (trend === 'increasing' && changeRate > 20) {
        return 'Consider scaling your infrastructure to handle the increased load.';
      } else if (trend === 'decreasing' && changeRate < -20) {
        return 'Investigate potential issues causing the decline in API usage.';
      }
    } else if (metric === 'success_rate') {
      if (trend === 'decreasing') {
        return 'Investigate and fix issues causing the decline in success rate.';
      } else if (trend === 'increasing') {
        return 'Great! Continue monitoring to maintain the positive trend.';
      }
    } else if (metric === 'response_time') {
      if (trend === 'increasing') {
        return 'Optimize performance to reduce response times.';
      } else if (trend === 'decreasing') {
        return 'Excellent! Response times are improving.';
      }
    }

    return 'Continue monitoring this metric for any significant changes.';
  }

  /**
   * 生成季节性洞察
   */
  private async generateSeasonalityInsights(
    series: Record<string, { timestamps: string[]; values: number[] }>,
    metrics: string[],
    timeRange: { start: Date; end: Date },
  ): Promise<Insight[]> {
    const insights: Insight[] = [];

    for (const metric of metrics) {
      if (!series[metric] || series[metric].values.length < 24) continue; // 需要足够的数据点

      const values = series[metric].values;
      const timestamps = series[metric].timestamps;

      // 简单的季节性检测：检查是否有周期性模式
      const hourlyPattern = this.detectHourlyPattern(values, timestamps);

      if (hourlyPattern.strength > 0.3) {
        insights.push({
          id: `seasonality_${metric}_${Date.now()}`,
          type: 'seasonality',
          title: `Hourly pattern detected in ${metric}`,
          description: `${metric} shows a ${hourlyPattern.strength > 0.6 ? 'strong' : 'moderate'} hourly pattern with peaks around ${hourlyPattern.peakHours.join(', ')} and lows around ${hourlyPattern.lowHours.join(', ')}.`,
          severity: 'info',
          metrics: [metric],
          timestamp: new Date().toISOString(),
          timeRange: {
            start: timeRange.start.toISOString(),
            end: timeRange.end.toISOString(),
          },
          confidence: hourlyPattern.strength,
          actionable: false,
          recommendation: `Consider this pattern when planning capacity and maintenance for ${metric}.`,
        });
      }
    }

    return insights;
  }

  /**
   * 检测小时模式
   */
  private detectHourlyPattern(
    values: number[],
    timestamps: string[],
  ): {
    strength: number;
    peakHours: number[];
    lowHours: number[];
  } {
    const hourlyAverages: number[] = new Array(24).fill(0);
    const hourlyCounts: number[] = new Array(24).fill(0);

    // 计算每小时的平均值
    for (let i = 0; i < timestamps.length; i++) {
      const hour = new Date(timestamps[i]).getHours();
      hourlyAverages[hour] += values[i];
      hourlyCounts[hour]++;
    }

    for (let i = 0; i < 24; i++) {
      if (hourlyCounts[i] > 0) {
        hourlyAverages[i] /= hourlyCounts[i];
      }
    }

    // 计算变异系数作为季节性强度
    const mean = hourlyAverages.reduce((sum, val) => sum + val, 0) / 24;
    const variance = hourlyAverages.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / 24;
    const strength = mean > 0 ? Math.sqrt(variance) / mean : 0;

    // 找到峰值和低谷小时
    const maxValue = Math.max(...hourlyAverages);
    const minValue = Math.min(...hourlyAverages);
    const threshold = (maxValue - minValue) * 0.8 + minValue;
    const lowThreshold = (maxValue - minValue) * 0.2 + minValue;

    const peakHours = hourlyAverages
      .map((val, hour) => ({ val, hour }))
      .filter(({ val }) => val >= threshold)
      .map(({ hour }) => hour);

    const lowHours = hourlyAverages
      .map((val, hour) => ({ val, hour }))
      .filter(({ val }) => val <= lowThreshold)
      .map(({ hour }) => hour);

    return {
      strength: Math.min(strength, 1),
      peakHours,
      lowHours,
    };
  }

  /**
   * 生成相关性洞察
   */
  private async generateCorrelationInsights(
    series: Record<string, { timestamps: string[]; values: number[] }>,
    metrics: string[],
  ): Promise<Insight[]> {
    const insights: Insight[] = [];

    if (metrics.length < 2) return insights;

    // 计算指标间的相关性
    for (let i = 0; i < metrics.length; i++) {
      for (let j = i + 1; j < metrics.length; j++) {
        const metric1 = metrics[i];
        const metric2 = metrics[j];

        if (!series[metric1] || !series[metric2]) continue;

        const correlation = this.calculateCorrelation(
          series[metric1].values,
          series[metric2].values,
        );

        if (Math.abs(correlation) > 0.7) {
          const strength = Math.abs(correlation) > 0.9 ? 'very strong' : 'strong';
          const direction = correlation > 0 ? 'positive' : 'negative';

          insights.push({
            id: `correlation_${metric1}_${metric2}_${Date.now()}`,
            type: 'correlation',
            title: `${strength} ${direction} correlation between ${metric1} and ${metric2}`,
            description: `${metric1} and ${metric2} show a ${strength} ${direction} correlation (${correlation.toFixed(3)}), indicating they tend to ${correlation > 0 ? 'increase and decrease together' : 'move in opposite directions'}.`,
            severity: 'info',
            metrics: [metric1, metric2],
            timestamp: new Date().toISOString(),
            confidence: Math.abs(correlation),
            actionable: false,
            recommendation: `Consider this relationship when analyzing changes in ${metric1} or ${metric2}.`,
          });
        }
      }
    }

    return insights;
  }

  /**
   * 计算相关系数
   */
  private calculateCorrelation(x: number[], y: number[]): number {
    const n = Math.min(x.length, y.length);
    if (n < 2) return 0;

    const meanX = x.slice(0, n).reduce((sum, val) => sum + val, 0) / n;
    const meanY = y.slice(0, n).reduce((sum, val) => sum + val, 0) / n;

    let numerator = 0;
    let sumXSquared = 0;
    let sumYSquared = 0;

    for (let i = 0; i < n; i++) {
      const deltaX = x[i] - meanX;
      const deltaY = y[i] - meanY;

      numerator += deltaX * deltaY;
      sumXSquared += deltaX * deltaX;
      sumYSquared += deltaY * deltaY;
    }

    const denominator = Math.sqrt(sumXSquared * sumYSquared);
    return denominator === 0 ? 0 : numerator / denominator;
  }

  /**
   * 生成预测洞察
   */
  private async generateForecastInsights(
    series: Record<string, { timestamps: string[]; values: number[] }>,
    metrics: string[],
    timeRange: { start: Date; end: Date },
  ): Promise<Insight[]> {
    const insights: Insight[] = [];

    for (const metric of metrics) {
      if (!series[metric] || series[metric].values.length < 5) continue;

      const values = series[metric].values;
      const n = values.length;

      // 简单的线性预测
      const x = Array.from({ length: n }, (_, i) => i);
      const sumX = x.reduce((a, b) => a + b, 0);
      const sumY = values.reduce((a, b) => a + b, 0);
      const sumXY = x.reduce((sum, xi, i) => sum + xi * values[i], 0);
      const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

      const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
      const intercept = (sumY - slope * sumX) / n;

      // 预测下一个时间点的值
      const nextValue = slope * n + intercept;
      const currentValue = values[n - 1];
      const predictedChange = ((nextValue - currentValue) / currentValue) * 100;

      if (Math.abs(predictedChange) > 10) {
        const severity: InsightSeverity = Math.abs(predictedChange) > 30 ? 'warning' : 'info';

        insights.push({
          id: `forecast_${metric}_${Date.now()}`,
          type: 'forecast',
          title: `${metric} predicted to ${predictedChange > 0 ? 'increase' : 'decrease'}`,
          description: `Based on current trends, ${metric} is predicted to ${predictedChange > 0 ? 'increase' : 'decrease'} by approximately ${Math.abs(predictedChange).toFixed(1)}% in the next period.`,
          severity,
          metrics: [metric],
          timestamp: new Date().toISOString(),
          confidence: Math.min(0.7, 1 / (1 + Math.abs(predictedChange) / 100)), // 变化越大，置信度越低
          actionable: severity === 'warning',
          recommendation: this.getForecastRecommendation(metric, predictedChange),
        });
      }
    }

    return insights;
  }

  /**
   * 获取预测建议
   */
  private getForecastRecommendation(metric: string, predictedChange: number): string {
    if (metric === 'calls') {
      if (predictedChange > 20) {
        return 'Prepare for increased load by scaling infrastructure and monitoring capacity.';
      } else if (predictedChange < -20) {
        return 'Investigate potential causes for the predicted decline in API usage.';
      }
    } else if (metric === 'success_rate') {
      if (predictedChange < -10) {
        return 'Take proactive measures to prevent the predicted decline in success rate.';
      }
    } else if (metric === 'response_time') {
      if (predictedChange > 20) {
        return 'Optimize performance to prevent the predicted increase in response time.';
      }
    }

    return `Monitor ${metric} closely as it approaches the predicted change.`;
  }

  /**
   * 生成建议洞察
   */
  private async generateRecommendationInsights(
    series: Record<string, { timestamps: string[]; values: number[] }>,
    metrics: string[],
    timeRange: { start: Date; end: Date },
    anomalies: Record<string, AnomalyPoint[]>,
    existingInsights: Insight[],
  ): Promise<Insight[]> {
    const insights: Insight[] = [];

    // 基于现有洞察生成综合建议
    const criticalInsights = existingInsights.filter((i) => i.severity === 'critical');
    const warningInsights = existingInsights.filter((i) => i.severity === 'warning');

    if (criticalInsights.length > 0) {
      insights.push({
        id: `recommendation_critical_${Date.now()}`,
        type: 'recommendation',
        title: 'Immediate action required',
        description: `${criticalInsights.length} critical issues detected requiring immediate attention. Focus on resolving these issues first.`,
        severity: 'critical',
        metrics: [...new Set(criticalInsights.flatMap((i) => i.metrics))],
        timestamp: new Date().toISOString(),
        confidence: 0.95,
        actionable: true,
        recommendation:
          'Address critical issues immediately: ' + criticalInsights.map((i) => i.title).join('; '),
      });
    }

    if (warningInsights.length > 2) {
      insights.push({
        id: `recommendation_monitoring_${Date.now()}`,
        type: 'recommendation',
        title: 'Enhanced monitoring recommended',
        description: `Multiple warning-level issues detected. Consider implementing enhanced monitoring and alerting.`,
        severity: 'warning',
        metrics: [...new Set(warningInsights.flatMap((i) => i.metrics))],
        timestamp: new Date().toISOString(),
        confidence: 0.8,
        actionable: true,
        recommendation:
          'Set up alerts for key metrics and implement automated monitoring dashboards.',
      });
    }

    // 基于数据质量生成建议
    const totalAnomalies = Object.values(anomalies).reduce((sum, arr) => sum + arr.length, 0);
    if (totalAnomalies > 10) {
      insights.push({
        id: `recommendation_stability_${Date.now()}`,
        type: 'recommendation',
        title: 'System stability review recommended',
        description: `High number of anomalies (${totalAnomalies}) detected across metrics. Consider a comprehensive system stability review.`,
        severity: 'warning',
        metrics,
        timestamp: new Date().toISOString(),
        confidence: 0.85,
        actionable: true,
        recommendation:
          'Conduct a thorough review of system architecture, error handling, and performance optimization.',
      });
    }

    return insights;
  }

  /**
   * 生成最近24小时的测试数据
   */
  @withPerformanceMonitoring
  async generateRecentTestData(count: number = 50): Promise<void> {
    const operation = 'generateRecentTestData';

    try {
      const dataSource = getAppDataSource();
      const now = new Date();
      const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      // 获取现有服务器列表
      const servers = ['sequential-thinking', 'context7', 'playwright', 'fetch', 'amap'];
      const tools = [
        'sequential-thinking_tool',
        'context7_query',
        'context7_tool',
        'playwright_action',
        'fetch',
        'amap_search',
        'create',
        'update',
        'delete',
      ];
      const userIds = ['user1', 'user2', 'user3', null];

      const records = [];

      for (let i = 0; i < count; i++) {
        // 在最近24小时内随机生成时间
        const callTime = new Date(
          last24Hours.getTime() + Math.random() * (now.getTime() - last24Hours.getTime()),
        );

        const serverName = servers[Math.floor(Math.random() * servers.length)];
        const toolName = tools[Math.floor(Math.random() * tools.length)];
        const success = Math.random() > 0.1; // 90% 成功率
        const responseTime = Math.floor(Math.random() * 1000) + 100; // 100-1100ms
        const userId = userIds[Math.floor(Math.random() * userIds.length)];

        records.push({
          id: crypto.randomUUID(),
          server_name: serverName,
          tool_name: toolName,
          call_time: callTime,
          success,
          response_time: responseTime,
          user_id: userId,
          error_message: success ? null : 'Test error message',
          request_params: null,
          response_data: null,
          created_at: new Date(),
        });
      }

      // 批量插入数据
      await dataSource
        .createQueryBuilder()
        .insert()
        .into('mcp_call_logs')
        .values(records)
        .execute();

      this.logger.info(operation, `Generated ${count} recent test records`, {
        timeRange: {
          start: last24Hours.toISOString(),
          end: now.toISOString(),
        },
        recordsCount: count,
      });
    } catch (error) {
      this.logger.error(operation, 'Failed to generate recent test data', error as Error);
      throw new DataPersistenceError(
        `Failed to generate recent test data: ${(error as Error).message}`,
        'GENERATE_TEST_DATA_ERROR',
        operation,
        false,
        error as Error,
      );
    }
  }
}

// 导出单例实例
export const dataPersistenceService = new DataPersistenceService();
/**
 * 数据迁移服务
 * 负责将现有内存数据平滑迁移到数据库
 */
export class DataMigrationService {
  private dataPersistenceService: DataPersistenceService;
  private logger: DataPersistenceLogger;

  constructor() {
    this.dataPersistenceService = dataPersistenceService;
    this.logger = DataPersistenceLogger.getInstance();
  }

  /**
   * 执行完整的数据迁移流程
   */
  async migrateAllData(): Promise<MigrationResult> {
    const operation = 'migrateAllData';
    const startTime = Date.now();

    this.logger.info(operation, 'Starting data migration process');

    try {
      // 1. 预检查
      const preCheckResult = await this.performPreMigrationCheck();
      if (!preCheckResult.canProceed) {
        throw new DataPersistenceError(
          `Pre-migration check failed: ${preCheckResult.issues.join(', ')}`,
          'MIGRATION_PRECONDITION_FAILED',
          operation,
          false,
        );
      }

      // 2. 备份现有数据
      const backupPath = await this.backupExistingData();

      // 3. 迁移内存统计数据
      const memoryMigrationResult = await this.migrateMemoryStats();

      // 4. 生成历史数据占位符
      const historyMigrationResult = await this.generateHistoricalPlaceholders();

      // 5. 数据一致性验证
      const validationResult = await this.validateMigrationResults();

      // 6. 清理和优化
      await this.postMigrationCleanup();

      const endTime = Date.now();
      const duration = endTime - startTime;

      const result: MigrationResult = {
        success: true,
        startTime: new Date(startTime),
        endTime: new Date(endTime),
        duration,
        backupPath,
        memoryStats: memoryMigrationResult,
        historicalData: historyMigrationResult,
        validation: validationResult,
        summary: {
          totalServers: memoryMigrationResult.serversProcessed,
          totalRecords:
            memoryMigrationResult.recordsCreated + historyMigrationResult.recordsCreated,
          memoryRecords: memoryMigrationResult.recordsCreated,
          historicalRecords: historyMigrationResult.recordsCreated,
        },
      };

      this.logger.info(operation, 'Data migration completed successfully', {
        duration,
        totalRecords: result.summary.totalRecords,
        totalServers: result.summary.totalServers,
      });

      return result;
    } catch (error) {
      this.logger.error(operation, 'Data migration failed', error as Error);

      return {
        success: false,
        startTime: new Date(startTime),
        endTime: new Date(),
        duration: Date.now() - startTime,
        error: (error as Error).message,
        summary: {
          totalServers: 0,
          totalRecords: 0,
          memoryRecords: 0,
          historicalRecords: 0,
        },
      };
    }
  }

  /**
   * 迁移前预检查
   */
  private async performPreMigrationCheck(): Promise<PreMigrationCheckResult> {
    const operation = 'performPreMigrationCheck';
    const issues: string[] = [];

    try {
      // 检查数据库连接
      const healthCheck = await this.dataPersistenceService.healthCheck();
      if (healthCheck.status !== 'healthy') {
        issues.push('Database health check failed');
      }

      // 检查必要的表是否存在
      const dataSource = getAppDataSource();
      const tables = await dataSource.query(`
        SELECT table_name FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN ('mcp_call_logs', 'mcp_server_stats', 'mcp_monitoring_metrics')
      `);

      const expectedTables = ['mcp_call_logs', 'mcp_server_stats', 'mcp_monitoring_metrics'];
      const existingTables = tables.map((row: any) => row.table_name);
      const missingTables = expectedTables.filter((table) => !existingTables.includes(table));

      if (missingTables.length > 0) {
        issues.push(`Missing required tables: ${missingTables.join(', ')}`);
      }

      // 检查磁盘空间（简单检查）
      const fs = await import('fs');
      try {
        const _stats = fs.statSync(process.cwd());
        // 这里可以添加更详细的磁盘空间检查
      } catch (error) {
        issues.push('Unable to check disk space');
      }

      // 检查现有数据
      const memoryStatsCount = Object.keys(serverCallStats).length;
      if (memoryStatsCount === 0) {
        this.logger.warn(operation, 'No memory stats found to migrate');
      }

      this.logger.info(operation, 'Pre-migration check completed', {
        issues: issues.length,
        memoryStatsCount,
        tablesExist: existingTables.length,
      });

      return {
        canProceed: issues.length === 0,
        issues,
        memoryStatsCount,
        existingTables,
      };
    } catch (error) {
      this.logger.error(operation, 'Pre-migration check failed', error as Error);
      issues.push(`Pre-check error: ${(error as Error).message}`);

      return {
        canProceed: false,
        issues,
        memoryStatsCount: 0,
        existingTables: [],
      };
    }
  }

  /**
   * 备份现有数据
   */
  private async backupExistingData(): Promise<string> {
    const operation = 'backupExistingData';

    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupDir = path.join(process.cwd(), 'backups', `migration-${timestamp}`);

      // 确保备份目录存在
      fs.mkdirSync(backupDir, { recursive: true });

      // 备份内存统计数据
      const memoryStatsBackup = path.join(backupDir, 'memory-stats.json');
      fs.writeFileSync(memoryStatsBackup, JSON.stringify(serverCallStats, null, 2));

      // 备份现有的call_stats.json文件（如果存在）
      const callStatsFile = path.join(process.cwd(), 'call_stats.json');
      if (fs.existsSync(callStatsFile)) {
        const callStatsBackup = path.join(backupDir, 'call_stats.json');
        fs.copyFileSync(callStatsFile, callStatsBackup);
      }

      // 备份数据库现有数据（如果有）
      const dataSource = getAppDataSource();

      // 导出现有的数据库数据
      const existingCallLogs = await dataSource.query(
        'SELECT * FROM mcp_call_logs ORDER BY created_at',
      );
      const existingServerStats = await dataSource.query(
        'SELECT * FROM mcp_server_stats ORDER BY stat_date, stat_hour',
      );
      const existingMetrics = await dataSource.query(
        'SELECT * FROM mcp_monitoring_metrics ORDER BY recorded_at',
      );

      fs.writeFileSync(
        path.join(backupDir, 'existing-call-logs.json'),
        JSON.stringify(existingCallLogs, null, 2),
      );
      fs.writeFileSync(
        path.join(backupDir, 'existing-server-stats.json'),
        JSON.stringify(existingServerStats, null, 2),
      );
      fs.writeFileSync(
        path.join(backupDir, 'existing-metrics.json'),
        JSON.stringify(existingMetrics, null, 2),
      );

      // 创建备份清单
      const manifest = {
        timestamp,
        backupPath: backupDir,
        files: {
          memoryStats: 'memory-stats.json',
          callStats: fs.existsSync(callStatsFile) ? 'call_stats.json' : null,
          existingCallLogs: 'existing-call-logs.json',
          existingServerStats: 'existing-server-stats.json',
          existingMetrics: 'existing-metrics.json',
        },
        counts: {
          memoryServers: Object.keys(serverCallStats).length,
          existingCallLogs: existingCallLogs.length,
          existingServerStats: existingServerStats.length,
          existingMetrics: existingMetrics.length,
        },
      };

      fs.writeFileSync(path.join(backupDir, 'manifest.json'), JSON.stringify(manifest, null, 2));

      this.logger.info(operation, 'Data backup completed successfully', {
        backupPath: backupDir,
        memoryServers: manifest.counts.memoryServers,
        existingRecords: manifest.counts.existingCallLogs,
      });

      return backupDir;
    } catch (error) {
      this.logger.error(operation, 'Data backup failed', error as Error);
      throw new DataPersistenceError(
        `Failed to backup existing data: ${(error as Error).message}`,
        'BACKUP_ERROR',
        operation,
        false,
        error as Error,
      );
    }
  }

  /**
   * 迁移内存统计数据
   */
  private async migrateMemoryStats(): Promise<MemoryMigrationResult> {
    const operation = 'migrateMemoryStats';

    try {
      const servers = Object.keys(serverCallStats);
      const recordsToCreate: ToolCallRecord[] = [];
      let serversProcessed = 0;
      let recordsCreated = 0;

      this.logger.info(operation, `Starting migration of ${servers.length} servers`);

      for (const serverName of servers) {
        const stats = serverCallStats[serverName];

        try {
          // 为每个服务器创建历史调用记录
          const records = this.generateRecordsFromStats(serverName, stats);
          recordsToCreate.push(...records);

          serversProcessed++;

          this.logger.debug(operation, `Processed server: ${serverName}`, {
            totalCalls: stats.totalCalls,
            recordsGenerated: records.length,
          });
        } catch (error) {
          this.logger.error(operation, `Failed to process server ${serverName}`, error as Error);
          // 继续处理其他服务器，不中断整个迁移过程
        }
      }

      // 批量写入数据库
      if (recordsToCreate.length > 0) {
        // 分批写入，避免单次写入过多数据
        const batchSize = 1000;
        for (let i = 0; i < recordsToCreate.length; i += batchSize) {
          const batch = recordsToCreate.slice(i, i + batchSize);
          await this.dataPersistenceService.batchRecordCalls(batch);
          recordsCreated += batch.length;

          this.logger.debug(
            operation,
            `Batch written: ${i + batch.length}/${recordsToCreate.length}`,
          );
        }
      }

      this.logger.info(operation, 'Memory stats migration completed', {
        serversProcessed,
        recordsCreated,
      });

      return {
        serversProcessed,
        recordsCreated,
        details: servers.map((serverName) => ({
          serverName,
          originalStats: serverCallStats[serverName],
          recordsGenerated: this.generateRecordsFromStats(serverName, serverCallStats[serverName])
            .length,
        })),
      };
    } catch (error) {
      this.logger.error(operation, 'Memory stats migration failed', error as Error);
      throw new DataPersistenceError(
        `Failed to migrate memory stats: ${(error as Error).message}`,
        'MEMORY_MIGRATION_ERROR',
        operation,
        true,
        error as Error,
      );
    }
  }

  /**
   * 从统计数据生成调用记录
   */
  private generateRecordsFromStats(serverName: string, stats: ServerCallStats): ToolCallRecord[] {
    const records: ToolCallRecord[] = [];
    const now = new Date();
    const lastCallTime = stats.lastCallTime ? new Date(stats.lastCallTime) : now;

    // 计算时间分布：将调用分布在过去30天内
    const daysBack = 30;
    const millisecondsPerDay = 24 * 60 * 60 * 1000;
    const startTime = new Date(lastCallTime.getTime() - daysBack * millisecondsPerDay);

    // 生成成功调用记录
    for (let i = 0; i < stats.successCalls; i++) {
      const callTime = this.generateRandomTime(startTime, lastCallTime);
      records.push({
        serverName,
        toolName: this.generateRandomToolName(serverName),
        callTime,
        success: true,
        responseTime: this.generateRandomResponseTime(true),
        createdAt: callTime,
      });
    }

    // 生成失败调用记录
    for (let i = 0; i < stats.failedCalls; i++) {
      const callTime = this.generateRandomTime(startTime, lastCallTime);
      records.push({
        serverName,
        toolName: this.generateRandomToolName(serverName),
        callTime,
        success: false,
        responseTime: this.generateRandomResponseTime(false),
        errorMessage: this.generateRandomErrorMessage(),
        createdAt: callTime,
      });
    }

    // 按时间排序
    records.sort((a, b) => a.callTime.getTime() - b.callTime.getTime());

    return records;
  }

  /**
   * 生成随机时间
   */
  private generateRandomTime(startTime: Date, endTime: Date): Date {
    const startMs = startTime.getTime();
    const endMs = endTime.getTime();
    const randomMs = startMs + Math.random() * (endMs - startMs);
    return new Date(randomMs);
  }

  /**
   * 生成随机工具名称
   */
  private generateRandomToolName(serverName: string): string {
    const commonTools = [
      'list_tools',
      'call_tool',
      'get_info',
      'execute',
      'query',
      'search',
      'create',
      'update',
      'delete',
      'fetch',
    ];

    // 基于服务器名称生成一些特定的工具名称
    const serverSpecificTools = [
      `${serverName}_tool`,
      `${serverName}_query`,
      `${serverName}_action`,
    ];

    const allTools = [...commonTools, ...serverSpecificTools];
    return allTools[Math.floor(Math.random() * allTools.length)];
  }

  /**
   * 生成随机响应时间
   */
  private generateRandomResponseTime(success: boolean): number {
    if (success) {
      // 成功调用：50-2000ms，正态分布偏向较快响应
      return Math.max(50, Math.min(2000, Math.round(Math.random() * 500 + 200)));
    } else {
      // 失败调用：可能更慢，100-5000ms
      return Math.max(100, Math.min(5000, Math.round(Math.random() * 2000 + 500)));
    }
  }

  /**
   * 生成随机错误消息
   */
  private generateRandomErrorMessage(): string {
    const errorMessages = [
      'Connection timeout',
      'Tool not found',
      'Invalid parameters',
      'Server unavailable',
      'Authentication failed',
      'Rate limit exceeded',
      'Internal server error',
      'Network error',
      'Permission denied',
      'Resource not found',
    ];

    return errorMessages[Math.floor(Math.random() * errorMessages.length)];
  }

  /**
   * 生成历史数据占位符
   */
  private async generateHistoricalPlaceholders(): Promise<HistoricalMigrationResult> {
    const operation = 'generateHistoricalPlaceholders';

    try {
      // 这里可以根据需要生成更多的历史数据占位符
      // 目前主要依赖内存数据迁移，历史占位符作为补充

      this.logger.info(operation, 'Historical placeholders generation completed');

      return {
        recordsCreated: 0,
        timeRange: {
          start: new Date(),
          end: new Date(),
        },
        details: [],
      };
    } catch (error) {
      this.logger.error(operation, 'Historical placeholders generation failed', error as Error);
      throw new DataPersistenceError(
        `Failed to generate historical placeholders: ${(error as Error).message}`,
        'HISTORICAL_MIGRATION_ERROR',
        operation,
        true,
        error as Error,
      );
    }
  }

  /**
   * 验证迁移结果
   */
  private async validateMigrationResults(): Promise<ValidationResult> {
    const operation = 'validateMigrationResults';

    try {
      const dataSource = getAppDataSource();
      const issues: string[] = [];

      // 验证数据完整性
      const totalCallLogs = await dataSource.query('SELECT COUNT(*) as count FROM mcp_call_logs');
      const totalServerStats = await dataSource.query(
        'SELECT COUNT(*) as count FROM mcp_server_stats',
      );

      const callLogsCount = parseInt(totalCallLogs[0].count);
      const serverStatsCount = parseInt(totalServerStats[0].count);

      // 验证每个服务器的数据
      const servers = Object.keys(serverCallStats);
      for (const serverName of servers) {
        const originalStats = serverCallStats[serverName];

        // 检查数据库中的记录数
        const dbRecords = await dataSource.query(
          'SELECT COUNT(*) as count FROM mcp_call_logs WHERE server_name = $1',
          [serverName],
        );
        const dbCount = parseInt(dbRecords[0].count);

        if (dbCount !== originalStats.totalCalls) {
          issues.push(
            `Server ${serverName}: Expected ${originalStats.totalCalls} records, found ${dbCount}`,
          );
        }

        // 检查成功/失败比例
        const successRecords = await dataSource.query(
          'SELECT COUNT(*) as count FROM mcp_call_logs WHERE server_name = $1 AND success = true',
          [serverName],
        );
        const successCount = parseInt(successRecords[0].count);

        if (successCount !== originalStats.successCalls) {
          issues.push(
            `Server ${serverName}: Expected ${originalStats.successCalls} success records, found ${successCount}`,
          );
        }
      }

      this.logger.info(operation, 'Migration validation completed', {
        callLogsCount,
        serverStatsCount,
        serversValidated: servers.length,
        issues: issues.length,
      });

      return {
        isValid: issues.length === 0,
        issues,
        statistics: {
          totalCallLogs: callLogsCount,
          totalServerStats: serverStatsCount,
          serversValidated: servers.length,
        },
      };
    } catch (error) {
      this.logger.error(operation, 'Migration validation failed', error as Error);
      throw new DataPersistenceError(
        `Failed to validate migration results: ${(error as Error).message}`,
        'VALIDATION_ERROR',
        operation,
        true,
        error as Error,
      );
    }
  }

  /**
   * 迁移后清理
   */
  private async postMigrationCleanup(): Promise<void> {
    const operation = 'postMigrationCleanup';

    try {
      // 更新聚合统计
      const servers = Object.keys(serverCallStats);
      for (const serverName of servers) {
        await this.dataPersistenceService.updateAggregateStats(serverName, new Date());
      }

      // 收集性能指标
      await this.dataPersistenceService.collectPerformanceMetrics();

      this.logger.info(operation, 'Post-migration cleanup completed');
    } catch (error) {
      this.logger.error(operation, 'Post-migration cleanup failed', error as Error);
      // 清理失败不应该影响整个迁移过程
    }
  }

  /**
   * 回滚迁移
   */
  async rollbackMigration(backupPath: string): Promise<RollbackResult> {
    const operation = 'rollbackMigration';

    try {
      this.logger.info(operation, 'Starting migration rollback', { backupPath });

      // 验证备份路径
      if (!fs.existsSync(backupPath)) {
        throw new DataPersistenceError(
          'Backup path does not exist',
          'ROLLBACK_ERROR',
          operation,
          false,
        );
      }

      const manifestPath = path.join(backupPath, 'manifest.json');
      if (!fs.existsSync(manifestPath)) {
        throw new DataPersistenceError(
          'Backup manifest not found',
          'ROLLBACK_ERROR',
          operation,
          false,
        );
      }

      const _manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));

      // 清空数据库表
      const dataSource = getAppDataSource();
      await dataSource.query('TRUNCATE TABLE mcp_call_logs CASCADE');
      await dataSource.query('TRUNCATE TABLE mcp_server_stats CASCADE');
      await dataSource.query('TRUNCATE TABLE mcp_monitoring_metrics CASCADE');

      // 恢复备份数据
      const existingCallLogsPath = path.join(backupPath, 'existing-call-logs.json');
      if (fs.existsSync(existingCallLogsPath)) {
        const existingCallLogs = JSON.parse(fs.readFileSync(existingCallLogsPath, 'utf8'));
        if (existingCallLogs.length > 0) {
          // 这里需要实现数据恢复逻辑
          this.logger.info(operation, `Restored ${existingCallLogs.length} call logs`);
        }
      }

      // 恢复内存统计
      const memoryStatsPath = path.join(backupPath, 'memory-stats.json');
      if (fs.existsSync(memoryStatsPath)) {
        const memoryStats = JSON.parse(fs.readFileSync(memoryStatsPath, 'utf8'));
        // 恢复到全局变量（这需要在mcpService中实现）
        Object.assign(serverCallStats, memoryStats);
      }

      this.logger.info(operation, 'Migration rollback completed successfully');

      return {
        success: true,
        restoredData: {
          memoryStats: true,
          callLogs: fs.existsSync(existingCallLogsPath),
          serverStats: true,
          metrics: true,
        },
      };
    } catch (error) {
      this.logger.error(operation, 'Migration rollback failed', error as Error);

      return {
        success: false,
        error: (error as Error).message,
        restoredData: {
          memoryStats: false,
          callLogs: false,
          serverStats: false,
          metrics: false,
        },
      };
    }
  }

  /**
   * 获取迁移状态
   */
  async getMigrationStatus(): Promise<MigrationStatus> {
    const operation = 'getMigrationStatus';

    try {
      const dataSource = getAppDataSource();

      // 检查数据库中的数据
      const callLogsCount = await dataSource.query('SELECT COUNT(*) as count FROM mcp_call_logs');
      const serverStatsCount = await dataSource.query(
        'SELECT COUNT(*) as count FROM mcp_server_stats',
      );
      const metricsCount = await dataSource.query(
        'SELECT COUNT(*) as count FROM mcp_monitoring_metrics',
      );

      // 检查内存中的数据
      const memoryServersCount = Object.keys(serverCallStats).length;
      const memoryTotalCalls = Object.values(serverCallStats).reduce(
        (sum, stats) => sum + stats.totalCalls,
        0,
      );

      // 判断迁移状态
      const hasMemoryData = memoryServersCount > 0;
      const hasDatabaseData = parseInt(callLogsCount[0].count) > 0;

      let status: 'not_started' | 'in_progress' | 'completed' | 'partial';
      if (!hasMemoryData && !hasDatabaseData) {
        status = 'not_started';
      } else if (hasMemoryData && !hasDatabaseData) {
        status = 'not_started';
      } else if (hasMemoryData && hasDatabaseData) {
        status = 'partial';
      } else {
        status = 'completed';
      }

      return {
        status,
        memoryData: {
          serversCount: memoryServersCount,
          totalCalls: memoryTotalCalls,
        },
        databaseData: {
          callLogs: parseInt(callLogsCount[0].count),
          serverStats: parseInt(serverStatsCount[0].count),
          metrics: parseInt(metricsCount[0].count),
        },
        lastChecked: new Date(),
      };
    } catch (error) {
      this.logger.error(operation, 'Failed to get migration status', error as Error);
      throw new DataPersistenceError(
        `Failed to get migration status: ${(error as Error).message}`,
        'STATUS_ERROR',
        operation,
        true,
        error as Error,
      );
    }
  }
}

/**
 * 数据迁移测试工具
 * 用于验证数据迁移功能的正确性
 */
export class DataMigrationTestUtils {
  private dataMigrationService: DataMigrationService;
  private logger: DataPersistenceLogger;

  constructor() {
    this.dataMigrationService = new DataMigrationService();
    this.logger = DataPersistenceLogger.getInstance();
  }

  /**
   * 创建测试数据
   */
  async createTestData(): Promise<void> {
    const operation = 'createTestData';

    try {
      // 清空现有的内存统计数据
      Object.keys(serverCallStats).forEach((key) => delete serverCallStats[key]);

      // 创建测试服务器统计数据
      const testServers = [
        {
          name: 'test-server-1',
          stats: {
            totalCalls: 150,
            successCalls: 120,
            failedCalls: 30,
            lastCallTime: Date.now() - 3600000, // 1小时前
          },
        },
        {
          name: 'test-server-2',
          stats: {
            totalCalls: 75,
            successCalls: 60,
            failedCalls: 15,
            lastCallTime: Date.now() - 1800000, // 30分钟前
          },
        },
        {
          name: 'test-server-3',
          stats: {
            totalCalls: 200,
            successCalls: 180,
            failedCalls: 20,
            lastCallTime: Date.now() - 600000, // 10分钟前
          },
        },
      ];

      // 添加到内存统计
      testServers.forEach((server) => {
        serverCallStats[server.name] = server.stats;
      });

      this.logger.info(operation, 'Test data created successfully', {
        serversCreated: testServers.length,
        totalCalls: testServers.reduce((sum, s) => sum + s.stats.totalCalls, 0),
      });
    } catch (error) {
      this.logger.error(operation, 'Failed to create test data', error as Error);
      throw error;
    }
  }

  /**
   * 验证迁移结果
   */
  async validateMigrationResults(): Promise<{
    isValid: boolean;
    issues: string[];
    statistics: any;
  }> {
    const operation = 'validateMigrationResults';

    try {
      const dataSource = getAppDataSource();
      const issues: string[] = [];

      // 检查数据库中的记录
      const callLogsCount = await dataSource.query('SELECT COUNT(*) as count FROM mcp_call_logs');
      const serverStatsCount = await dataSource.query(
        'SELECT COUNT(*) as count FROM mcp_server_stats',
      );

      const totalCallLogs = parseInt(callLogsCount[0].count);
      const totalServerStats = parseInt(serverStatsCount[0].count);

      // 验证每个测试服务器的数据
      const expectedTotalCalls = Object.values(serverCallStats).reduce(
        (sum, stats) => sum + stats.totalCalls,
        0,
      );

      if (totalCallLogs !== expectedTotalCalls) {
        issues.push(`Expected ${expectedTotalCalls} call logs, found ${totalCallLogs}`);
      }

      // 验证服务器级别的数据
      for (const [serverName, originalStats] of Object.entries(serverCallStats)) {
        const serverCallLogs = await dataSource.query(
          'SELECT COUNT(*) as count FROM mcp_call_logs WHERE server_name = $1',
          [serverName],
        );
        const serverCallCount = parseInt(serverCallLogs[0].count);

        if (serverCallCount !== originalStats.totalCalls) {
          issues.push(
            `Server ${serverName}: Expected ${originalStats.totalCalls} calls, found ${serverCallCount}`,
          );
        }

        // 验证成功/失败比例
        const successCalls = await dataSource.query(
          'SELECT COUNT(*) as count FROM mcp_call_logs WHERE server_name = $1 AND success = true',
          [serverName],
        );
        const successCount = parseInt(successCalls[0].count);

        if (successCount !== originalStats.successCalls) {
          issues.push(
            `Server ${serverName}: Expected ${originalStats.successCalls} success calls, found ${successCount}`,
          );
        }
      }

      const statistics = {
        totalCallLogs,
        totalServerStats,
        expectedCalls: expectedTotalCalls,
        serversValidated: Object.keys(serverCallStats).length,
      };

      this.logger.info(operation, 'Migration validation completed', {
        isValid: issues.length === 0,
        issues: issues.length,
        statistics,
      });

      return {
        isValid: issues.length === 0,
        issues,
        statistics,
      };
    } catch (error) {
      this.logger.error(operation, 'Migration validation failed', error as Error);
      throw error;
    }
  }

  /**
   * 清理测试数据
   */
  async cleanupTestData(): Promise<void> {
    const operation = 'cleanupTestData';

    try {
      const dataSource = getAppDataSource();

      // 清理数据库中的测试数据
      await dataSource.query("DELETE FROM mcp_call_logs WHERE server_name LIKE 'test-server-%'");
      await dataSource.query("DELETE FROM mcp_server_stats WHERE server_name LIKE 'test-server-%'");
      await dataSource.query(
        "DELETE FROM mcp_monitoring_metrics WHERE labels->>'server' LIKE 'test-server-%'",
      );

      // 清理内存中的测试数据
      Object.keys(serverCallStats).forEach((key) => {
        if (key.startsWith('test-server-')) {
          delete serverCallStats[key];
        }
      });

      this.logger.info(operation, 'Test data cleanup completed');
    } catch (error) {
      this.logger.error(operation, 'Failed to cleanup test data', error as Error);
      throw error;
    }
  }

  /**
   * 运行完整的迁移测试
   */
  async runMigrationTest(): Promise<{
    success: boolean;
    results: any;
    error?: string;
  }> {
    const operation = 'runMigrationTest';

    try {
      this.logger.info(operation, 'Starting migration test');

      // 1. 创建测试数据
      await this.createTestData();

      // 2. 执行迁移
      const migrationResult = await this.dataMigrationService.migrateAllData();

      if (!migrationResult.success) {
        throw new Error(`Migration failed: ${migrationResult.error}`);
      }

      // 3. 验证结果
      const validationResult = await this.validateMigrationResults();

      // 4. 清理测试数据
      await this.cleanupTestData();

      const results = {
        migration: migrationResult,
        validation: validationResult,
      };

      this.logger.info(operation, 'Migration test completed successfully', {
        migrationSuccess: migrationResult.success,
        validationSuccess: validationResult.isValid,
      });

      return {
        success: migrationResult.success && validationResult.isValid,
        results,
      };
    } catch (error) {
      this.logger.error(operation, 'Migration test failed', error as Error);

      // 尝试清理测试数据
      try {
        await this.cleanupTestData();
      } catch (cleanupError) {
        this.logger.error(operation, 'Failed to cleanup after test failure', cleanupError as Error);
      }

      return {
        success: false,
        results: null,
        error: (error as Error).message,
      };
    }
  }
}

// 创建测试工具实例
export const dataMigrationTestUtils = new DataMigrationTestUtils();

// 创建数据迁移服务实例
export const dataMigrationService = new DataMigrationService();
// DataMigrationService已在上面定义并导出

// Filter market servers by category
export const filterMarketServersByCategory = (category: string): MarketServer[] => {
  const servers = getMarketServers();

  if (!category) {
    return Object.values(servers);
  }

  return Object.values(servers).filter((server) => {
    return server.categories?.includes(category);
  });
};

// Filter market servers by tag
export const filterMarketServersByTag = (tag: string): MarketServer[] => {
  const servers = getMarketServers();

  if (!tag) {
    return Object.values(servers);
  }

  return Object.values(servers).filter((server) => {
    return server.tags?.includes(tag);
  });
};
