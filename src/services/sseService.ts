import { Request, Response } from 'express';
import { randomUUID } from 'node:crypto';
import { Transport } from '@modelcontextprotocol/sdk/shared/transport.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { isInitializeRequest } from '@modelcontextprotocol/sdk/types.js';
import { deleteMcpServer, getMcpServer } from './mcpService.js';
import { loadSettings } from '../config/index.js';
import config from '../config/index.js';

const transports: { [sessionId: string]: { transport: Transport; group: string; clientIp?: string } } = {};;

export const getGroup = (sessionId: string): string => {
  return transports[sessionId]?.group || '';
};
export const getClientIp = (sessionId: string): string => {
  const clientIp = transports[sessionId]?.clientIp || 'unknown';
  
  // 添加调试日志
  if (process.env.DEBUG_CLIENT_IP === 'true') {
    console.log(`[getClientIp] SessionId: ${sessionId}, ClientIP: ${clientIp}`);
    console.log(`[getClientIp] Available transports:`, Object.keys(transports));
    if (transports[sessionId]) {
      console.log(`[getClientIp] Transport data:`, {
        hasTransport: !!transports[sessionId].transport,
        group: transports[sessionId].group,
        clientIp: transports[sessionId].clientIp
      });
    }
  }
  
  return clientIp;
};

// Helper function to validate bearer auth
const validateBearerAuth = (req: Request, group?: string): boolean => {
  const settings = loadSettings();
  const routingConfig = settings.systemConfig?.routing || {
    enableGlobalRoute: true,
    enableGroupNameRoute: true,
    enableBearerAuth: false,
    bearerAuthKey: '',
  };

  if (routingConfig.enableBearerAuth) {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return false;
    }

    const token = authHeader.substring(7); // Remove "Bearer " prefix

    // If group is provided, check for group-level Bearer key first
    if (group) {
      // Import group service to get group information
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const { getGroupByIdOrName } = require('./groupService.js');
      const groupInfo = getGroupByIdOrName(group);

      if (groupInfo && groupInfo.bearerAuthKey) {
        // Group has its own Bearer key, use it for authentication
        console.log(`Using group-level Bearer authentication for group: ${group}`);
        return token === groupInfo.bearerAuthKey;
      }
      // If group doesn't have its own Bearer key, fall back to global key
      console.log(`Group ${group} has no Bearer key, falling back to global authentication`);
    }

    // Use global Bearer key for authentication
    return token === routingConfig.bearerAuthKey;
  }

  return true;
};

export const handleSseConnection = async (req: Request, res: Response): Promise<void> => {
  const group = req.params.group;

  // Check bearer auth
  if (!validateBearerAuth(req, group)) {
    res.status(401).send('Bearer authentication required or invalid token');
    return;
  }

  const settings = loadSettings();
  const routingConfig = settings.systemConfig?.routing || {
    enableGlobalRoute: true,
    enableGroupNameRoute: true,
    enableBearerAuth: false,
    bearerAuthKey: '',
  };

  // Check if this is a global route (no group) and if it's allowed
  if (!group && !routingConfig.enableGlobalRoute) {
    res.status(403).send('Global routes are disabled. Please specify a group ID.');
    return;
  }

  // 获取客户端IP信息
  const clientIp = req.clientInfo?.ip || 'unknown';

  const transport = new SSEServerTransport(`${config.basePath}/messages`, res);
  transports[transport.sessionId] = { transport, group: group, clientIp };

  res.on('close', () => {
    delete transports[transport.sessionId];
    deleteMcpServer(transport.sessionId);
    console.log(`SSE connection closed: ${transport.sessionId}`);
  });

  console.log(
    `New SSE connection established: ${transport.sessionId} with group: ${group || 'global'}`,
  );
  await getMcpServer(transport.sessionId, group).connect(transport);
};;

export const handleSseMessage = async (req: Request, res: Response): Promise<void> => {
  const sessionId = req.query.sessionId as string;

  // Validate sessionId
  if (!sessionId) {
    console.error('Missing sessionId in query parameters');
    res.status(400).send('Missing sessionId parameter');
    return;
  }

  // Check if transport exists before destructuring
  const transportData = transports[sessionId];
  if (!transportData) {
    console.warn(`No transport found for sessionId: ${sessionId}`);
    res.status(404).send('No transport found for sessionId');
    return;
  }

  const { transport, group } = transportData;

  // Check bearer auth with group context
  if (!validateBearerAuth(req, group)) {
    res.status(401).send('Bearer authentication required or invalid token');
    return;
  }
  req.params.group = group;
  req.query.group = group;
  console.log(`Received message for sessionId: ${sessionId} in group: ${group}`);

  await (transport as SSEServerTransport).handlePostMessage(req, res);
};

export const handleMcpPostRequest = async (req: Request, res: Response): Promise<void> => {
  const sessionId = req.headers['mcp-session-id'] as string | undefined;
  const group = req.params.group;
  const body = req.body;
  console.log(
    `Handling MCP post request for sessionId: ${sessionId} and group: ${group} with body: ${JSON.stringify(body)}`,
  );
  // Check bearer auth
  if (!validateBearerAuth(req, group)) {
    res.status(401).send('Bearer authentication required or invalid token');
    return;
  }

  const settings = loadSettings();
  const routingConfig = settings.systemConfig?.routing || {
    enableGlobalRoute: true,
    enableGroupNameRoute: true,
  };
  if (!group && !routingConfig.enableGlobalRoute) {
    res.status(403).send('Global routes are disabled. Please specify a group ID.');
    return;
  }

  // 获取客户端IP信息
  const clientIp = req.clientInfo?.ip || 'unknown';
  
  // 添加调试日志
  if (process.env.DEBUG_CLIENT_IP === 'true') {
    console.log(`[handleMcpPostRequest] SessionId: ${sessionId}, Group: ${group}, ClientIP: ${clientIp}`);
    console.log(`[handleMcpPostRequest] Request URL: ${req.protocol}://${req.get('host')}${req.originalUrl}`);
    console.log(`[handleMcpPostRequest] Request headers:`, {
      'x-forwarded-for': req.headers['x-forwarded-for'],
      'x-real-ip': req.headers['x-real-ip'],
      'x-client-ip': req.headers['x-client-ip'],
      'cf-connecting-ip': req.headers['cf-connecting-ip'],
      'host': req.headers['host'],
      'user-agent': req.headers['user-agent'],
      'mcp-session-id': req.headers['mcp-session-id']
    });
    console.log(`[handleMcpPostRequest] Connection info:`, {
      remoteAddress: req.connection?.remoteAddress,
      remoteFamily: req.connection?.remoteFamily,
      remotePort: req.connection?.remotePort,
      localAddress: req.connection?.localAddress,
      localPort: req.connection?.localPort
    });
  }

  let transport: StreamableHTTPServerTransport;
  if (sessionId && transports[sessionId]) {
    console.log(`Reusing existing transport for sessionId: ${sessionId}`);
    transport = transports[sessionId].transport as StreamableHTTPServerTransport;
    // 更新现有transport的客户端IP信息
    transports[sessionId].clientIp = clientIp;
  } else if (!sessionId && isInitializeRequest(req.body)) {
    transport = new StreamableHTTPServerTransport({
      sessionIdGenerator: () => randomUUID(),
      onsessioninitialized: (sessionId) => {
        transports[sessionId] = { transport, group, clientIp };

        // 添加调试日志
        if (process.env.DEBUG_CLIENT_IP === 'true') {
          console.log(`[onsessioninitialized] SessionId: ${sessionId}, Group: ${group}, ClientIP: ${clientIp}`);
        }

        // 在sessionId设置后连接MCP服务器
        getMcpServer(sessionId, group).connect(transport).then(() => {
          console.log(`MCP connection established: ${sessionId}`);
        }).catch((error) => {
          console.error(`Failed to connect MCP server for sessionId ${sessionId}:`, error);
        });
      },
    });

    transport.onclose = () => {
      console.log(`Transport closed: ${transport.sessionId}`);
      if (transport.sessionId) {
        delete transports[transport.sessionId];
        deleteMcpServer(transport.sessionId);
        console.log(`MCP connection closed: ${transport.sessionId}`);
      }
    };
  } else {
    res.status(400).json({
      jsonrpc: '2.0',
      error: {
        code: -32000,
        message: 'Bad Request: No valid session ID provided',
      },
      id: null,
    });
    return;
  }

  // 对于已存在的transport，正常处理请求
  console.log(`Handling request using transport with type ${transport.constructor.name}`);
  await transport.handleRequest(req, res, req.body);
};;

export const handleMcpOtherRequest = async (req: Request, res: Response) => {
  console.log('Handling MCP other request');

  const sessionId = req.headers['mcp-session-id'] as string | undefined;
  if (!sessionId || !transports[sessionId]) {
    res.status(400).send('Invalid or missing session ID');
    return;
  }

  const { transport, group } = transports[sessionId];

  // 更新客户端IP信息
  const clientIp = req.clientInfo?.ip || 'unknown';
  transports[sessionId].clientIp = clientIp;

  // Check bearer auth with group context
  if (!validateBearerAuth(req, group)) {
    res.status(401).send('Bearer authentication required or invalid token');
    return;
  }
  await (transport as StreamableHTTPServerTransport).handleRequest(req, res);
};;

export const getConnectionCount = (): number => {
  return Object.keys(transports).length;
};
