import 'reflect-metadata'; // Ensure reflect-metadata is imported here too
import { DataSource, DataSourceOptions } from 'typeorm';
import entities from './entities/index.js';
import { registerPostgresVectorType } from './types/postgresVectorType.js';
import { VectorEmbeddingSubscriber } from './subscribers/VectorEmbeddingSubscriber.js';
import { getSmartRoutingConfig } from '../utils/smartRouting.js';

// Helper function to create required PostgreSQL extensions
const createRequiredExtensions = async (dataSource: DataSource): Promise<void> => {
  try {
    await dataSource.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
    console.log('UUID extension created or already exists.');
  } catch (err: any) {
    console.warn('Failed to create uuid-ossp extension:', err.message);
    console.warn('UUID generation functionality may not be available.');
  }

  try {
    await dataSource.query('CREATE EXTENSION IF NOT EXISTS vector;');
    console.log('Vector extension created or already exists.');
  } catch (err: any) {
    console.warn('Failed to create vector extension:', err.message);
    console.warn('Vector functionality may not be available.');
  }
};

// Get database URL from smart routing config or fallback to environment variable
const getDatabaseUrl = (): string => {
  return getSmartRoutingConfig().dbUrl;
};

// Default database configuration
const defaultConfig: DataSourceOptions = {
  type: 'postgres',
  url: getDatabaseUrl(),
  synchronize: true,
  entities: entities,
  subscribers: [VectorEmbeddingSubscriber],
};

// AppDataSource is the TypeORM data source
let appDataSource = new DataSource(defaultConfig);

// Global promise to track initialization status
let initializationPromise: Promise<DataSource> | null = null;

// Function to create a new DataSource with updated configuration
export const updateDataSourceConfig = (): DataSource => {
  const newConfig: DataSourceOptions = {
    ...defaultConfig,
    url: getDatabaseUrl(),
  };

  // If the configuration has changed, we need to create a new DataSource
  const currentUrl = (appDataSource.options as any).url;
  if (currentUrl !== newConfig.url) {
    console.log('Database URL configuration changed, updating DataSource...');
    appDataSource = new DataSource(newConfig);
    // Reset initialization promise when configuration changes
    initializationPromise = null;
  }

  return appDataSource;
};

// Get the current AppDataSource instance
export const getAppDataSource = (): DataSource => {
  return appDataSource;
};

// Reconnect database with updated configuration
export const reconnectDatabase = async (): Promise<DataSource> => {
  try {
    // Close existing connection if it exists
    if (appDataSource.isInitialized) {
      console.log('Closing existing database connection...');
      await appDataSource.destroy();
    }

    // Reset initialization promise to allow fresh initialization
    initializationPromise = null;

    // Update configuration and reconnect
    appDataSource = updateDataSourceConfig();
    return await initializeDatabase();
  } catch (error) {
    console.error('Error during database reconnection:', error);
    throw error;
  }
};

// Initialize database connection with concurrency control
export const initializeDatabase = async (): Promise<DataSource> => {
  // If initialization is already in progress, wait for it to complete
  if (initializationPromise) {
    console.log('Database initialization already in progress, waiting for completion...');
    return initializationPromise;
  }

  // If already initialized, return the existing instance
  if (appDataSource.isInitialized) {
    console.log('Database already initialized, returning existing instance');
    return Promise.resolve(appDataSource);
  }

  // Create a new initialization promise
  initializationPromise = performDatabaseInitialization();

  try {
    const result = await initializationPromise;
    console.log('Database initialization completed successfully');
    return result;
  } catch (error) {
    // Reset the promise on error so initialization can be retried
    initializationPromise = null;
    console.error('Database initialization failed:', error);
    throw error;
  }
};

// Internal function to perform the actual database initialization
const performDatabaseInitialization = async (): Promise<DataSource> => {
  try {
    // Update configuration before initializing
    appDataSource = updateDataSourceConfig();

    if (!appDataSource.isInitialized) {
      console.log('Initializing database connection...');
      // Register the vector type with TypeORM
      await appDataSource.initialize();
      registerPostgresVectorType(appDataSource);

      // Create required PostgreSQL extensions
      await createRequiredExtensions(appDataSource);

      // Set up vector column and index with a more direct approach
      try {
        // Check if table exists first
        const tableExists = await appDataSource.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public'
            AND table_name = 'vector_embeddings'
          );
        `);

        if (tableExists[0].exists) {
          // Add pgvector support via raw SQL commands
          console.log('Configuring vector support for embeddings table...');

          // Step 1: Drop any existing index on the column
          try {
            await appDataSource.query(`DROP INDEX IF EXISTS idx_vector_embeddings_embedding;`);
          } catch (dropError: any) {
            console.warn('Note: Could not drop existing index:', dropError.message);
          }

          // Step 2: Alter column type to vector (if it's not already)
          try {
            // Check column type first
            const columnType = await appDataSource.query(`
              SELECT data_type FROM information_schema.columns
              WHERE table_schema = 'public' AND table_name = 'vector_embeddings'
              AND column_name = 'embedding';
            `);

            if (columnType.length > 0 && columnType[0].data_type !== 'vector') {
              await appDataSource.query(`
                ALTER TABLE vector_embeddings 
                ALTER COLUMN embedding TYPE vector USING embedding::vector;
              `);
              console.log('Vector embedding column type updated successfully.');
            }
          } catch (alterError: any) {
            console.warn('Could not alter embedding column type:', alterError.message);
            console.warn('Will try to recreate the table later.');
          }

          // Step 3: Try to create appropriate indices
          try {
            // First, let's check if there are any records to determine the dimensions
            const records = await appDataSource.query(`
              SELECT dimensions FROM vector_embeddings LIMIT 1;
            `);

            let dimensions = 1536; // Default to common OpenAI embedding size
            if (records && records.length > 0 && records[0].dimensions) {
              dimensions = records[0].dimensions;
              console.log(`Found vector dimension from existing data: ${dimensions}`);
            } else {
              console.log(`Using default vector dimension: ${dimensions} (no existing data found)`);
            }

            // Set the vector dimensions explicitly only if table has data
            if (records && records.length > 0) {
              await appDataSource.query(`
                ALTER TABLE vector_embeddings 
                ALTER COLUMN embedding TYPE vector(${dimensions});
              `);

              // Now try to create the index
              await appDataSource.query(`
                CREATE INDEX IF NOT EXISTS idx_vector_embeddings_embedding 
                ON vector_embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
              `);
              console.log('Created IVFFlat index for vector similarity search.');
            } else {
              console.log(
                'No existing vector data found, skipping index creation - will be handled by vector service.',
              );
            }
          } catch (indexError: any) {
            console.warn('IVFFlat index creation failed:', indexError.message);
            console.warn('Trying alternative index type...');

            try {
              // Try HNSW index instead
              await appDataSource.query(`
                CREATE INDEX IF NOT EXISTS idx_vector_embeddings_embedding 
                ON vector_embeddings USING hnsw (embedding vector_cosine_ops);
              `);
              console.log('Created HNSW index for vector similarity search.');
            } catch (hnswError: any) {
              // Final fallback to simpler index type
              console.warn('HNSW index creation failed too. Using simple L2 distance index.');

              try {
                // Create a basic GIN index as last resort
                await appDataSource.query(`
                  CREATE INDEX IF NOT EXISTS idx_vector_embeddings_embedding 
                  ON vector_embeddings USING gin (embedding);
                `);
                console.log('Created GIN index for basic vector lookups.');
              } catch (ginError: any) {
                console.warn('All index creation attempts failed:', ginError.message);
                console.warn('Vector search will be slower without an optimized index.');
              }
            }
          }
        } else {
          console.log(
            'Vector embeddings table does not exist yet - will configure after schema sync.',
          );
        }
      } catch (error: any) {
        console.warn('Could not set up vector column/index:', error.message);
        console.warn('Will attempt again after schema synchronization.');
      }

      console.log('Database connection established successfully.');

      // Run one final setup check after schema synchronization is done
      if (defaultConfig.synchronize) {
        try {
          console.log('Running final vector configuration check...');

          // Try setup again with the same code from above
          const tableExists = await appDataSource.query(`
              SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public'
                AND table_name = 'vector_embeddings'
              );
            `);

          if (tableExists[0].exists) {
            console.log('Vector embeddings table found, checking configuration...');

            // Get the dimension size first
            try {
              // Try to get dimensions from an existing record
              const records = await appDataSource.query(`
                  SELECT dimensions FROM vector_embeddings LIMIT 1;
                `);

              // Only proceed if we have existing data, otherwise let vector service handle it
              if (records && records.length > 0 && records[0].dimensions) {
                const dimensions = records[0].dimensions;
                console.log(`Found vector dimension from database: ${dimensions}`);

                // Ensure column type is vector with explicit dimensions
                await appDataSource.query(`
                    ALTER TABLE vector_embeddings 
                    ALTER COLUMN embedding TYPE vector(${dimensions});
                  `);
                console.log('Vector embedding column type updated in final check.');

                // One more attempt at creating the index with dimensions
                try {
                  // Drop existing index if any
                  await appDataSource.query(`
                      DROP INDEX IF EXISTS idx_vector_embeddings_embedding;
                    `);

                  // Create new index with proper dimensions
                  await appDataSource.query(`
                      CREATE INDEX idx_vector_embeddings_embedding 
                      ON vector_embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
                    `);
                  console.log('Created IVFFlat index in final check.');
                } catch (indexError: any) {
                  console.warn('Final index creation attempt did not succeed:', indexError.message);
                  console.warn('Using basic lookup without vector index.');
                }
              } else {
                console.log(
                  'No existing vector data found, vector dimensions will be configured by vector service.',
                );
              }
            } catch (setupError: any) {
              console.warn('Vector setup in final check failed:', setupError.message);
            }
          }
        } catch (error: any) {
          console.warn('Post-initialization vector setup failed:', error.message);
        }
      }
    }
    return appDataSource;
  } catch (error) {
    console.error('Error during database initialization:', error);
    throw error;
  }
};

// Get database connection status
export const isDatabaseConnected = (): boolean => {
  return appDataSource.isInitialized;
};

// Close database connection
export const closeDatabase = async (): Promise<void> => {
  if (appDataSource.isInitialized) {
    await appDataSource.destroy();
    console.log('Database connection closed.');
  }
};

// 创建MCP数据持久化表的迁移函数
export const createMcpPersistenceTables = async (): Promise<void> => {
  const dataSource = getAppDataSource();

  if (!dataSource.isInitialized) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }

  try {
    console.log('Creating MCP persistence tables...');

    // 创建调用日志表
    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS mcp_call_logs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        server_name VARCHAR(255) NOT NULL,
        tool_name VARCHAR(255),
        call_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        success BOOLEAN NOT NULL,
        response_time INTEGER,
        user_id VARCHAR(255),
        client_ip VARCHAR(45),
        error_message TEXT,
        request_params JSONB,
        response_data JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // 创建调用日志表的索引
    await dataSource.query(`
      CREATE INDEX IF NOT EXISTS idx_call_logs_server_time 
      ON mcp_call_logs(server_name, call_time DESC);
    `);

    await dataSource.query(`
      CREATE INDEX IF NOT EXISTS idx_call_logs_time 
      ON mcp_call_logs(call_time DESC);
    `);

    await dataSource.query(`
      CREATE INDEX IF NOT EXISTS idx_call_logs_success 
      ON mcp_call_logs(success, call_time DESC);
    `);

    await dataSource.query(`
      CREATE INDEX IF NOT EXISTS idx_call_logs_tool
      ON mcp_call_logs(tool_name, call_time DESC);
    `);

    await dataSource.query(`
      CREATE INDEX IF NOT EXISTS idx_call_logs_client_ip
      ON mcp_call_logs(client_ip, call_time DESC);
    `);

    // 为现有表添加client_ip字段（如果不存在）
    await dataSource.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'mcp_call_logs' AND column_name = 'client_ip'
        ) THEN
          ALTER TABLE mcp_call_logs ADD COLUMN client_ip VARCHAR(45);
          CREATE INDEX IF NOT EXISTS idx_call_logs_client_ip
          ON mcp_call_logs(client_ip, call_time DESC);
        END IF;
      END $$;
    `);

    // 创建服务器统计表
    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS mcp_server_stats (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        server_name VARCHAR(255) NOT NULL,
        stat_date DATE NOT NULL,
        stat_hour INTEGER,
        total_calls INTEGER DEFAULT 0,
        success_calls INTEGER DEFAULT 0,
        failed_calls INTEGER DEFAULT 0,
        avg_response_time DECIMAL(10,2),
        min_response_time INTEGER,
        max_response_time INTEGER,
        unique_users INTEGER DEFAULT 0,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(server_name, stat_date, stat_hour)
      );
    `);

    // 创建服务器统计表的索引
    await dataSource.query(`
      CREATE INDEX IF NOT EXISTS idx_server_stats_lookup 
      ON mcp_server_stats(server_name, stat_date, stat_hour);
    `);

    await dataSource.query(`
      CREATE INDEX IF NOT EXISTS idx_server_stats_date 
      ON mcp_server_stats(stat_date DESC);
    `);

    // 创建监控指标表
    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS mcp_monitoring_metrics (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        metric_name VARCHAR(255) NOT NULL,
        metric_value DECIMAL(15,4) NOT NULL,
        metric_type VARCHAR(50) NOT NULL,
        labels JSONB,
        recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // 创建监控指标表的索引
    await dataSource.query(`
      CREATE INDEX IF NOT EXISTS idx_monitoring_metrics_time 
      ON mcp_monitoring_metrics(recorded_at DESC);
    `);

    await dataSource.query(`
      CREATE INDEX IF NOT EXISTS idx_monitoring_metrics_name 
      ON mcp_monitoring_metrics(metric_name, recorded_at DESC);
    `);

    await dataSource.query(`
      CREATE INDEX IF NOT EXISTS idx_monitoring_metrics_type 
      ON mcp_monitoring_metrics(metric_type, recorded_at DESC);
    `);

    console.log('MCP persistence tables created successfully.');

    // 验证表创建
    const tables = await dataSource.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('mcp_call_logs', 'mcp_server_stats', 'mcp_monitoring_metrics')
      ORDER BY table_name;
    `);

    console.log(
      'Created tables:',
      tables.map((t: any) => t.table_name),
    );
  } catch (error) {
    console.error('Error creating MCP persistence tables:', error);
    throw error;
  }
};

// 检查MCP数据持久化表是否存在
export const checkMcpPersistenceTablesExist = async (): Promise<boolean> => {
  const dataSource = getAppDataSource();

  if (!dataSource.isInitialized) {
    return false;
  }

  try {
    const result = await dataSource.query(`
      SELECT COUNT(*) as count
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('mcp_call_logs', 'mcp_server_stats', 'mcp_monitoring_metrics');
    `);

    return parseInt(result[0].count) === 3;
  } catch (error) {
    console.error('Error checking MCP persistence tables:', error);
    return false;
  }
};
// 初始化MCP数据持久化表
export const initializeMcpPersistenceTables = async (): Promise<void> => {
  try {
    console.log('Initializing MCP persistence tables...');

    // 确保数据库已连接
    await initializeDatabase();

    // 检查表是否已存在
    const tablesExist = await checkMcpPersistenceTablesExist();

    if (!tablesExist) {
      console.log('MCP persistence tables not found, creating...');
      await createMcpPersistenceTables();
      console.log('MCP persistence tables created successfully.');
    } else {
      console.log('MCP persistence tables already exist.');
    }

    // 验证表创建
    const finalCheck = await checkMcpPersistenceTablesExist();
    if (finalCheck) {
      console.log('✅ MCP persistence tables initialization completed successfully.');
    } else {
      throw new Error('❌ MCP persistence tables initialization failed.');
    }
  } catch (error) {
    console.error('Error initializing MCP persistence tables:', error);
    throw error;
  }
};

// Export AppDataSource for backward compatibility
export const AppDataSource = appDataSource;

export default getAppDataSource;
