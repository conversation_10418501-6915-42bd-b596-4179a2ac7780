import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  Unique,
} from 'typeorm';

@Entity({ name: 'vector_embeddings' })
export class VectorEmbedding {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar' })
  content_type: string; // 'market_server', 'tool', 'documentation', etc.

  @Column({ type: 'varchar' })
  content_id: string; // Reference ID to the original content

  @Column('text')
  text_content: string; // The text that was embedded

  @Column('simple-json')
  metadata: Record<string, any>; // Additional metadata about the embedding

  @Column({
    type: 'float',
    array: true,
    nullable: true,
  })
  embedding: number[]; // The vector embedding - will be converted to vector type after table creation

  @Column({ type: 'int' })
  dimensions: number; // Dimensionality of the embedding vector

  @Column({ type: 'varchar' })
  model: string; // Model used to create the embedding

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;
}

// MCP调用日志实体 - 详细记录每次工具调用
@Entity({ name: 'mcp_call_logs' })
@Index('idx_call_logs_server_time', ['server_name', 'call_time'])
@Index('idx_call_logs_time', ['call_time'])
@Index('idx_call_logs_success', ['success', 'call_time'])
@Index('idx_call_logs_tool', ['tool_name', 'call_time'])
export class McpCallLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  server_name: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  tool_name: string | undefined;

  @Column({ type: 'timestamp with time zone', default: () => 'NOW()' })
  call_time: Date;

  @Column({ type: 'boolean' })
  success: boolean;

  @Column({ type: 'integer', nullable: true })
  response_time: number | undefined; // 响应时间，毫秒

  @Column({ type: 'varchar', length: 255, nullable: true })
  user_id: string | undefined;

  @Column({ type: 'varchar', length: 45, nullable: true })
  client_ip: string | undefined; // 客户端IP地址，支持IPv4和IPv6

  @Column({ type: 'text', nullable: true })
  error_message: string | undefined;

  @Column({ type: 'jsonb', nullable: true })
  request_params: Record<string, any> | undefined;

  @Column({ type: 'jsonb', nullable: true })
  response_data: Record<string, any> | undefined;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp with time zone' })
  created_at: Date;
}

// MCP服务器聚合统计实体 - 用于快速查询统计数据
@Entity({ name: 'mcp_server_stats' })
@Unique('unique_server_stat', ['server_name', 'stat_date', 'stat_hour'])
@Index('idx_server_stats_lookup', ['server_name', 'stat_date', 'stat_hour'])
@Index('idx_server_stats_date', ['stat_date'])
export class McpServerStats {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  server_name: string;

  @Column({ type: 'date' })
  stat_date: Date;

  @Column({ type: 'integer', nullable: true })
  stat_hour: number; // 0-23，NULL表示日级别统计

  @Column({ type: 'integer', default: 0 })
  total_calls: number;

  @Column({ type: 'integer', default: 0 })
  success_calls: number;

  @Column({ type: 'integer', default: 0 })
  failed_calls: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  avg_response_time: number;

  @Column({ type: 'integer', nullable: true })
  min_response_time: number;

  @Column({ type: 'integer', nullable: true })
  max_response_time: number;

  @Column({ type: 'integer', default: 0 })
  unique_users: number;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp with time zone' })
  updated_at: Date;
}

// MCP监控指标实体 - 系统监控数据
@Entity({ name: 'mcp_monitoring_metrics' })
@Index('idx_monitoring_metrics_time', ['recorded_at'])
@Index('idx_monitoring_metrics_name', ['metric_name', 'recorded_at'])
@Index('idx_monitoring_metrics_type', ['metric_type', 'recorded_at'])
export class McpMonitoringMetrics {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  metric_name: string;

  @Column({ type: 'decimal', precision: 15, scale: 4 })
  metric_value: number;

  @Column({ type: 'varchar', length: 50 })
  metric_type: string; // 'gauge', 'counter', 'histogram'

  @Column({ type: 'jsonb', nullable: true })
  labels: Record<string, any> | undefined;

  @Column({ type: 'timestamp with time zone', default: () => 'NOW()' })
  recorded_at: Date;
}

export default VectorEmbedding;
