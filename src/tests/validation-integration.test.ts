import request from 'supertest';
import app from '../index';

describe('数据验证集成测试', () => {
  let authToken: string;

  beforeAll(async () => {
    // 获取认证token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'admin',
        password: 'admin123'
      });
    
    authToken = loginResponse.body.token;
  });

  describe('历史数据查询验证', () => {
    test('应该拒绝开始时间晚于结束时间的请求', async () => {
      const response = await request(app)
        .get('/api/analytics/history')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          startTime: '2024-01-02T00:00:00.000Z',
          endTime: '2024-01-01T00:00:00.000Z'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Time range validation failed');
      expect(response.body.errors).toContainEqual(
        expect.objectContaining({
          field: 'timeRange',
          message: '开始时间不能晚于或等于结束时间'
        })
      );
    });

    test('应该拒绝无效的时间格式', async () => {
      const response = await request(app)
        .get('/api/analytics/history')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          startTime: 'invalid-date',
          endTime: '2024-01-01T00:00:00.000Z'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('应该拒绝超过1年的时间范围', async () => {
      const response = await request(app)
        .get('/api/analytics/history')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          startTime: '2023-01-01T00:00:00.000Z',
          endTime: '2025-01-01T00:00:00.000Z'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toContainEqual(
        expect.objectContaining({
          field: 'timeRange',
          message: '时间范围不能超过1年'
        })
      );
    });

    test('应该接受有效的查询参数', async () => {
      const response = await request(app)
        .get('/api/analytics/history')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          startTime: '2024-01-01T00:00:00.000Z',
          endTime: '2024-01-02T00:00:00.000Z',
          page: '1',
          pageSize: '20'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('应该清理和验证服务器名称', async () => {
      const response = await request(app)
        .get('/api/analytics/history')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          startTime: '2024-01-01T00:00:00.000Z',
          endTime: '2024-01-02T00:00:00.000Z',
          serverNames: '  server1  , server2 ,  '
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });

  describe('数据导出验证', () => {
    test('应该拒绝无效的导出格式', async () => {
      const response = await request(app)
        .get('/api/analytics/history/export')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          startTime: '2024-01-01T00:00:00.000Z',
          endTime: '2024-01-02T00:00:00.000Z',
          format: 'invalid'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('应该接受有效的导出请求', async () => {
      const response = await request(app)
        .get('/api/analytics/history/export')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          startTime: '2024-01-01T00:00:00.000Z',
          endTime: '2024-01-02T00:00:00.000Z',
          format: 'csv'
        });

      // 可能返回200（有数据）或404（无数据），都是有效的响应
      expect([200, 404]).toContain(response.status);
    });
  });

  describe('趋势分析验证', () => {
    test('应该拒绝无效的时间范围', async () => {
      const response = await request(app)
        .get('/api/analytics/trends')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          timeRange: 'invalid',
          granularity: 'hour',
          metrics: 'calls'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toContainEqual(
        expect.objectContaining({
          field: 'timeRange',
          message: '时间范围必须是以下值之一: 1h, 24h, 7d, 30d, custom'
        })
      );
    });

    test('应该要求自定义时间范围提供开始和结束时间', async () => {
      const response = await request(app)
        .get('/api/analytics/trends')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          timeRange: 'custom',
          granularity: 'hour',
          metrics: 'calls'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Custom time range requires startTime and endTime parameters');
    });

    test('应该接受有效的趋势分析请求', async () => {
      const response = await request(app)
        .get('/api/analytics/trends')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          timeRange: '24h',
          granularity: 'hour',
          metrics: 'calls,success_rate'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });
});
