import { DataPersistenceService } from '../services/marketService';

describe('TrendAnalysis', () => {
  let service: DataPersistenceService;

  beforeEach(() => {
    // 创建服务实例用于测试
    service = new DataPersistenceService();
  });

  describe('Linear Regression Calculation', () => {
    test('should calculate correct trend for increasing data', async () => {
      // 模拟递增数据：1, 2, 3, 4, 5
      const series = {
        calls: {
          timestamps: ['2025-01-01T00:00:00Z', '2025-01-01T01:00:00Z', '2025-01-01T02:00:00Z', '2025-01-01T03:00:00Z', '2025-01-01T04:00:00Z'],
          values: [1, 2, 3, 4, 5]
        }
      };

      // 使用反射访问私有方法进行测试
      const result = await (service as any).calculateTrendInsights(series, ['calls']);

      expect(result.analysis.calls.trend).toBe('increasing');
      expect(result.analysis.calls.changeRate).toBeGreaterThan(0);
      expect(result.analysis.calls.strength).toBeCloseTo(1, 2); // 完美线性关系，R²应该接近1
    });

    test('should calculate correct trend for decreasing data', async () => {
      // 模拟递减数据：5, 4, 3, 2, 1
      const series = {
        calls: {
          timestamps: ['2025-01-01T00:00:00Z', '2025-01-01T01:00:00Z', '2025-01-01T02:00:00Z', '2025-01-01T03:00:00Z', '2025-01-01T04:00:00Z'],
          values: [5, 4, 3, 2, 1]
        }
      };

      const result = await (service as any).calculateTrendInsights(series, ['calls']);

      expect(result.analysis.calls.trend).toBe('decreasing');
      expect(result.analysis.calls.changeRate).toBeLessThan(0);
      expect(result.analysis.calls.strength).toBeCloseTo(1, 2); // 完美线性关系，R²应该接近1
    });

    test('should calculate correct trend for stable data', async () => {
      // 模拟稳定数据：3, 3, 3, 3, 3
      const series = {
        calls: {
          timestamps: ['2025-01-01T00:00:00Z', '2025-01-01T01:00:00Z', '2025-01-01T02:00:00Z', '2025-01-01T03:00:00Z', '2025-01-01T04:00:00Z'],
          values: [3, 3, 3, 3, 3]
        }
      };

      const result = await (service as any).calculateTrendInsights(series, ['calls']);

      expect(result.analysis.calls.trend).toBe('stable');
      expect(Math.abs(result.analysis.calls.changeRate)).toBeLessThan(1); // 变化率应该接近0
    });

    test('should calculate correct trend for noisy but increasing data', async () => {
      // 模拟有噪声但总体递增的数据：1, 3, 2, 4, 5
      const series = {
        calls: {
          timestamps: ['2025-01-01T00:00:00Z', '2025-01-01T01:00:00Z', '2025-01-01T02:00:00Z', '2025-01-01T03:00:00Z', '2025-01-01T04:00:00Z'],
          values: [1, 3, 2, 4, 5]
        }
      };

      const result = await (service as any).calculateTrendInsights(series, ['calls']);

      expect(result.analysis.calls.trend).toBe('increasing');
      expect(result.analysis.calls.changeRate).toBeGreaterThan(0);
      expect(result.analysis.calls.strength).toBeLessThan(1); // 有噪声，R²应该小于1
      expect(result.analysis.calls.strength).toBeGreaterThan(0.5); // 但仍然有明显趋势
    });

    test('should handle edge cases', async () => {
      // 测试只有一个数据点的情况
      const singlePointSeries = {
        calls: {
          timestamps: ['2025-01-01T00:00:00Z'],
          values: [5]
        }
      };

      const result1 = await (service as any).calculateTrendInsights(singlePointSeries, ['calls']);
      expect(result1.analysis.calls.trend).toBe('stable');
      expect(result1.analysis.calls.changeRate).toBe(0);

      // 测试空数据的情况
      const emptySeries = {
        calls: {
          timestamps: [],
          values: []
        }
      };

      const result2 = await (service as any).calculateTrendInsights(emptySeries, ['calls']);
      expect(result2.analysis.calls.trend).toBe('stable');
      expect(result2.analysis.calls.changeRate).toBe(0);
    });
  });

  describe('Adaptive Threshold', () => {
    test('should use adaptive threshold based on R² value', async () => {
      // 创建两个数据集：一个有强趋势（高R²），一个有弱趋势（低R²）
      const strongTrendSeries = {
        calls: {
          timestamps: ['2025-01-01T00:00:00Z', '2025-01-01T01:00:00Z', '2025-01-01T02:00:00Z', '2025-01-01T03:00:00Z', '2025-01-01T04:00:00Z'],
          values: [100, 102, 104, 106, 108] // 2%的稳定增长
        }
      };

      const weakTrendSeries = {
        calls: {
          timestamps: ['2025-01-01T00:00:00Z', '2025-01-01T01:00:00Z', '2025-01-01T02:00:00Z', '2025-01-01T03:00:00Z', '2025-01-01T04:00:00Z'],
          values: [100, 105, 95, 110, 108] // 噪声很大的数据
        }
      };

      const strongResult = await (service as any).calculateTrendInsights(strongTrendSeries, ['calls']);
      const weakResult = await (service as any).calculateTrendInsights(weakTrendSeries, ['calls']);

      // 强趋势应该被识别为increasing，即使变化率较小
      expect(strongResult.analysis.calls.trend).toBe('increasing');
      expect(strongResult.analysis.calls.strength).toBeGreaterThan(0.8);

      // 弱趋势可能被识别为stable，因为噪声太大
      expect(weakResult.analysis.calls.strength).toBeLessThan(0.5);
    });
  });
});
