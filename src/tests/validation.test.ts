import {
  validateTimeRange,
  validatePaginationParams,
  validateServerNames,
  validateToolNames,
  validateHistoryQueryParamsDetailed,
  validateTrendAnalysisParams,
  sanitizeQueryParams,
} from '../types/index';

describe('数据验证逻辑测试', () => {
  describe('时间范围验证', () => {
    test('有效的时间范围', () => {
      const result = validateTimeRange('2024-01-01T00:00:00.000Z', '2024-01-02T00:00:00.000Z');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('开始时间晚于结束时间', () => {
      const result = validateTimeRange('2024-01-02T00:00:00.000Z', '2024-01-01T00:00:00.000Z');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'timeRange',
          message: '开始时间不能晚于或等于结束时间',
        })
      );
    });

    test('时间范围超过1年', () => {
      const result = validateTimeRange('2023-01-01T00:00:00.000Z', '2025-01-01T00:00:00.000Z');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'timeRange',
          message: '时间范围不能超过1年',
        })
      );
    });

    test('无效的时间格式', () => {
      const result = validateTimeRange('invalid-date', '2024-01-01T00:00:00.000Z');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'startTime',
          message: '开始时间格式无效，请使用ISO 8601格式',
        })
      );
    });
  });

  describe('分页参数验证', () => {
    test('有效的分页参数', () => {
      const result = validatePaginationParams(1, 20);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('无效的页码', () => {
      const result = validatePaginationParams(0, 20);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'page',
          message: '页码必须是大于0的整数',
        })
      );
    });

    test('页码超过限制', () => {
      const result = validatePaginationParams(20000, 20);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'page',
          message: '页码不能超过10000',
        })
      );
    });

    test('页大小超过限制', () => {
      const result = validatePaginationParams(1, 200000);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'pageSize',
          message: '页大小不能超过100000',
        })
      );
    });
  });

  describe('服务器名称验证', () => {
    test('有效的服务器名称', () => {
      const result = validateServerNames(['server1', 'server-2', 'server_3']);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('包含无效字符的服务器名称', () => {
      const result = validateServerNames(['server@1', 'server#2']);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'serverNames[0]',
          message: '服务器名称只能包含字母、数字、下划线、连字符和点',
        })
      );
    });

    test('空的服务器名称', () => {
      const result = validateServerNames(['', '  ']);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(2);
    });

    test('服务器名称过长', () => {
      const longName = 'a'.repeat(101);
      const result = validateServerNames([longName]);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'serverNames[0]',
          message: '服务器名称长度不能超过100个字符',
        })
      );
    });
  });

  describe('参数清理功能', () => {
    test('清理分页参数', () => {
      const params = {
        pagination: {
          page: '2.5',
          pageSize: '25.7',
        },
      };
      const sanitized = sanitizeQueryParams(params);
      expect(sanitized.pagination.page).toBe(2);
      expect(sanitized.pagination.pageSize).toBe(25);
    });

    test('清理服务器名称', () => {
      const params = {
        filters: {
          serverNames: ['  server1  ', '', 'server2', '  '],
        },
      };
      const sanitized = sanitizeQueryParams(params);
      expect(sanitized.filters.serverNames).toEqual(['server1', 'server2']);
    });

    test('清理成功状态', () => {
      const params = {
        filters: {
          success: 'true',
        },
      };
      const sanitized = sanitizeQueryParams(params);
      expect(sanitized.filters.success).toBe(true);
    });
  });

  describe('完整查询参数验证', () => {
    test('有效的查询参数', () => {
      const params = {
        timeRange: {
          startTime: '2024-01-01T00:00:00.000Z',
          endTime: '2024-01-02T00:00:00.000Z',
        },
        pagination: {
          page: 1,
          pageSize: 20,
        },
        filters: {
          serverNames: ['server1', 'server2'],
          success: true,
        },
      };
      const result = validateHistoryQueryParamsDetailed(params);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('缺少必需参数', () => {
      const params = {
        pagination: {
          page: 1,
          pageSize: 20,
        },
      };
      const result = validateHistoryQueryParamsDetailed(params);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'timeRange',
          message: '时间范围参数是必需的',
        })
      );
    });
  });

  describe('趋势分析参数验证', () => {
    test('有效的趋势分析参数', () => {
      const params = {
        timeRange: '24h',
        granularity: 'hour',
        metrics: ['calls', 'success_rate'],
      };
      const result = validateTrendAnalysisParams(params);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('无效的时间范围', () => {
      const params = {
        timeRange: 'invalid',
        granularity: 'hour',
        metrics: ['calls'],
      };
      const result = validateTrendAnalysisParams(params);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'timeRange',
          message: '时间范围必须是以下值之一: 1h, 24h, 7d, 30d, custom',
        })
      );
    });

    test('自定义时间范围缺少参数', () => {
      const params = {
        timeRange: 'custom',
        granularity: 'hour',
        metrics: ['calls'],
      };
      const result = validateTrendAnalysisParams(params);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'customTimeRange',
          message: '自定义时间范围需要提供startTime和endTime参数',
        })
      );
    });
  });
});
