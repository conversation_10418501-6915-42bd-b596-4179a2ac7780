import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { DataPersistenceService, DataMigrationTestUtils } from '../services/marketService.js';
import {
  HistoryQueryParams,
  TrendAnalysisParams,
  TrendAnalysisResponse,
  validateHistoryQueryParamsDetailed,
  validateTrendAnalysisParams,
  sanitizeQueryParams,
  validateTimeRange
} from '../types/index.js';

// 创建数据持久化服务实例
const dataPersistenceService = new DataPersistenceService();
// 创建数据迁移测试工具实例
const dataMigrationTestUtils = new DataMigrationTestUtils();

/**
 * 历史数据查询API端点
 * GET /api/analytics/history
 *
 * 查询参数:
 * - startTime: 开始时间 (ISO 8601格式)
 * - endTime: 结束时间 (ISO 8601格式)
 * - serverNames: 服务器名称列表 (可选，逗号分隔)
 * - toolNames: 工具名称列表 (可选，逗号分隔)
 * - clientIps: 客户端IP地址列表 (可选，逗号分隔)
 * - success: 成功状态筛选 (可选，true/false)
 * - page: 页码 (默认1)
 * - pageSize: 每页大小 (默认20，最大1000)
 * - groupBy: 聚合粒度 (可选，hour/day/week/month)
 * - metrics: 聚合指标 (可选，count,success_rate,avg_response_time)
 */
export const getHistoryData = async (req: Request, res: Response): Promise<void> => {
  try {
    // 基础参数验证（express-validator）
    const expressErrors = validationResult(req);
    if (!expressErrors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: 'Invalid request parameters',
        errors: expressErrors.array(),
      });
      return;
    }

    // 解析查询参数
    const {
      startTime,
      endTime,
      serverNames,
      toolNames,
      clientIps,
      success,
      page = '1',
      pageSize = '20',
      groupBy,
      metrics,
    } = req.query;

    // 额外的时间范围验证
    if (startTime && endTime) {
      const timeValidation = validateTimeRange(startTime as string, endTime as string);
      if (!timeValidation.isValid) {
        res.status(400).json({
          success: false,
          message: 'Time range validation failed',
          errors: timeValidation.errors,
        });
        return;
      }
    }

    // 构建查询参数
    let queryParams: any = {
      timeRange: {
        startTime: startTime as string,
        endTime: endTime as string,
      },
      pagination: {
        page: page as string,
        pageSize: pageSize as string,
      },
    };

    // 添加筛选条件
    if (serverNames || toolNames || clientIps || success !== undefined) {
      queryParams.filters = {};

      if (serverNames) {
        queryParams.filters.serverNames = (serverNames as string).split(',');
      }

      if (toolNames) {
        queryParams.filters.toolNames = (toolNames as string).split(',');
      }

      if (clientIps) {
        queryParams.filters.clientIps = (clientIps as string).split(',');
      }

      if (success !== undefined) {
        queryParams.filters.success = success;
      }
    }

    // 添加聚合配置
    if (groupBy && metrics) {
      queryParams.aggregation = {
        groupBy: groupBy as 'hour' | 'day' | 'week' | 'month',
        metrics: (metrics as string).split(','),
      };
    }

    // 清理和格式化参数
    queryParams = sanitizeQueryParams(queryParams);

    // 详细验证清理后的参数
    const detailedValidation = validateHistoryQueryParamsDetailed(queryParams);
    if (!detailedValidation.isValid) {
      res.status(400).json({
        success: false,
        message: 'Parameter validation failed',
        errors: detailedValidation.errors,
      });
      return;
    }

    // 转换为正确的类型
    const finalQueryParams: HistoryQueryParams = {
      timeRange: {
        startTime: queryParams.timeRange.startTime,
        endTime: queryParams.timeRange.endTime,
      },
      pagination: {
        page: queryParams.pagination.page,
        pageSize: queryParams.pagination.pageSize,
      },
      filters: queryParams.filters,
      aggregation: queryParams.aggregation,
    };

    // 执行查询
    const result: any = await dataPersistenceService.queryHistory(finalQueryParams);

    // 构建响应
    const response: any = {
      success: true,
      data: result.data,
      pagination: result.pagination,
      aggregates: result.aggregates,
      query: {
        timeRange: finalQueryParams.timeRange,
        filters: finalQueryParams.filters,
        aggregation: finalQueryParams.aggregation,
      },
    };

    // 如果是聚合查询，添加时间序列数据
    if (result.isAggregated && result.timeSeries) {
      response.timeSeries = result.timeSeries;
      response.isAggregated = true;
    }

    res.json(response);
  } catch (error) {
    console.error('Error in getHistoryData:', error);

    res.status(500).json({
      success: false,
      message: 'Failed to query history data',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
/**
 * 导出历史数据
 * GET /api/analytics/history/export
 *
 * 查询参数与getHistoryData相同，但返回文件流而不是JSON
 * 额外参数:
 * - format: 导出格式 (csv/excel)
 */
export const exportHistoryData = async (req: Request, res: Response): Promise<void> => {
  try {
    // 基础参数验证（express-validator）
    const expressErrors = validationResult(req);
    if (!expressErrors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: 'Invalid request parameters',
        errors: expressErrors.array(),
      });
      return;
    }

    // 解析查询参数
    const {
      startTime,
      endTime,
      serverNames,
      toolNames,
      clientIps,
      success,
      format = 'csv', // 默认CSV格式
    } = req.query;

    // 验证导出格式
    if (format !== 'csv' && format !== 'excel') {
      res.status(400).json({
        success: false,
        message: 'Invalid export format. Supported formats: csv, excel',
      });
      return;
    }

    // 额外的时间范围验证
    if (startTime && endTime) {
      const timeValidation = validateTimeRange(startTime as string, endTime as string);
      if (!timeValidation.isValid) {
        res.status(400).json({
          success: false,
          message: 'Time range validation failed',
          errors: timeValidation.errors,
        });
        return;
      }
    }

    // 构建查询参数 - 移除分页限制，获取所有数据
    let queryParams: any = {
      timeRange: {
        startTime: startTime as string,
        endTime: endTime as string,
      },
      pagination: {
        page: '1',
        pageSize: '50000', // 增加到50000条记录的限制，防止内存溢出
      },
    };

    // 添加筛选条件
    if (serverNames || toolNames || clientIps || success !== undefined) {
      queryParams.filters = {};

      if (serverNames) {
        queryParams.filters.serverNames = (serverNames as string).split(',');
      }

      if (toolNames) {
        queryParams.filters.toolNames = (toolNames as string).split(',');
      }

      if (clientIps) {
        queryParams.filters.clientIps = (clientIps as string).split(',');
      }

      if (success !== undefined) {
        queryParams.filters.success = success;
      }
    }

    // 清理和格式化参数
    queryParams = sanitizeQueryParams(queryParams);

    // 详细验证清理后的参数
    const detailedValidation = validateHistoryQueryParamsDetailed(queryParams);
    if (!detailedValidation.isValid) {
      res.status(400).json({
        success: false,
        message: 'Parameter validation failed',
        errors: detailedValidation.errors,
      });
      return;
    }

    // 转换为正确的类型
    const finalQueryParams: HistoryQueryParams = {
      timeRange: {
        startTime: queryParams.timeRange.startTime,
        endTime: queryParams.timeRange.endTime,
      },
      pagination: {
        page: queryParams.pagination.page,
        pageSize: queryParams.pagination.pageSize,
      },
      filters: queryParams.filters,
    };

    // 执行查询
    const result: any = await dataPersistenceService.queryHistory(finalQueryParams);

    if (!result.data || !Array.isArray(result.data)) {
      res.status(404).json({
        success: false,
        message: 'No data found for the specified criteria',
      });
      return;
    }

    const records = result.data;

    // 检查数据量，如果超过限制则警告
    if (records.length >= 50000) {
      console.warn(`Export data size reached limit: ${records.length} records`);
    }

    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const filename = `mcp_history_${timestamp}.${format === 'excel' ? 'csv' : format}`;

    if (format === 'csv') {
      // 生成CSV
      const csvContent = generateCSV(records);

      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Cache-Control', 'no-cache');

      // 添加BOM以支持Excel正确显示中文
      res.write('\uFEFF');
      res.end(csvContent);
    } else if (format === 'excel') {
      // 生成Excel (这里先用CSV格式，后续可以升级为真正的Excel)
      const csvContent = generateCSV(records);

      res.setHeader('Content-Type', 'application/vnd.ms-excel; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Cache-Control', 'no-cache');

      // 添加BOM以支持Excel正确显示中文
      res.write('\uFEFF');
      res.end(csvContent);
    }
  } catch (error) {
    console.error('Error in exportHistoryData:', error);

    res.status(500).json({
      success: false,
      message: 'Failed to export history data',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};;

/**
 * 生成CSV内容
 */
function generateCSV(records: any[]): string {
  if (!records || records.length === 0) {
    return 'No data available';
  }

  // CSV头部 - 使用中文标题
  const headers = [
    'ID',
    '服务器名称',
    '工具名称', 
    '调用时间',
    '成功状态',
    '响应时间(毫秒)',
    '用户ID',
    '错误信息',
    '创建时间',
  ];

  // 转换数据行 - 修正字段映射
  const rows = records.map((record) => [
    record.id || '',
    record.serverName || '', // 修正：使用serverName而不是server_name
    record.toolName || '',   // 修正：使用toolName而不是tool_name
    record.callTime ? new Date(record.callTime).toLocaleString('zh-CN') : '', // 使用中文时间格式
    record.success ? '成功' : '失败', // 使用中文状态
    record.responseTime || '',
    record.userId || '',
    record.errorMessage || '',
    record.createdAt ? new Date(record.createdAt).toLocaleString('zh-CN') : '',
  ]);

  // 生成CSV内容
  const csvLines = [headers, ...rows].map((row) =>
    row
      .map((field) => {
        // 处理包含逗号、引号或换行符的字段
        const stringField = String(field);
        if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
          return `"${stringField.replace(/"/g, '""')}"`;
        }
        return stringField;
      })
      .join(','),
  );

  return csvLines.join('\n');
}
/**
 * 获取趋势分析数据
 */
export const getTrendsData = async (req: Request, res: Response): Promise<void> => {
  try {
    // 基础参数验证（express-validator）
    const expressErrors = validationResult(req);
    if (!expressErrors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: 'Invalid request parameters',
        errors: expressErrors.array(),
      });
      return;
    }

    // 解析查询参数
    const {
      timeRange = '24h',
      startTime,
      endTime,
      granularity = 'hour',
      metrics = 'calls,success_rate',
      serverNames,
    } = req.query;

    // 构建趋势分析参数
    let trendParams: any = {
      timeRange: timeRange,
      granularity: granularity,
      metrics: (metrics as string).split(','),
    };

    // 添加自定义时间范围
    if (timeRange === 'custom') {
      if (!startTime || !endTime) {
        res.status(400).json({
          success: false,
          message: 'Custom time range requires startTime and endTime parameters',
        });
        return;
      }
      trendParams.startTime = startTime as string;
      trendParams.endTime = endTime as string;
    }

    // 添加服务器筛选
    if (serverNames) {
      trendParams.serverNames = (serverNames as string).split(',');
    }

    // 详细验证趋势分析参数
    const trendValidation = validateTrendAnalysisParams(trendParams);
    if (!trendValidation.isValid) {
      res.status(400).json({
        success: false,
        message: 'Trend analysis parameter validation failed',
        errors: trendValidation.errors,
      });
      return;
    }

    // 转换为正确的类型
    const finalTrendParams: TrendAnalysisParams = {
      timeRange: trendParams.timeRange as '1h' | '24h' | '7d' | '30d' | 'custom',
      granularity: trendParams.granularity as 'hour' | 'day' | 'week',
      metrics: trendParams.metrics.map((m: string) => m.trim()) as (
        | 'calls'
        | 'success_rate'
        | 'response_time'
      )[],
      startTime: trendParams.startTime,
      endTime: trendParams.endTime,
      serverNames: trendParams.serverNames?.map((s: string) => s.trim()),
    };

    // 执行趋势分析查询
    const result: TrendAnalysisResponse = await dataPersistenceService.queryTrends(finalTrendParams);

    // 构建响应
    const response = {
      success: true,
      data: result,
      query: {
        timeRange: finalTrendParams.timeRange,
        granularity: finalTrendParams.granularity,
        metrics: finalTrendParams.metrics,
        serverNames: finalTrendParams.serverNames,
      },
    };

    res.json(response);
  } catch (error) {
    console.error('Error in getTrendsData:', error);

    res.status(500).json({
      success: false,
      message: 'Failed to query trends data',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
/**
 * 生成最近24小时的测试数据
 */
export const generateRecentTestData = async (req: Request, res: Response): Promise<void> => {
  try {
    const { count = 50 } = req.body;

    // 验证参数
    if (count < 1 || count > 1000) {
      res.status(400).json({
        success: false,
        message: 'Count must be between 1 and 1000',
      });
      return;
    }

    // 生成测试数据
    await dataPersistenceService.generateRecentTestData(count);

    res.json({
      success: true,
      message: `Generated ${count} recent test records successfully`,
      data: {
        count,
        timeRange: 'last 24 hours',
      },
    });
  } catch (error) {
    console.error('Error in generateRecentTestData:', error);

    res.status(500).json({
      success: false,
      message: 'Failed to generate recent test data',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * 获取历史数据统计摘要
 * GET /api/analytics/summary
 *
 * 查询参数:
 * - startTime: 开始时间 (ISO 8601格式)
 * - endTime: 结束时间 (ISO 8601格式)
 * - serverNames: 服务器名称列表 (可选，逗号分隔)
 */
export const getHistorySummary = async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: 'Invalid request parameters',
        errors: errors.array(),
      });
      return;
    }

    const { startTime, endTime, serverNames } = req.query;

    // 构建查询参数 - 只获取聚合数据，不需要详细记录
    const queryParams: HistoryQueryParams = {
      timeRange: {
        startTime: startTime as string,
        endTime: endTime as string,
      },
      pagination: {
        page: 1,
        pageSize: 1, // 最小分页，因为我们只需要聚合数据
      },
    };

    if (serverNames) {
      queryParams.filters = {
        serverNames: (serverNames as string).split(',').map((s) => s.trim()),
      };
    }

    const result = await dataPersistenceService.queryHistory(queryParams);

    // 只返回聚合统计数据
    res.json({
      success: true,
      summary: result.aggregates,
      timeRange: queryParams.timeRange,
      filters: queryParams.filters,
    });
  } catch (error) {
    console.error('Error in getHistorySummary:', error);

    res.status(500).json({
      success: false,
      message: 'Failed to get history summary',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * 获取可用的筛选选项
 * GET /api/analytics/filters
 *
 * 返回可用的服务器名称、工具名称等筛选选项
 */
export const getFilterOptions = async (req: Request, res: Response): Promise<void> => {
  try {
    // 从数据库查询可用的筛选选项
    const { getAppDataSource } = await import('../db/connection.js');
    
    // 获取查询参数
    const { serverNames } = req.query;
    
    // 查询可用的服务器名称
    const serverNamesResult = await getAppDataSource().query(`
      SELECT DISTINCT server_name
      FROM mcp_call_logs
      WHERE server_name IS NOT NULL
      ORDER BY server_name
    `);

    // 查询可用的工具名称 - 根据服务器筛选
    let toolNamesQuery = `
      SELECT DISTINCT tool_name
      FROM mcp_call_logs
      WHERE tool_name IS NOT NULL
    `;
    let toolQueryParams: any[] = [];
    
    // 如果指定了服务器，则只查询这些服务器的工具
    if (serverNames && typeof serverNames === 'string' && serverNames.trim()) {
      const serverList = serverNames.split(',').map(s => s.trim()).filter(s => s);
      if (serverList.length > 0) {
        toolNamesQuery += ` AND server_name = ANY($1)`;
        toolQueryParams.push(serverList);
      }
    }
    
    toolNamesQuery += ` ORDER BY tool_name`;
    
    const toolNamesResult = await getAppDataSource().query(toolNamesQuery, toolQueryParams);

    // 查询可用的客户端IP地址
    const clientIpsResult = await getAppDataSource().query(`
      SELECT DISTINCT client_ip
      FROM mcp_call_logs
      WHERE client_ip IS NOT NULL
      ORDER BY client_ip
    `);

    res.json({
      success: true,
      filters: {
        serverNames: serverNamesResult.map((row: any) => row.server_name),
        toolNames: toolNamesResult.map((row: any) => row.tool_name),
        clientIps: clientIpsResult.map((row: any) => row.client_ip),
      },
    });
  } catch (error) {
    console.error('Error in getFilterOptions:', error);

    res.status(500).json({
      success: false,
      message: 'Failed to get filter options',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};;
/**
 * API文档：历史数据查询接口
 *
 * @api {get} /api/analytics/history 查询历史调用数据
 * @apiName GetHistoryData
 * @apiGroup Analytics
 * @apiVersion 1.0.0
 *
 * @apiDescription
 * 查询MCP工具调用的历史数据，支持多维度筛选、分页查询和时间序列聚合。
 * 可以返回详细的调用记录或聚合的时间序列数据。
 *
 * @apiParam {String} startTime 开始时间 (ISO 8601格式，必需)
 * @apiParam {String} endTime 结束时间 (ISO 8601格式，必需)
 * @apiParam {String} [serverNames] 服务器名称列表，逗号分隔
 * @apiParam {String} [toolNames] 工具名称列表，逗号分隔
 * @apiParam {String} [clientIps] 客户端IP地址列表，逗号分隔
 * @apiParam {Boolean} [success] 是否成功 (true/false)
 * @apiParam {Number} [page=1] 页码 (1-N)
 * @apiParam {Number} [pageSize=20] 每页大小 (1-1000)
 * @apiParam {String} [groupBy] 时间聚合粒度 (hour/day/week/month)
 * @apiParam {String} [metrics] 聚合指标，逗号分隔 (count/success_rate/avg_response_time)
 *
 * @apiSuccess {Boolean} success 请求是否成功
 * @apiSuccess {Object[]} data 详细调用记录 (非聚合模式)
 * @apiSuccess {Object} pagination 分页信息
 * @apiSuccess {Object} aggregates 聚合统计信息
 * @apiSuccess {Object} query 查询参数
 * @apiSuccess {Object[]} [timeSeries] 时间序列数据 (聚合模式)
 * @apiSuccess {Boolean} [isAggregated] 是否为聚合查询
 *
 * @apiSuccessExample {json} 详细记录响应示例:
 * {
 *   "success": true,
 *   "data": [
 *     {
 *       "id": 123,
 *       "serverName": "example-server",
 *       "toolName": "example-tool",
 *       "callTime": "2024-01-15T10:30:00.000Z",
 *       "success": true,
 *       "responseTime": 150,
 *       "userId": "user123",
 *       "errorMessage": null,
 *       "requestParams": {...},
 *       "responseData": {...},
 *       "createdAt": "2024-01-15T10:30:00.000Z"
 *     }
 *   ],
 *   "pagination": {
 *     "page": 1,
 *     "pageSize": 20,
 *     "total": 100,
 *     "totalPages": 5
 *   },
 *   "aggregates": {
 *     "totalCalls": 100,
 *     "successRate": 95.5,
 *     "avgResponseTime": 145.2
 *   },
 *   "query": {
 *     "timeRange": {
 *       "startTime": "2024-01-15T00:00:00.000Z",
 *       "endTime": "2024-01-15T23:59:59.999Z"
 *     },
 *     "filters": {
 *       "serverNames": ["example-server"]
 *     }
 *   }
 * }
 *
 * @apiSuccessExample {json} 时间序列聚合响应示例:
 * {
 *   "success": true,
 *   "data": [],
 *   "pagination": {
 *     "page": 1,
 *     "pageSize": 24,
 *     "total": 24,
 *     "totalPages": 1
 *   },
 *   "aggregates": {
 *     "totalCalls": 1200,
 *     "successRate": 96.2,
 *     "avgResponseTime": 142.8
 *   },
 *   "timeSeries": [
 *     {
 *       "timestamp": "2024-01-15T00:00:00.000Z",
 *       "metrics": {
 *         "count": 45,
 *         "success_rate": 95.6,
 *         "avg_response_time": 138.2
 *       }
 *     },
 *     {
 *       "timestamp": "2024-01-15T01:00:00.000Z",
 *       "metrics": {
 *         "count": 52,
 *         "success_rate": 96.2,
 *         "avg_response_time": 145.1
 *       }
 *     }
 *   ],
 *   "isAggregated": true,
 *   "query": {
 *     "timeRange": {
 *       "startTime": "2024-01-15T00:00:00.000Z",
 *       "endTime": "2024-01-15T23:59:59.999Z"
 *     },
 *     "aggregation": {
 *       "groupBy": "hour",
 *       "metrics": ["count", "success_rate", "avg_response_time"]
 *     }
 *   }
 * }
 *
 * @apiError {Boolean} success=false 请求失败
 * @apiError {String} message 错误信息
 * @apiError {Object[]} [errors] 参数验证错误详情
 *
 * @apiErrorExample {json} 参数错误响应:
 * {
 *   "success": false,
 *   "message": "Invalid request parameters",
 *   "errors": [
 *     {
 *       "msg": "Start time is required and must be a valid ISO 8601 timestamp",
 *       "param": "startTime",
 *       "location": "query"
 *     }
 *   ]
 * }
 *
 * @apiExample {curl} 查询详细记录:
 * curl -X GET "http://localhost:3001/api/analytics/history?startTime=2024-01-15T00:00:00.000Z&endTime=2024-01-15T23:59:59.999Z&page=1&pageSize=20"
 *
 * @apiExample {curl} 查询时间序列聚合:
 * curl -X GET "http://localhost:3001/api/analytics/history?startTime=2024-01-15T00:00:00.000Z&endTime=2024-01-15T23:59:59.999Z&groupBy=hour&metrics=count,success_rate,avg_response_time"
 *
 * @apiExample {curl} 复杂筛选查询:
 * curl -X GET "http://localhost:3001/api/analytics/history?startTime=2024-01-15T00:00:00.000Z&endTime=2024-01-15T23:59:59.999Z&serverNames=server1,server2&success=true&page=1&pageSize=50"
 */

/**
 * 清理测试数据API端点
 * DELETE /api/analytics/test-data
 *
 * 清理所有以 test-server- 开头的测试数据，包括：
 * - mcp_call_logs 表中的测试记录
 * - mcp_server_stats 表中的测试统计
 * - mcp_monitoring_metrics 表中的测试指标
 * - 内存中的测试数据
 */
export const cleanupTestData = async (req: Request, res: Response): Promise<void> => {
  try {
    await dataMigrationTestUtils.cleanupTestData();

    res.json({
      success: true,
      message: 'Test data cleanup completed successfully',
      data: {
        timestamp: new Date().toISOString(),
        cleanedTables: ['mcp_call_logs', 'mcp_server_stats', 'mcp_monitoring_metrics'],
        pattern: 'test-server-%'
      }
    });
  } catch (error) {
    console.error('Failed to cleanup test data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cleanup test data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
