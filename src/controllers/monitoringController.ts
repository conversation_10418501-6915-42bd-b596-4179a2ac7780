import { Request, Response } from 'express';
import { ApiResponse, PerformanceMetrics, HealthCheck } from '../types/index.js';
import { dataPersistenceService } from '../services/marketService.js';
import { getServersInfo } from '../services/mcpService.js';
import { isDatabaseConnected, getAppDataSource } from '../db/connection.js';
import { McpMonitoringMetrics } from '../db/entities/index.js';
import os from 'os';
import fs from 'fs/promises';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * 持久化监控指标到数据库
 */
async function persistMonitoringMetric(
  metricName: string,
  metricValue: number,
  metricType: 'gauge' | 'counter' | 'histogram',
  labels?: Record<string, any>
): Promise<void> {
  try {
    const dataSource = getAppDataSource();
    const metricsRepo = dataSource.getRepository(McpMonitoringMetrics);

    const metric = new McpMonitoringMetrics();
    metric.metric_name = metricName;
    metric.metric_value = metricValue;
    metric.metric_type = metricType;
    metric.labels = labels;

    await metricsRepo.save(metric);
  } catch (error) {
    console.warn('Failed to persist monitoring metric:', error);
  }
}

/**
 * 批量持久化系统指标
 */
async function persistSystemMetrics(): Promise<void> {
  try {
    const cpuUsage = getRealCpuUsage();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const memoryUsage = ((totalMemory - freeMemory) / totalMemory) * 100;
    const diskInfo = await getDiskUsage();
    const dbResponseTime = await getDatabaseResponseTime();

    // 持久化CPU指标
    await persistMonitoringMetric('system_cpu_usage_percent', cpuUsage, 'gauge');

    // 持久化内存指标
    await persistMonitoringMetric('system_memory_usage_percent', memoryUsage, 'gauge');
    await persistMonitoringMetric('system_memory_total_bytes', totalMemory, 'gauge');
    await persistMonitoringMetric('system_memory_free_bytes', freeMemory, 'gauge');

    // 持久化磁盘指标
    await persistMonitoringMetric('system_disk_usage_percent', diskInfo.usage, 'gauge');
    await persistMonitoringMetric('system_disk_total_bytes', diskInfo.total, 'gauge');
    await persistMonitoringMetric('system_disk_free_bytes', diskInfo.free, 'gauge');

    // 持久化数据库指标
    if (dbResponseTime >= 0) {
      await persistMonitoringMetric('database_response_time_ms', dbResponseTime, 'gauge');
    }

    // 持久化服务器指标
    const serversInfo = getServersInfo();
    const activeServers = serversInfo.filter((s) => s.status === 'connected').length;
    await persistMonitoringMetric('mcp_servers_active_count', activeServers, 'gauge');
    await persistMonitoringMetric('mcp_servers_total_count', serversInfo.length, 'gauge');

  } catch (error) {
    console.warn('Failed to persist system metrics:', error);
  }
}

/**
 * 获取真实的数据库响应时间
 */
export async function getDatabaseResponseTime(): Promise<number> {
  try {
    const startTime = Date.now();
    const dataSource = getAppDataSource();
    await dataSource.query('SELECT 1');
    return Date.now() - startTime;
  } catch (error) {
    console.warn('Failed to measure database response time:', error);
    return -1;
  }
}

/**
 * 获取真实的磁盘使用情况
 */
async function getDiskUsage(): Promise<{ total: number; used: number; free: number; usage: number }> {
  try {
    const { stdout } = await execAsync('df -B1 / | tail -1');
    const parts = stdout.trim().split(/\s+/);
    const total = parseInt(parts[1]);
    const used = parseInt(parts[2]);
    const free = parseInt(parts[3]);
    const usage = (used / total) * 100;

    return { total, used, free, usage: Math.round(usage * 100) / 100 };
  } catch (error) {
    console.warn('Failed to get disk usage:', error);
    return { total: 0, used: 0, free: 0, usage: 0 };
  }
}

/**
 * 获取真实的数据库连接池信息
 */
async function getDatabasePoolInfo(): Promise<{ active: number; idle: number; total: number }> {
  try {
    const dataSource = getAppDataSource();
    if (dataSource.isInitialized) {
      // TypeORM doesn't expose pool stats directly, so we'll estimate
      const result = await dataSource.query(`
        SELECT count(*) as active_connections
        FROM pg_stat_activity
        WHERE state = 'active' AND datname = current_database()
      `);
      const active = parseInt(result[0]?.active_connections || '0');
      return { active, idle: Math.max(0, 10 - active), total: 10 };
    }
    return { active: 0, idle: 0, total: 0 };
  } catch (error) {
    console.warn('Failed to get database pool info:', error);
    return { active: 0, idle: 0, total: 0 };
  }
}

/**
 * 获取真实的CPU使用率（修复计算方式）
 */
export function getRealCpuUsage(): number {
  try {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type as keyof typeof cpu.times];
      }
      totalIdle += cpu.times.idle;
    });

    const idle = totalIdle / cpus.length;
    const total = totalTick / cpus.length;
    const usage = 100 - ~~(100 * idle / total);

    return Math.max(0, Math.min(100, usage));
  } catch (error) {
    console.warn('Failed to calculate CPU usage:', error);
    // Fallback to load average method but cap at 100%
    const loadAvg = os.loadavg()[0];
    const cpuCount = os.cpus().length;
    return Math.min(100, Math.round((loadAvg / cpuCount) * 100));
  }
}

/**
 * 获取系统健康状态
 */
export const getSystemHealth = async (req: Request, res: Response): Promise<void> => {
  try {
    // 检查数据库连接
    const dbStatus = isDatabaseConnected();

    // 获取真实的数据库响应时间
    const dbResponseTime = await getDatabaseResponseTime();

    // 获取MCP服务器状态
    const serversInfo = getServersInfo();
    const totalServers = serversInfo.length;
    const runningServers = serversInfo.filter((server) => server.status === 'connected').length;
    const stoppedServers = serversInfo.filter((server) => server.status === 'disconnected').length;
    const errorServers = serversInfo.filter((server) => server.error !== null).length;

    // 检查数据持久化服务健康状态
    let persistenceHealth;
    try {
      persistenceHealth = await dataPersistenceService.healthCheck();
    } catch (error) {
      persistenceHealth = {
        status: 'unhealthy',
        details: {
          error: (error as Error).message,
          timestamp: new Date().toISOString(),
        },
      };
    }

    // 计算整体健康状态
    let overallStatus: 'healthy' | 'warning' | 'critical' = 'healthy';

    if (!dbStatus || persistenceHealth.status === 'unhealthy' || errorServers > 0 || dbResponseTime > 1000) {
      overallStatus = 'critical';
    } else if (stoppedServers > 0 || persistenceHealth.status === 'degraded' || dbResponseTime > 500) {
      overallStatus = 'warning';
    }

    const healthCheck: HealthCheck = {
      status: overallStatus,
      services: {
        database: {
          status: dbStatus && dbResponseTime >= 0 ? 'up' : 'down',
          responseTime: Math.max(0, dbResponseTime),
        },
        mcpServers: {
          status: errorServers === 0 ? 'up' : 'down',
          count: totalServers,
          active: runningServers,
        },
        cache: {
          status: 'up', // 暂时保持为up，后续可以实现Redis缓存检查
          size: 0,
          hitRate: 0,
        },
      },
      lastCheck: new Date().toISOString(),
    };

    const response: ApiResponse = {
      success: true,
      data: healthCheck,
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting system health:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get system health',
      error: (error as Error).message,
    });
  }
};

/**
 * 获取系统性能指标
 */
export const getPerformanceMetrics = async (req: Request, res: Response): Promise<void> => {
  try {
    // 获取真实的CPU使用率
    const cpuUsage = getRealCpuUsage();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsage = (usedMemory / totalMemory) * 100;

    // 获取真实的磁盘使用情况
    const diskInfo = await getDiskUsage();

    // 获取真实的数据库性能指标
    let dbMetrics = {
      connections: 0,
      responseTime: 0,
      queriesPerMinute: 0,
    };

    try {
      const dbResponseTime = await getDatabaseResponseTime();
      const poolInfo = await getDatabasePoolInfo();

      dbMetrics = {
        connections: poolInfo.active,
        responseTime: Math.max(0, dbResponseTime),
        queriesPerMinute: 0, // 这需要实现查询计数器，暂时保持为0
      };
    } catch (error) {
      console.warn('Failed to get database metrics:', error);
    }

    // 获取API性能指标 (暂时保持基础实现)
    const apiMetrics = {
      requestsPerMinute: 0, // 需要实现请求计数器
      averageResponseTime: 0, // 需要实现响应时间统计
      errorRate: 0, // 需要实现错误率统计
      activeConnections: 0, // 需要实现连接数统计
    };

    const performanceMetrics: PerformanceMetrics = {
      cpu: {
        usage: Math.round(cpuUsage * 100) / 100,
        cores: os.cpus().length,
      },
      memory: {
        total: totalMemory,
        used: usedMemory,
        free: freeMemory,
        usage: Math.round(memoryUsage * 100) / 100,
      },
      disk: diskInfo,
      database: dbMetrics,
      api: apiMetrics,
    };

    const response: ApiResponse = {
      success: true,
      data: performanceMetrics,
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting performance metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get performance metrics',
      error: (error as Error).message,
    });
  }
};

/**
 * 获取系统状态概览
 */
export const getSystemStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    // 获取系统基础信息
    const uptime = process.uptime();
    const nodeVersion = process.version;
    const platform = os.platform();
    const arch = os.arch();

    // 获取服务器信息
    const serversInfo = getServersInfo();
    const totalServers = serversInfo.length;
    const runningServers = serversInfo.filter((server) => server.status === 'connected').length;
    const stoppedServers = serversInfo.filter((server) => server.status === 'disconnected').length;
    const errorServers = serversInfo.filter((server) => server.error !== null).length;

    // 计算总调用次数
    const totalCalls = serversInfo.reduce((sum, server) => {
      return sum + (server.callStats?.totalCalls || 0);
    }, 0);

    // 计算成功率
    const totalSuccessCalls = serversInfo.reduce((sum, server) => {
      return sum + (server.callStats?.successCalls || 0);
    }, 0);
    const successRate = totalCalls > 0 ? (totalSuccessCalls / totalCalls) * 100 : 0;

    const systemStatus = {
      system: {
        uptime: Math.round(uptime),
        version: process.env.npm_package_version || '1.0.0',
        nodeVersion,
        platform,
        arch,
      },
      servers: {
        total: totalServers,
        running: runningServers,
        stopped: stoppedServers,
        error: errorServers,
      },
      performance: {
        totalCalls,
        successRate: Math.round(successRate * 100) / 100,
        avgResponseTime: '0ms', // TODO: 计算实际平均响应时间
        errorRate: Math.round((100 - successRate) * 100) / 100,
      },
      database: {
        connected: isDatabaseConnected(),
        // TODO: 添加更多数据库状态信息
      },
    };

    const response: ApiResponse = {
      success: true,
      data: systemStatus,
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting system status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get system status',
      error: (error as Error).message,
    });
  }
};

/**
 * 获取数据库状态详情
 */
export const getDatabaseStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const isConnected = isDatabaseConnected();

    let dbDetails = {
      connected: isConnected,
      connectionPool: {
        active: 0,
        idle: 0,
        total: 0,
      },
      performance: {
        avgQueryTime: 0,
        slowQueries: 0,
        totalQueries: 0,
      },
      storage: {
        size: 0,
        tables: 0,
        indexes: 0,
      },
    };

    if (isConnected) {
      try {
        // 获取真实的数据库连接池信息
        const poolInfo = await getDatabasePoolInfo();

        // 获取数据库大小信息
        const dataSource = getAppDataSource();
        const sizeResult = await dataSource.query(`
          SELECT pg_database_size(current_database()) as db_size
        `);

        // 获取表和索引数量
        const tableResult = await dataSource.query(`
          SELECT
            (SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public') as table_count,
            (SELECT count(*) FROM pg_indexes WHERE schemaname = 'public') as index_count
        `);

        // 获取平均查询时间（从pg_stat_statements如果可用）
        let avgQueryTime = 0;
        try {
          const queryStatsResult = await dataSource.query(`
            SELECT avg(mean_exec_time) as avg_time
            FROM pg_stat_statements
            WHERE calls > 0
          `);
          avgQueryTime = parseFloat(queryStatsResult[0]?.avg_time || '0');
        } catch {
          // pg_stat_statements可能未启用，使用默认值
          avgQueryTime = 0;
        }

        dbDetails = {
          connected: true,
          connectionPool: poolInfo,
          performance: {
            avgQueryTime: Math.round(avgQueryTime * 100) / 100,
            slowQueries: 0, // 需要实现慢查询统计
            totalQueries: 0, // 需要实现查询计数
          },
          storage: {
            size: parseInt(sizeResult[0]?.db_size || '0'),
            tables: parseInt(tableResult[0]?.table_count || '0'),
            indexes: parseInt(tableResult[0]?.index_count || '0'),
          },
        };
      } catch (error) {
        console.warn('Failed to get detailed database status:', error);
      }
    }

    const response: ApiResponse = {
      success: true,
      data: dbDetails,
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting database status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get database status',
      error: (error as Error).message,
    });
  }
};

/**
 * 获取MCP服务器详细状态
 */
export const getServersStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const serversInfo = getServersInfo();

    const serversStatus = serversInfo.map((server) => {
      // Map server status to monitoring status
      let monitoringStatus: 'running' | 'stopped' | 'error';
      if (server.error !== null) {
        monitoringStatus = 'error';
      } else if (server.status === 'connected') {
        monitoringStatus = 'running';
      } else {
        monitoringStatus = 'stopped';
      }

      return {
        name: server.name,
        status: monitoringStatus,
        type: 'stdio', // Default type since type is not in ServerInfo
        uptime: 0, // Default uptime since uptime is not in ServerInfo
        lastActivity: server.callStats?.lastCallTime
          ? new Date(server.callStats.lastCallTime).toISOString()
          : null,
        performance: {
          totalCalls: server.callStats?.totalCalls || 0,
          successCalls: server.callStats?.successCalls || 0,
          failedCalls: server.callStats?.failedCalls || 0,
          successRate: server.callStats?.totalCalls
            ? ((server.callStats.successCalls || 0) / server.callStats.totalCalls) * 100
            : 0,
        },
        tools: server.tools ? server.tools.length : 0,
        resources: 0, // Default since resources is not in ServerInfo
        prompts: 0, // Default since prompts is not in ServerInfo
      };
    });

    const summary = {
      total: serversInfo.length,
      running: serversInfo.filter((s) => s.status === 'connected').length,
      stopped: serversInfo.filter((s) => s.status === 'disconnected').length,
      error: serversInfo.filter((s) => s.error !== null).length,
      totalCalls: serversInfo.reduce((sum, s) => sum + (s.callStats?.totalCalls || 0), 0),
      totalTools: serversInfo.reduce((sum, s) => sum + (s.tools?.length || 0), 0),
    };

    const response: ApiResponse = {
      success: true,
      data: {
        summary,
        servers: serversStatus,
      },
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting servers status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get servers status',
      error: (error as Error).message,
    });
  }
};

/**
 * 获取实时监控数据（用于仪表板）
 */
export const getRealtimeData = async (req: Request, res: Response): Promise<void> => {
  try {
    // 获取真实的系统信息
    const cpuUsage = getRealCpuUsage();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const memoryUsage = ((totalMemory - freeMemory) / totalMemory) * 100;

    // 获取服务器状态
    const serversInfo = getServersInfo();
    const activeServers = serversInfo.filter((s) => s.status === 'connected').length;
    const totalCalls = serversInfo.reduce((sum, s) => sum + (s.callStats?.totalCalls || 0), 0);

    // 获取真实的数据库响应时间
    const dbResponseTime = await getDatabaseResponseTime();

    const realtimeData = {
      timestamp: new Date().toISOString(),
      system: {
        cpu: Math.round(cpuUsage * 100) / 100,
        memory: Math.round(memoryUsage * 100) / 100,
        uptime: process.uptime(),
      },
      servers: {
        active: activeServers,
        total: serversInfo.length,
      },
      performance: {
        totalCalls,
        requestsPerMinute: 0, // 需要实现请求计数器
        avgResponseTime: Math.max(0, dbResponseTime), // 使用数据库响应时间作为临时指标
      },
      database: {
        connected: isDatabaseConnected(),
        responseTime: Math.max(0, dbResponseTime),
      },
    };

    // 异步持久化监控数据（不阻塞响应）
    persistSystemMetrics().catch(error => {
      console.warn('Failed to persist monitoring metrics:', error);
    });

    const response: ApiResponse = {
      success: true,
      data: realtimeData,
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting realtime data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get realtime data',
      error: (error as Error).message,
    });
  }
};

/**
 * 获取监控历史数据
 */
export const getMonitoringHistory = async (req: Request, res: Response): Promise<void> => {
  try {
    const { metric, timeRange = '1h', limit = 100 } = req.query;

    const dataSource = getAppDataSource();
    const metricsRepo = dataSource.getRepository(McpMonitoringMetrics);

    // 计算时间范围
    const now = new Date();
    let startTime = new Date();

    switch (timeRange) {
      case '1h':
        startTime.setHours(now.getHours() - 1);
        break;
      case '6h':
        startTime.setHours(now.getHours() - 6);
        break;
      case '24h':
        startTime.setDate(now.getDate() - 1);
        break;
      case '7d':
        startTime.setDate(now.getDate() - 7);
        break;
      default:
        startTime.setHours(now.getHours() - 1);
    }

    let query = metricsRepo
      .createQueryBuilder('metrics')
      .where('metrics.recorded_at >= :startTime', { startTime })
      .orderBy('metrics.recorded_at', 'DESC')
      .limit(parseInt(limit as string));

    if (metric) {
      query = query.andWhere('metrics.metric_name = :metric', { metric });
    }

    const historyData = await query.getMany();

    const response: ApiResponse = {
      success: true,
      data: {
        metrics: historyData,
        timeRange,
        count: historyData.length,
      },
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting monitoring history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get monitoring history',
      error: (error as Error).message,
    });
  }
};

/**
 * 清理过期的监控数据
 */
export const cleanupMonitoringData = async (req: Request, res: Response): Promise<void> => {
  try {
    const { retentionDays = 30 } = req.query;

    const dataSource = getAppDataSource();
    const metricsRepo = dataSource.getRepository(McpMonitoringMetrics);

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - parseInt(retentionDays as string));

    const result = await metricsRepo
      .createQueryBuilder()
      .delete()
      .where('recorded_at < :cutoffDate', { cutoffDate })
      .execute();

    const response: ApiResponse = {
      success: true,
      data: {
        deletedCount: result.affected || 0,
        retentionDays: parseInt(retentionDays as string),
      },
    };

    res.json(response);
  } catch (error) {
    console.error('Error cleaning up monitoring data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cleanup monitoring data',
      error: (error as Error).message,
    });
  }
};
