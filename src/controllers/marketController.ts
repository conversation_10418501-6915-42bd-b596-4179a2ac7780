import { Request, Response } from 'express';
import { ApiResponse } from '../types/index.js';
import {
  getMarketServers,
  getMarketServerByName,
  getMarketCategories,
  getMarketTags,
  searchMarketServers,
  filterMarketServersByCategory,
  filterMarketServersByTag,
} from '../services/marketService.js';

// Get all market servers
export const getAllMarketServers = (_: Request, res: Response): void => {
  try {
    const marketServers = Object.values(getMarketServers());
    const response: ApiResponse = {
      success: true,
      data: marketServers,
    };
    res.json(response);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get market servers information',
    });
  }
};

// Get a specific market server by name
export const getMarketServer = (req: Request, res: Response): void => {
  try {
    const { name } = req.params;
    if (!name) {
      res.status(400).json({
        success: false,
        message: 'Server name is required',
      });
      return;
    }

    const server = getMarketServerByName(name);
    if (!server) {
      res.status(404).json({
        success: false,
        message: 'Market server not found',
      });
      return;
    }

    const response: ApiResponse = {
      success: true,
      data: server,
    };
    res.json(response);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get market server information',
    });
  }
};

// Get all market categories
export const getAllMarketCategories = (_: Request, res: Response): void => {
  try {
    const categories = getMarketCategories();
    const response: ApiResponse = {
      success: true,
      data: categories,
    };
    res.json(response);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get market categories',
    });
  }
};

// Get all market tags
export const getAllMarketTags = (_: Request, res: Response): void => {
  try {
    const tags = getMarketTags();
    const response: ApiResponse = {
      success: true,
      data: tags,
    };
    res.json(response);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get market tags',
    });
  }
};

// Search market servers
export const searchMarketServersByQuery = (req: Request, res: Response): void => {
  try {
    const { query } = req.query;
    const searchQuery = typeof query === 'string' ? query : '';

    const servers = searchMarketServers(searchQuery);
    const response: ApiResponse = {
      success: true,
      data: servers,
    };
    res.json(response);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to search market servers',
    });
  }
};
// 数据迁移控制器
import { dataMigrationService } from '../services/marketService.js';

// 执行数据迁移
export const migrateData = async (req: Request, res: Response): Promise<void> => {
  try {
    const result = await dataMigrationService.migrateAllData();

    const response: ApiResponse = {
      success: result.success,
      data: result,
      message: result.success ? 'Data migration completed successfully' : 'Data migration failed',
    };

    res.status(result.success ? 200 : 500).json(response);
  } catch (error) {
    console.error('Migration controller error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to execute data migration',
      error: (error as Error).message,
    });
  }
};

// 获取迁移状态
export const getMigrationStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const status = await dataMigrationService.getMigrationStatus();

    const response: ApiResponse = {
      success: true,
      data: status,
    };

    res.json(response);
  } catch (error) {
    console.error('Get migration status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get migration status',
      error: (error as Error).message,
    });
  }
};

// 回滚迁移
export const rollbackMigration = async (req: Request, res: Response): Promise<void> => {
  try {
    const { backupPath } = req.body;

    if (!backupPath) {
      res.status(400).json({
        success: false,
        message: 'Backup path is required',
      });
      return;
    }

    const result = await dataMigrationService.rollbackMigration(backupPath);

    const response: ApiResponse = {
      success: result.success,
      data: result,
      message: result.success
        ? 'Migration rollback completed successfully'
        : 'Migration rollback failed',
    };

    res.status(result.success ? 200 : 500).json(response);
  } catch (error) {
    console.error('Rollback migration error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to rollback migration',
      error: (error as Error).message,
    });
  }
};

// Filter market servers by category
export const getMarketServersByCategory = (req: Request, res: Response): void => {
  try {
    const { category } = req.params;

    const servers = filterMarketServersByCategory(category);
    const response: ApiResponse = {
      success: true,
      data: servers,
    };
    res.json(response);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to filter market servers by category',
    });
  }
};

// Filter market servers by tag
export const getMarketServersByTag = (req: Request, res: Response): void => {
  try {
    const { tag } = req.params;

    const servers = filterMarketServersByTag(tag);
    const response: ApiResponse = {
      success: true,
      data: servers,
    };
    res.json(response);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to filter market servers by tag',
    });
  }
};
