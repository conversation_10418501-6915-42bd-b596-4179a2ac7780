import { Request, Response, NextFunction } from 'express';

/**
 * 扩展Request接口以包含客户端信息
 */
declare global {
  namespace Express {
    interface Request {
      clientInfo?: {
        ip: string;
        userAgent?: string;
        forwardedFor?: string;
      };
    }
  }
}

/**
 * 获取客户端真实IP地址
 * 支持代理服务器和负载均衡器的场景
 */
export function getClientIP(req: Request): string {
  if (process.env.DEBUG_CLIENT_IP === 'true') {
    console.log(`[getClientIP] Starting IP detection for ${req.method} ${req.url}`);
    console.log(`[getClientIP] All headers:`, req.headers);
    console.log(`[getClientIP] req.ip:`, req.ip);
    console.log(`[getClientIP] req.ips:`, req.ips);
    console.log(`[getClientIP] Connection details:`, {
      remoteAddress: req.connection?.remoteAddress,
      remoteFamily: req.connection?.remoteFamily,
      remotePort: req.connection?.remotePort,
      localAddress: req.connection?.localAddress,
      localPort: req.connection?.localPort
    });
  }

  // 1. 优先检查代理头部（适用于通过代理、负载均衡器或反向代理的连接）
  const trustProxy = req.app?.get('trust proxy');
  if (process.env.DEBUG_CLIENT_IP === 'true') {
    console.log(`[getClientIP] Trust proxy setting:`, trustProxy);
  }

  // 尝试从X-Forwarded-For头部获取（最常用的代理头部）
  const forwardedFor = req.headers['x-forwarded-for'] as string;
  if (forwardedFor) {
    const firstIP = forwardedFor.split(',')[0].trim();
    if (isValidIP(firstIP) && !isLocalIP(firstIP)) {
      if (process.env.DEBUG_CLIENT_IP === 'true') {
        console.log(`[getClientIP] Using X-Forwarded-For IP: ${firstIP}`);
      }
      return firstIP;
    }
  }

  // 尝试从X-Real-IP头部获取（Nginx常用）
  const realIP = req.headers['x-real-ip'] as string;
  if (realIP && isValidIP(realIP) && !isLocalIP(realIP)) {
    if (process.env.DEBUG_CLIENT_IP === 'true') {
      console.log(`[getClientIP] Using X-Real-IP: ${realIP}`);
    }
    return realIP;
  }

  // 尝试从X-Client-IP头部获取
  const clientIP = req.headers['x-client-ip'] as string;
  if (clientIP && isValidIP(clientIP) && !isLocalIP(clientIP)) {
    if (process.env.DEBUG_CLIENT_IP === 'true') {
      console.log(`[getClientIP] Using X-Client-IP: ${clientIP}`);
    }
    return clientIP;
  }

  // 尝试从CF-Connecting-IP头部获取（Cloudflare）
  const cfConnectingIP = req.headers['cf-connecting-ip'] as string;
  if (cfConnectingIP && isValidIP(cfConnectingIP) && !isLocalIP(cfConnectingIP)) {
    if (process.env.DEBUG_CLIENT_IP === 'true') {
      console.log(`[getClientIP] Using CF-Connecting-IP: ${cfConnectingIP}`);
    }
    return cfConnectingIP;
  }

  // 2. 从TCP连接获取IP地址（但如果是本地地址，继续尝试其他方法）
  const tcpRemoteAddress = req.connection?.remoteAddress || req.socket?.remoteAddress;

  if (tcpRemoteAddress) {
    // 处理IPv6映射的IPv4地址 (::ffff:*********** -> ***********)
    let cleanIP = tcpRemoteAddress;
    if (tcpRemoteAddress.startsWith('::ffff:')) {
      cleanIP = tcpRemoteAddress.replace(/^::ffff:/, '');
    }

    if (isValidIP(cleanIP)) {
      // 如果不是本地IP，直接返回
      if (!isLocalIP(cleanIP)) {
        if (process.env.DEBUG_CLIENT_IP === 'true') {
          console.log(`[getClientIP] Using TCP connection IP: ${cleanIP} (original: ${tcpRemoteAddress})`);
        }
        return cleanIP;
      } else {
        // 如果是本地IP，记录但继续尝试其他方法
        if (process.env.DEBUG_CLIENT_IP === 'true') {
          console.log(`[getClientIP] TCP connection shows local IP: ${cleanIP}, trying other methods`);
        }
      }
    }
  }

  // 3. 尝试从Host头部推断真实IP（如果Host不是localhost）
  const hostHeader = req.headers.host;
  if (hostHeader && !hostHeader.startsWith('localhost') && !hostHeader.startsWith('127.0.0.1')) {
    const hostIP = hostHeader.split(':')[0]; // 移除端口号
    if (isValidIP(hostIP) && !isLocalIP(hostIP)) {
      if (process.env.DEBUG_CLIENT_IP === 'true') {
        console.log(`[getClientIP] Using Host header IP: ${hostIP} (from host: ${hostHeader})`);
      }
      return hostIP;
    }
  }

  // 4. 最后尝试req.ip（Express处理后的IP）
  if (req.ip && isValidIP(req.ip)) {
    if (process.env.DEBUG_CLIENT_IP === 'true') {
      console.log(`[getClientIP] Using req.ip: ${req.ip}`);
    }
    return req.ip;
  }

  // 5. 如果所有方法都失败，但TCP连接有本地IP，返回本地IP（总比unknown好）
  if (tcpRemoteAddress) {
    let cleanIP = tcpRemoteAddress;
    if (tcpRemoteAddress.startsWith('::ffff:')) {
      cleanIP = tcpRemoteAddress.replace(/^::ffff:/, '');
    }
    if (isValidIP(cleanIP)) {
      if (process.env.DEBUG_CLIENT_IP === 'true') {
        console.log(`[getClientIP] Fallback to TCP connection IP: ${cleanIP} (original: ${tcpRemoteAddress})`);
      }
      return cleanIP;
    }
  }

  if (process.env.DEBUG_CLIENT_IP === 'true') {
    console.log(`[getClientIP] No valid IP found, returning 'unknown'`);
    console.log(`[getClientIP] Debug info:`, {
      tcpRemoteAddress,
      connectionRemoteAddress: req.connection?.remoteAddress,
      socketRemoteAddress: req.socket?.remoteAddress,
      reqIp: req.ip,
      trustProxy,
      headers: {
        'x-forwarded-for': req.headers['x-forwarded-for'],
        'x-real-ip': req.headers['x-real-ip'],
        'x-client-ip': req.headers['x-client-ip'],
        'cf-connecting-ip': req.headers['cf-connecting-ip']
      }
    });
  }

  return 'unknown';
}

/**
 * 验证IP地址格式（IPv4和IPv6）
 */
function isValidIP(ip: string): boolean {
  if (!ip || ip === 'unknown') {
    return false;
  }

  // 移除IPv6的方括号（如果存在）
  const cleanIP = ip.replace(/^\[|\]$/g, '');

  // IPv4正则表达式
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  
  // IPv6正则表达式（简化版）
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
  
  // IPv6压缩格式
  const ipv6CompressedRegex = /^(?:[0-9a-fA-F]{1,4}:)*::(?:[0-9a-fA-F]{1,4}:)*[0-9a-fA-F]{1,4}$|^::$/;

  return ipv4Regex.test(cleanIP) || ipv6Regex.test(cleanIP) || ipv6CompressedRegex.test(cleanIP);
}

/**
 * 检查是否为本地IP地址
 */
function isLocalIP(ip: string): boolean {
  if (!ip) return true;

  // 本地回环地址
  if (ip === '127.0.0.1' || ip === '::1' || ip === 'localhost') {
    return true;
  }

  // 私有网络地址范围
  const privateRanges = [
    /^10\./,                    // 10.0.0.0/8
    /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12
    /^192\.168\./,              // ***********/16
    /^169\.254\./,              // ***********/16 (链路本地地址)
  ];

  return privateRanges.some(range => range.test(ip));
}

/**
 * 客户端信息中间件
 * 提取并存储客户端IP地址和其他相关信息
 */
export const clientInfoMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  try {
    const clientIP = getClientIP(req);
    const userAgent = req.headers['user-agent'];
    const forwardedFor = req.headers['x-forwarded-for'] as string;

    // 将客户端信息附加到请求对象
    req.clientInfo = {
      ip: clientIP,
      userAgent,
      forwardedFor,
    };

    // 在开发环境下记录客户端信息（用于调试）
    if (process.env.NODE_ENV === 'development') {
      console.log(`[ClientInfo] IP: ${clientIP}, User-Agent: ${userAgent?.substring(0, 50)}...`);
    }

    // 添加详细的IP地址调试信息
    if (process.env.DEBUG_CLIENT_IP === 'true') {
      console.log(`[ClientInfo Debug] Full headers:`, {
        'x-forwarded-for': req.headers['x-forwarded-for'],
        'x-real-ip': req.headers['x-real-ip'],
        'x-client-ip': req.headers['x-client-ip'],
        'cf-connecting-ip': req.headers['cf-connecting-ip'],
        'req.ip': req.ip,
        'req.connection.remoteAddress': req.connection?.remoteAddress,
        'req.socket.remoteAddress': req.socket?.remoteAddress,
        'final_client_ip': clientIP
      });
    }

    next();
  } catch (error) {
    console.error('Error in clientInfoMiddleware:', error);
    // 即使出错也要继续，设置默认值
    req.clientInfo = {
      ip: 'unknown',
      userAgent: req.headers['user-agent'],
      forwardedFor: req.headers['x-forwarded-for'] as string,
    };
    next();
  }
};

/**
 * 获取请求的客户端IP地址（便捷函数）
 */
export function getRequestClientIP(req: Request): string {
  return req.clientInfo?.ip || getClientIP(req);
}

/**
 * 测试客户端IP地址获取功能
 * 这个函数可以用于调试和验证IP地址解析是否正常工作
 */
export function testClientIPExtraction(mockHeaders: Record<string, string>): {
  extractedIP: string;
  headers: Record<string, string>;
  validationResults: Record<string, boolean>;
} {
  // 创建模拟的请求对象
  const mockReq = {
    headers: mockHeaders,
    ip: mockHeaders['mock-req-ip'],
    connection: {
      remoteAddress: mockHeaders['mock-connection-remote']
    },
    socket: {
      remoteAddress: mockHeaders['mock-socket-remote']
    }
  } as any;

  const extractedIP = getClientIP(mockReq);
  
  // 验证各个头部的有效性
  const validationResults = {
    'x-forwarded-for': mockHeaders['x-forwarded-for'] ? isValidIP(mockHeaders['x-forwarded-for'].split(',')[0].trim()) : false,
    'x-real-ip': mockHeaders['x-real-ip'] ? isValidIP(mockHeaders['x-real-ip']) : false,
    'x-client-ip': mockHeaders['x-client-ip'] ? isValidIP(mockHeaders['x-client-ip']) : false,
    'cf-connecting-ip': mockHeaders['cf-connecting-ip'] ? isValidIP(mockHeaders['cf-connecting-ip']) : false,
    'req.ip': mockHeaders['mock-req-ip'] ? isValidIP(mockHeaders['mock-req-ip']) : false,
    'connection.remoteAddress': mockHeaders['mock-connection-remote'] ? isValidIP(mockHeaders['mock-connection-remote']) : false
  };

  return {
    extractedIP,
    headers: mockHeaders,
    validationResults
  };
}

// 导出isValidIP函数以便外部使用
export { isValidIP };
