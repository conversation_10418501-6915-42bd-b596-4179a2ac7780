import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import config, { loadSettings } from '../config/index.js';

const validateBearerAuth = (req: Request, routingConfig: any, group?: string): boolean => {
  if (!routingConfig.enableBearerAuth) {
    return false;
  }

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }

  const token = authHeader.substring(7); // Remove "Bearer " prefix

  // If group is provided, check for group-level Bearer key first
  if (group) {
    // Import group service to get group information
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { getGroupByIdOrName } = require('../services/groupService.js');
    const groupInfo = getGroupByIdOrName(group);

    if (groupInfo && groupInfo.bearerAuthKey) {
      // Group has its own Bearer key, use it for authentication
      console.log(`Using group-level Bearer authentication for group: ${group}`);
      return token === groupInfo.bearerAuthKey;
    }
    // If group doesn't have its own Bearer key, fall back to global key
    console.log(`Group ${group} has no Bearer key, falling back to global authentication`);
  }

  // Use global Bearer key for authentication
  return token === routingConfig.bearerAuthKey;
};

// Middleware to authenticate JWT token
export const auth = (req: Request, res: Response, next: NextFunction): void => {
  // Check if authentication is disabled globally
  const routingConfig = loadSettings().systemConfig?.routing || {
    enableGlobalRoute: true,
    enableGroupNameRoute: true,
    enableBearerAuth: false,
    bearerAuthKey: '',
    skipAuth: false,
  };

  if (routingConfig.skipAuth) {
    next();
    return;
  }

  // Extract group information from URL path if present
  // This handles routes like /api/groups/:id or other group-specific routes
  let group: string | undefined;

  // Check if this is a group-specific route
  const groupMatch = req.path.match(/\/groups\/([^/]+)/);
  if (groupMatch) {
    group = groupMatch[1];
  }

  // Also check for group parameter in query string
  if (!group && req.query.group) {
    group = req.query.group as string;
  }

  // Check if bearer auth is enabled and validate it
  if (validateBearerAuth(req, routingConfig, group)) {
    next();
    return;
  }

  // Get token from header or query parameter
  const headerToken = req.header('x-auth-token');
  const queryToken = req.query.token as string;
  const token = headerToken || queryToken;

  // Check if no token
  if (!token) {
    res.status(401).json({ success: false, message: 'No token, authorization denied' });
    return;
  }

  // Verify token
  try {
    const decoded = jwt.verify(token, config.jwtSecret);

    // Add user from payload to request
    (req as any).user = (decoded as any).user;
    next();
  } catch (error) {
    res.status(401).json({ success: false, message: 'Token is not valid' });
  }
};
