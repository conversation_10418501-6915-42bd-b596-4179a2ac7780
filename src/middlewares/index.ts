import express, { Request, Response, NextFunction } from 'express';
import { auth } from './auth.js';
import { clientInfoMiddleware } from './clientInfo.js';
import { initializeDefaultUser } from '../models/User.js';
import config from '../config/index.js';

export const errorHandler = (
  err: Error,
  _req: Request,
  res: Response,
  _next: NextFunction,
): void => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
  });
};

export const initMiddlewares = (app: express.Application): void => {
  // Serve static files from the dynamically determined frontend path
  // Note: Static files will be handled by the server directly, not here

  // 设置trust proxy以正确获取客户端IP地址
  // 这对于通过代理（如Nginx、负载均衡器等）连接的客户端是必需的
  // 使用更宽松的配置来支持各种代理场景，包括Docker网络
  app.set('trust proxy', true); // 信任所有代理，强制检查代理头部

  // 添加客户端信息中间件（在其他中间件之前）
  app.use(clientInfoMiddleware);

  app.use((req, res, next) => {
    const basePath = config.basePath;
    // Only apply JSON parsing for API and auth routes, not for SSE or message endpoints
    // TODO exclude sse responses by mcp endpoint
    if (
      req.path !== `${basePath}/sse` &&
      !req.path.startsWith(`${basePath}/sse/`) &&
      req.path !== `${basePath}/messages`
    ) {
      express.json()(req, res, next);
    } else {
      next();
    }
  });

  // Initialize default admin user if no users exist
  initializeDefaultUser().catch((err) => {
    console.error('Error initializing default user:', err);
  });

  // Protect API routes with authentication middleware, but exclude auth endpoints
  app.use(`${config.basePath}/api`, (req, res, next) => {
    // Skip authentication for login and register endpoints
    if (req.path === '/auth/login' || req.path === '/auth/register') {
      next();
    } else {
      auth(req, res, next);
    }
  });

  app.use(errorHandler);
};
