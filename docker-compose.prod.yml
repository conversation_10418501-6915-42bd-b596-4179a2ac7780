version: '3.8'

services:
  mcphub:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: mcphub-prod
    restart: unless-stopped
    ports:
      - "8080:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=*******************************************/mcphub
    volumes:
      - ./logs:/app/logs
      - ./mcp_settings.json:/app/mcp_settings.json:ro
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - mcphub-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: pgvector/pgvector:pg16
    container_name: mcphub-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=mcphub
      - POSTGRES_USER=mcphub
      - POSTGRES_PASSWORD=mcphub123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-db:/docker-entrypoint-initdb.d:ro
      - ./docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    ports:
      - "5432:5432"
    networks:
      - mcphub-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mcphub -d mcphub"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  nginx:
    image: nginx:alpine
    container_name: mcphub-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - mcphub
    networks:
      - mcphub-network

volumes:
  postgres_data:
    driver: local

networks:
  mcphub-network:
    driver: bridge
