# MCPHub 代码结构

## 项目根目录结构
```
mcphub/
├── src/                    # 后端源代码
├── frontend/               # 前端源代码
├── tests/                  # 测试文件
├── docs/                   # 文档
├── bin/                    # CLI 工具
├── scripts/                # 构建脚本
├── examples/               # 示例配置
├── mcp_settings.json       # MCP 服务器配置
├── servers.json            # 服务器市场数据
├── package.json            # 项目配置
└── tsconfig.json           # TypeScript 配置
```

## 后端结构 (src/)
```
src/
├── controllers/            # 控制器层
│   ├── authController.ts   # 认证控制器
│   ├── serverController.ts # 服务器管理
│   ├── groupController.ts  # 组管理
│   ├── toolController.ts   # 工具调用
│   ├── marketController.ts # 市场管理
│   └── analyticsController.ts # 数据分析控制器
├── services/               # 业务逻辑层
│   ├── mcpService.ts       # MCP 服务器服务
│   ├── groupService.ts     # 组管理服务
│   ├── logService.ts       # 日志服务
│   ├── vectorSearchService.ts # 向量搜索
│   └── analyticsService.ts # 数据分析服务
├── models/                 # 数据模型
├── db/                     # 数据库相关
│   ├── entities/           # 实体定义
│   ├── repositories/       # 数据访问层
│   └── connection.ts       # 数据库连接
├── middlewares/            # 中间件
├── routes/                 # 路由定义
├── types/                  # 类型定义
├── utils/                  # 工具函数
├── config/                 # 配置管理
├── index.ts                # 应用入口
└── server.ts               # 服务器配置
```

## 前端结构 (frontend/src/)
```
frontend/src/
├── components/             # React 组件
│   ├── ui/                 # 基础 UI 组件
│   ├── layout/             # 布局组件
│   ├── icons/              # 图标组件
│   └── analytics/          # 数据分析组件
├── pages/                  # 页面组件
│   ├── Analytics.tsx       # 数据分析页面
│   └── ...                 # 其他页面
├── contexts/               # React Context
├── hooks/                  # 自定义 Hooks
├── services/               # API 服务
├── types/                  # 类型定义
├── utils/                  # 工具函数
├── layouts/                # 页面布局
├── locales/                # 国际化文件
│   ├── en.json             # 英文翻译
│   └── zh.json             # 中文翻译
├── App.tsx                 # 应用根组件
└── main.tsx                # 应用入口
```

## 关键文件说明
- **mcp_settings.json**: 存储 MCP 服务器配置、用户信息、组信息
- **servers.json**: 服务器市场的元数据信息
- **src/services/mcpService.ts**: MCP 服务器管理的核心逻辑
- **src/services/analyticsService.ts**: 数据分析功能的核心逻辑
- **frontend/src/pages/Analytics.tsx**: 数据分析页面的完整实现
- **frontend/src/components/analytics/**: 数据分析相关组件
- **frontend/src/locales/**: 完整的国际化支持文件

## 新增功能模块
- **数据分析模块**: 完整的API调用统计、趋势分析、智能洞察功能
- **客户端追踪**: IP地址显示和用户行为分析
- **组级别认证**: 支持组级别的Bearer token配置
- **国际化增强**: 完善的中英文双语支持