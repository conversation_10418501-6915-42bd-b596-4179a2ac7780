# MCPHub 任务完成后的操作

## 代码质量检查
完成代码修改后，按以下顺序执行：

### 1. 代码格式化
```bash
pnpm format
```
- 自动格式化所有 TypeScript 文件
- 确保代码风格一致性

### 2. 代码检查
```bash
pnpm lint
```
- 检查 TypeScript 语法错误
- 检查代码风格问题
- 修复可自动修复的问题

### 3. 类型检查
```bash
npx tsc --noEmit
```
- 验证 TypeScript 类型正确性
- 确保没有类型错误

## 测试验证
### 4. 运行测试
```bash
# 运行所有测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 如果有特定测试文件
pnpm test -- --testPathPattern=specific-test.test.ts
```

### 5. 构建验证
```bash
# 验证后端构建
pnpm backend:build

# 验证前端构建
pnpm frontend:build

# 验证整体构建
pnpm build
```

## 功能测试
### 6. 启动开发环境测试
```bash
# 启动开发服务器
pnpm dev

# 或分别启动
pnpm backend:dev    # 后端: http://localhost:3000
pnpm frontend:dev   # 前端: http://localhost:5000
```

### 7. 手动功能测试
- 访问前端界面验证功能正常
- 测试相关的 API 端点
- 验证数据持久化正确
- 检查日志输出无异常

## 提交前检查
### 8. Git 操作
```bash
# 查看修改状态
git status

# 查看具体修改内容
git diff

# 添加修改文件
git add .

# 提交修改 (使用有意义的提交信息)
git commit -m "feat: 添加新功能描述"

# 推送到远程仓库
git push
```

## 部署验证 (如果需要)
### 9. 生产环境测试
```bash
# 构建生产版本
pnpm build

# 启动生产服务器
pnpm start

# 验证生产环境功能正常
```

## 文档更新
### 10. 更新相关文档
- 如果添加了新功能，更新 README.md
- 如果修改了 API，更新 API 文档
- 如果更改了配置，更新配置说明

## 检查清单
- [ ] 代码格式化完成
- [ ] 代码检查通过
- [ ] 类型检查通过
- [ ] 测试运行通过
- [ ] 构建成功
- [ ] 功能测试正常
- [ ] Git 提交完成
- [ ] 文档已更新