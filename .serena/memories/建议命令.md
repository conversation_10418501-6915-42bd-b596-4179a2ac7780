# MCPHub 建议命令

## 开发环境启动
```bash
# 安装依赖
pnpm install

# 启动开发环境 (前后端同时启动)
pnpm dev

# 仅启动后端开发服务器
pnpm backend:dev

# 仅启动前端开发服务器
pnpm frontend:dev

# 启动调试模式
pnpm debug
```

## 构建和生产
```bash
# 构建整个项目
pnpm build

# 仅构建后端
pnpm backend:build

# 仅构建前端
pnpm frontend:build

# 启动生产服务器
pnpm start

# 前端预览构建结果
pnpm frontend:preview
```

## 代码质量
```bash
# 代码检查
pnpm lint

# 代码格式化
pnpm format

# 运行测试
pnpm test

# 监听模式运行测试
pnpm test:watch

# 生成测试覆盖率报告
pnpm test:coverage

# 详细测试输出
pnpm test:verbose

# CI 环境测试
pnpm test:ci
```

## 系统命令 (Linux)
```bash
# 文件操作
ls -la                      # 列出文件详情
find . -name "*.ts"         # 查找 TypeScript 文件
grep -r "pattern" src/      # 在源码中搜索模式
cd src/                     # 切换目录

# 进程管理
ps aux | grep node          # 查看 Node.js 进程
kill -9 <pid>              # 强制终止进程
netstat -tulpn | grep :3000 # 查看端口占用

# Git 操作
git status                  # 查看状态
git add .                   # 添加所有更改
git commit -m "message"     # 提交更改
git push                    # 推送到远程
git pull                    # 拉取最新代码
```

## 端口配置
- **后端服务器**: 默认 3000 端口 (可通过 PORT 环境变量配置)
- **前端开发服务器**: 5000 端口 (在 vite.config.ts 中配置)
- **数据库**: PostgreSQL 默认 5432 端口