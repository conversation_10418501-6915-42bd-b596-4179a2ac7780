# MCPHub开发规范更新 - React最佳实践

## 更新说明
基于MCPHub项目中趋势分析死循环Bug的修复经验，更新React开发规范，增加Hook使用和性能优化的最佳实践。

## React Hook使用规范

### 1. useCallback使用规范

#### 基本原则
- **谨慎使用**: 不是所有函数都需要useCallback
- **完整依赖**: 依赖数组必须包含所有使用的变量
- **稳定优先**: 优先保证依赖项的稳定性

#### 使用场景
```typescript
// ✅ 适合使用useCallback的场景
const ParentComponent = () => {
  const [filter, setFilter] = useState('');
  
  // 传递给子组件的回调函数
  const handleItemClick = useCallback((id: string) => {
    // 处理点击事件
    console.log('Clicked item:', id);
  }, []); // 无外部依赖，真正稳定
  
  return <ItemList onItemClick={handleItemClick} />;
};

// ❌ 不适合使用useCallback的场景
const Component = () => {
  const [data, setData] = useState(null);
  
  // 简单的事件处理，不需要useCallback
  const handleClick = () => {
    setData(null);
  };
  
  return <button onClick={handleClick}>Reset</button>;
};
```

#### 依赖项管理
```typescript
// ✅ 正确的依赖项管理
const Component = ({ userId }: { userId: string }) => {
  const [data, setData] = useState(null);
  
  const loadUserData = useCallback(async () => {
    const result = await fetchUserData(userId);
    setData(result);
  }, [userId]); // 包含所有外部依赖
  
  useEffect(() => {
    loadUserData();
  }, [loadUserData]);
};

// ❌ 错误的依赖项管理
const Component = ({ userId }: { userId: string }) => {
  const [data, setData] = useState(null);
  
  const loadUserData = useCallback(async () => {
    const result = await fetchUserData(userId);
    setData(result);
  }, []); // 缺少userId依赖，可能导致闭包陷阱
};
```

### 2. useMemo使用规范

#### 使用原则
- **昂贵计算**: 只对真正昂贵的计算使用useMemo
- **引用稳定**: 用于保持对象引用的稳定性
- **避免滥用**: 不要对简单计算使用useMemo

#### 正确使用示例
```typescript
// ✅ 适合使用useMemo的场景
const ExpensiveComponent = ({ items }: { items: Item[] }) => {
  // 昂贵的计算
  const expensiveValue = useMemo(() => {
    return items.reduce((sum, item) => {
      return sum + complexCalculation(item);
    }, 0);
  }, [items]);
  
  // 保持对象引用稳定
  const config = useMemo(() => ({
    sortBy: 'name',
    order: 'asc'
  }), []); // 真正的空依赖
  
  return <div>{expensiveValue}</div>;
};

// ❌ 不适合使用useMemo的场景
const SimpleComponent = ({ name }: { name: string }) => {
  // 简单计算，不需要useMemo
  const displayName = useMemo(() => {
    return name.toUpperCase();
  }, [name]); // 过度优化
  
  return <div>{displayName}</div>;
};
```

### 3. useEffect使用规范

#### 依赖项规则
- **完整依赖**: 包含所有在effect中使用的变量
- **稳定引用**: 避免在依赖数组中使用不稳定的引用
- **清理函数**: 及时清理副作用

#### 最佳实践
```typescript
// ✅ 正确的useEffect使用
const Component = ({ url }: { url: string }) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    let cancelled = false;
    
    const loadData = async () => {
      setLoading(true);
      try {
        const result = await fetch(url);
        if (!cancelled) {
          setData(result);
        }
      } catch (error) {
        if (!cancelled) {
          console.error('Failed to load data:', error);
        }
      } finally {
        if (!cancelled) {
          setLoading(false);
        }
      }
    };
    
    loadData();
    
    // 清理函数
    return () => {
      cancelled = true;
    };
  }, [url]); // 完整的依赖项
  
  return loading ? <div>Loading...</div> : <div>{data}</div>;
};
```

### 4. 自定义Hook设计规范

#### 设计原则
- **单一职责**: 每个Hook只负责一个功能
- **参数简化**: 避免复杂的配置对象
- **返回稳定**: 确保返回值的引用稳定

#### 设计模式
```typescript
// ✅ 良好的自定义Hook设计
const useApiData = <T>(url: string, enabled: boolean = true) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const refetch = useCallback(async () => {
    if (!enabled) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(url);
      const result = await response.json();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [url, enabled]);
  
  useEffect(() => {
    refetch();
  }, [refetch]);
  
  // 返回稳定的对象
  return useMemo(() => ({
    data,
    loading,
    error,
    refetch
  }), [data, loading, error, refetch]);
};

// ❌ 避免的设计模式
const useApiDataBad = (config: {
  url: string;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
  retryConfig?: RetryConfig;
}) => {
  // 复杂的配置对象会导致依赖项不稳定
  // 每次传入的config都可能是新对象
};
```

## 性能优化规范

### 1. 组件渲染优化

#### React.memo使用
```typescript
// ✅ 适合使用React.memo的场景
interface ItemProps {
  id: string;
  name: string;
  onClick: (id: string) => void;
}

const Item = React.memo<ItemProps>(({ id, name, onClick }) => {
  const handleClick = useCallback(() => {
    onClick(id);
  }, [id, onClick]);
  
  return <div onClick={handleClick}>{name}</div>;
});

// 自定义比较函数
const ItemWithCustomCompare = React.memo<ItemProps>(
  ({ id, name, onClick }) => {
    return <div onClick={() => onClick(id)}>{name}</div>;
  },
  (prevProps, nextProps) => {
    return prevProps.id === nextProps.id && 
           prevProps.name === nextProps.name;
    // 忽略onClick的比较，因为它可能每次都不同
  }
);
```

#### 避免内联对象和函数
```typescript
// ❌ 避免的模式
const ParentComponent = () => {
  return (
    <ChildComponent 
      style={{ margin: 10 }} // 每次都是新对象
      onClick={() => console.log('clicked')} // 每次都是新函数
    />
  );
};

// ✅ 推荐的模式
const ITEM_STYLE = { margin: 10 }; // 提取到组件外部

const ParentComponent = () => {
  const handleClick = useCallback(() => {
    console.log('clicked');
  }, []);
  
  return (
    <ChildComponent 
      style={ITEM_STYLE}
      onClick={handleClick}
    />
  );
};
```

### 2. 状态管理优化

#### 状态结构设计
```typescript
// ✅ 良好的状态结构
interface AppState {
  user: {
    id: string;
    name: string;
    email: string;
  } | null;
  ui: {
    loading: boolean;
    error: string | null;
  };
  data: {
    items: Item[];
    filters: FilterState;
  };
}

// ❌ 避免的状态结构
interface BadState {
  userIdAndNameAndEmail: string; // 混合数据
  isLoadingUserOrData: boolean; // 模糊的状态
  everythingElse: any; // 过于宽泛
}
```

#### 状态更新模式
```typescript
// ✅ 推荐的状态更新模式
const useUserData = () => {
  const [state, setState] = useState({
    user: null,
    loading: false,
    error: null
  });
  
  const loadUser = useCallback(async (id: string) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const user = await fetchUser(id);
      setState(prev => ({ ...prev, user, loading: false }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error.message 
      }));
    }
  }, []);
  
  return { ...state, loadUser };
};
```

## 错误处理规范

### 1. 错误边界
```typescript
// 错误边界组件
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    // 发送错误报告到监控服务
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }
    
    return this.props.children;
  }
}
```

### 2. 异步错误处理
```typescript
// 统一的错误处理Hook
const useAsyncError = () => {
  const [error, setError] = useState<Error | null>(null);
  
  const throwError = useCallback((error: Error) => {
    setError(error);
  }, []);
  
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  // 抛出错误给错误边界
  if (error) {
    throw error;
  }
  
  return { throwError, clearError };
};
```

## 测试规范

### 1. 组件测试
```typescript
// 组件测试示例
describe('TrendsTab', () => {
  it('should not cause infinite re-renders', async () => {
    const renderSpy = jest.fn();
    
    const TestComponent = () => {
      renderSpy();
      return <TrendsTab />;
    };
    
    render(<TestComponent />);
    
    // 等待一段时间确保没有无限渲染
    await waitFor(() => {
      expect(renderSpy).toHaveBeenCalledTimes(1);
    }, { timeout: 1000 });
  });
  
  it('should handle API errors gracefully', async () => {
    // Mock API失败
    jest.spyOn(analyticsService, 'fetchTrendsData')
      .mockRejectedValue(new Error('API Error'));
    
    render(<TrendsTab />);
    
    await waitFor(() => {
      expect(screen.getByText(/error/i)).toBeInTheDocument();
    });
  });
});
```

### 2. Hook测试
```typescript
// Hook测试示例
describe('useErrorHandler', () => {
  it('should not cause infinite loops', () => {
    const { result } = renderHook(() => useErrorHandler());
    
    // 验证返回值稳定
    const firstResult = result.current;
    
    // 重新渲染
    renderHook(() => useErrorHandler());
    
    expect(result.current).toBe(firstResult);
  });
});
```

## 代码审查检查清单

### React Hook检查项
- [ ] useCallback的依赖数组是否包含所有使用的变量？
- [ ] useMemo是否真正必要，还是过度优化？
- [ ] useEffect是否可能导致无限循环？
- [ ] 自定义Hook的设计是否简洁合理？
- [ ] Hook的返回值是否稳定？

### 性能检查项
- [ ] 组件是否有不必要的重新渲染？
- [ ] 是否正确使用了React.memo？
- [ ] 是否避免了内联对象和函数？
- [ ] 状态结构是否合理？
- [ ] 是否有内存泄漏风险？

### 错误处理检查项
- [ ] 是否有适当的错误边界？
- [ ] 异步操作是否有错误处理？
- [ ] 是否有用户友好的错误提示？
- [ ] 错误是否被正确记录？

## 工具配置

### ESLint配置更新
```javascript
// .eslintrc.js 新增规则
module.exports = {
  extends: [
    'plugin:react-hooks/recommended'
  ],
  rules: {
    // React Hooks规则
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    
    // 性能相关规则
    'react/jsx-no-bind': 'warn',
    'react/jsx-no-constructed-context-values': 'warn',
    
    // 自定义规则
    'no-console': ['warn', { allow: ['warn', 'error'] }]
  }
};
```

### TypeScript配置
```json
{
  "compilerOptions": {
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true
  }
}
```

## 总结

这些规范基于实际项目经验总结，重点关注：

1. **Hook使用的正确性**: 避免死循环和性能问题
2. **代码的可维护性**: 清晰的结构和命名
3. **性能优化**: 合理的优化策略
4. **错误处理**: 完善的错误处理机制
5. **测试覆盖**: 确保代码质量

遵循这些规范可以有效避免类似MCPHub趋势分析死循环的问题，提高代码质量和开发效率。