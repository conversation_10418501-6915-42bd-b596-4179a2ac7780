# MCPHub 项目执行流程分析

## 项目启动流程

### 1. 开发环境启动
```bash
# 同时启动后端和前端开发服务器
pnpm dev
# 等价于: concurrently "pnpm backend:dev" "pnpm frontend:dev"

# 后端开发服务器
pnpm backend:dev  # tsx watch src/index.ts

# 前端开发服务器  
pnpm frontend:dev # cd frontend && vite
```

### 2. 生产环境启动
```bash
# 构建项目
pnpm build  # 等价于: pnpm backend:build && pnpm frontend:build

# 启动生产服务器
pnpm start  # node dist/index.js
```

## 后端执行流程

### 1. 应用入口 (src/index.ts)
- `boot()` 函数作为应用启动入口
- 调用 `appServer.initialize()` 初始化服务器
- 调用 `appServer.start()` 启动服务器

### 2. 服务器初始化 (src/server.ts - AppServer类)
**initialize() 方法执行顺序：**
1. **用户初始化**: `initializeDefaultUser()` - 创建默认管理员用户
2. **中间件初始化**: `initMiddlewares(this.app)` - 设置Express中间件
3. **路由初始化**: `initRoutes(this.app)` - 注册API路由
4. **MCP服务器初始化**: `initUpstreamServers()` - 初始化MCP服务器连接
5. **SSE/MCP路由注册**: 注册实时通信和MCP协议路由
6. **前端服务**: `findAndServeFrontend()` - 查找并服务前端静态文件

### 3. MCP服务器初始化流程 (src/services/mcpService.ts)
**initUpstreamServers() → registerAllTools() → initializeClientsFromSettings():**

1. **加载配置**: 从 `mcp_settings.json` 读取服务器配置
2. **遍历服务器配置**: 为每个启用的服务器创建连接
3. **服务器类型处理**:
   - **OpenAPI类型**: 创建OpenAPIClient，初始化并获取工具列表
   - **其他类型**: 创建MCP传输层和客户端连接
4. **工具注册**: 获取每个服务器的工具列表并注册
5. **向量化存储**: 将工具信息保存为向量嵌入用于智能搜索
6. **保活机制**: 为SSE连接设置心跳保活

### 4. 服务器启动 (AppServer.start())
- 监听指定端口 (默认3000)
- 绑定到 0.0.0.0 接受所有网络接口连接
- 输出访问URL信息

## 前端执行流程

### 1. 应用入口 (frontend/src/main.tsx)
**initializeApp() 函数执行流程：**
1. **运行时配置加载**: `loadRuntimeConfig()` - 从后端获取配置
2. **全局配置存储**: 将配置存储到 `window.__MCPHUB_CONFIG__`
3. **React应用渲染**: 创建React根节点并渲染App组件

### 2. 应用组件结构 (frontend/src/App.tsx)
**组件层次结构：**
```
App
├── ThemeProvider (主题上下文)
├── AuthProvider (认证上下文)  
├── ToastProvider (通知上下文)
└── Router (路由管理)
    ├── 公共路由: /login (登录页面)
    └── 受保护路由 (需要认证)
        └── MainLayout (主布局)
            ├── / (仪表板)
            ├── /servers (服务器管理)
            ├── /groups (组管理)
            ├── /market (市场)
            ├── /logs (日志)
            └── /settings (设置)
```

### 3. 页面功能模块
- **Dashboard**: 系统概览和统计信息
- **Servers**: MCP服务器的安装、配置、启用/禁用
- **Groups**: 服务器分组管理
- **Market**: 浏览和安装市场中的MCP服务器
- **Tools**: 直接调用MCP工具
- **Logs**: 实时查看系统日志
- **Settings**: 系统配置和用户管理

## 关键执行特点

### 1. 异步初始化
- 后端MCP服务器连接是异步的，不会阻塞主服务器启动
- 前端配置加载失败时有降级机制

### 2. 实时通信
- 使用SSE (Server-Sent Events) 实现实时日志流
- MCP协议支持多种传输方式 (stdio, SSE, OpenAPI)

### 3. 智能路由
- 支持基于向量搜索的工具智能路由
- 可选的PostgreSQL + pgvector支持

### 4. 容错机制
- 单个MCP服务器连接失败不影响整体系统
- 前端配置加载失败时使用默认配置
- 前端未找到时后端仍可提供API服务

### 5. 开发友好
- 支持热重载开发模式
- 前后端可独立开发和调试
- 完整的测试框架支持