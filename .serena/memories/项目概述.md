# MCPHub 项目概述

## 项目目的
MCPHub 是一个用于管理 MCP (Model Context Protocol) 服务器的中心化管理平台。它提供了一个 Web 界面来安装、配置、管理和监控各种 MCP 服务器。

## 主要功能
- **MCP 服务器管理**: 安装、启用/禁用、配置 MCP 服务器
- **服务器市场**: 浏览和安装来自市场的 MCP 服务器
- **组管理**: 将服务器组织成组进行批量管理，支持组级别Bearer认证配置
- **工具调用**: 直接在 Web 界面中调用 MCP 工具
- **智能路由**: 基于向量搜索的智能工具路由（可选）
- **用户认证**: 支持用户登录和权限管理
- **日志监控**: 实时查看系统日志
- **DXT 支持**: 支持上传和管理 DXT 文件
- **数据分析**: 完整的数据分析功能模块，包括API调用统计、趋势分析、智能洞察等

## 技术特点
- 前后端分离架构
- 支持多种 MCP 服务器类型（stdio、SSE、OpenAPI）
- 国际化支持（中英文）
- 响应式设计
- 实时日志流
- 数据持久化与历史分析
- 客户端IP地址显示
- 智能数据洞察与可视化

## 最新更新
- ✅ 数据分析功能模块完整实现
- ✅ 客户端IP地址显示功能
- ✅ 组级别Bearer认证配置
- ✅ 智能洞察与趋势分析
- ✅ 完善的国际化支持