# MCPHub 技术栈

## 后端技术栈
- **运行时**: Node.js (^18.0.0 || >=20.0.0)
- **语言**: TypeScript 5.2.2
- **框架**: Express.js 4.21.2
- **数据库**: PostgreSQL (用于向量搜索和数据分析，可选)
- **ORM**: TypeORM 0.3.24
- **向量搜索**: pgvector 0.2.1
- **认证**: JWT (jsonwebtoken 9.0.2)
- **密码加密**: bcryptjs 3.0.2
- **文件上传**: multer 2.0.1
- **HTTP 客户端**: axios 1.10.0
- **环境变量**: dotenv 16.3.1
- **MCP SDK**: @modelcontextprotocol/sdk 1.12.1

## 前端技术栈
- **框架**: React 19.1.0
- **构建工具**: Vite 6.3.5
- **语言**: TypeScript
- **路由**: React Router DOM 7.6.0
- **样式**: Tailwind CSS 4.0.17
- **UI 组件**: Radix <PERSON>I, Shadcn UI
- **图标**: Lucide React 0.486.0
- **国际化**: i18next 24.2.3, react-i18next 15.4.1
- **状态管理**: React Context API
- **数据可视化**: Recharts (用于数据分析图表)

## 开发工具
- **包管理器**: pnpm 10.11.0
- **代码检查**: ESLint 8.50.0
- **代码格式化**: Prettier 3.0.3
- **测试框架**: Jest 29.7.0
- **类型检查**: TypeScript
- **构建工具**: tsx 4.7.0 (开发), tsc (生产)
- **并发运行**: concurrently 9.1.2

## 数据存储
- **配置存储**: JSON 文件 (mcp_settings.json, servers.json)
- **向量数据**: PostgreSQL + pgvector (仅智能路由功能)
- **分析数据**: PostgreSQL (API调用统计、趋势分析)
- **日志**: 内存存储 + 文件系统

## 新增功能模块
- **数据分析**: 完整的API调用统计和趋势分析
- **智能洞察**: 基于数据的智能分析和建议
- **客户端追踪**: IP地址显示和用户行为分析
- **组级别认证**: 支持组级别的Bearer token配置