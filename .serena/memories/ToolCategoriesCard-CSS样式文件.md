# ToolCategoriesCard CSS样式文件

```css
/* ToolCategoriesCard.module.css */
.container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
}

.statsRow {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--color-border);
}

.statItem {
  text-align: center;
}

.statValue {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 0.25rem;
}

.statLabel {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  line-height: 1.2;
}

.content {
  display: flex;
  gap: 1.5rem;
  flex: 1;
  min-height: 0;
}

.chartSection {
  flex: 0 0 140px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.listSection {
  flex: 1;
  min-width: 0;
}

/* 饼图样式 */
.pieChart {
  display: flex;
  align-items: center;
  justify-content: center;
}

.chartContainer {
  position: relative;
  width: 120px;
  height: 120px;
}

.pieSvg {
  width: 100%;
  height: 100%;
}

.pieSlice {
  transition: opacity 0.2s ease;
  cursor: pointer;
}

.pieSlice:hover {
  opacity: 0.8;
}

.centerLabel {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.centerValue {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-text-primary);
  line-height: 1;
}

.centerText {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  margin-top: 0.25rem;
}

/* 分类列表样式 */
.categoryList {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.listHeader {
  margin-bottom: 0.75rem;
}

.listTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.listContent {
  flex: 1;
  overflow-y: auto;
  max-height: 220px;
  padding-right: 0.5rem;
}

.categoryItem {
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--color-border-light);
}

.categoryItem:last-child {
  border-bottom: none;
}

.categoryInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.categoryIndicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  min-width: 0;
}

.colorDot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.categoryName {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.categoryStats {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.categoryCount {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.categoryPercentage {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.categoryBar {
  width: 100%;
  height: 4px;
  background-color: var(--color-border-light);
  border-radius: 2px;
  overflow: hidden;
}

.categoryProgress {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .chartSection {
    flex: none;
  }
  
  .statsRow {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
  
  .statValue {
    font-size: 1.125rem;
  }
  
  .statLabel {
    font-size: 0.7rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .pieSlice {
    stroke: var(--color-background-dark, #1f2937);
  }
}
```