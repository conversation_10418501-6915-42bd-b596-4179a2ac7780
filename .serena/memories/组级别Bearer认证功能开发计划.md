# 组级别Bearer认证功能开发计划

## 功能需求
- 当前全局Bearer认证对所有组生效
- 每个组可以设置独立的Bearer key（可选）
- 组级别的key优先于全局key
- 保持向后兼容性，不影响现有功能

## 开发阶段

### 阶段1：数据模型扩展（后端）
**任务1.1：扩展组数据结构**
- 修改 `IGroup` 接口，添加 `bearerAuthKey?: string` 字段
- 更新相关类型定义

**任务1.2：更新组服务层**
- 修改 `groupService.ts` 中的创建和更新方法
- 支持组级别Bearer key的存储和管理

**任务1.3：更新认证中间件**
- 修改 `auth.ts` 中的 `validateBearerAuth` 函数
- 实现组级别认证优先级逻辑：组key > 全局key
- 修改 `sseService.ts` 中的认证验证逻辑

### 阶段2：API接口扩展（后端）
**任务2.1：扩展组管理API**
- 更新组创建/更新接口，支持Bearer key参数
- 添加组Bearer key的单独管理接口

**任务2.2：更新认证验证逻辑**
- 在所有需要Bearer认证的地方实现组级别验证
- 确保认证优先级正确

### 阶段3：前端界面开发
**任务3.1：扩展组创建表单**
- 修改 `AddGroupForm.tsx`，添加Bearer key设置选项
- 添加可选的Bearer key输入框和说明

**任务3.2：扩展组编辑功能**
- 更新组编辑界面，支持Bearer key的修改
- 添加Bearer key的显示和管理功能

**任务3.3：更新组管理界面**
- 在组卡片中显示是否设置了独立Bearer key
- 添加相关的状态指示

### 阶段4：测试和验证
**任务4.1：功能测试**
- 测试组级别Bearer认证的优先级
- 验证向后兼容性
- 测试各种认证场景

**任务4.2：集成测试**
- 测试SSE连接的组级别认证
- 测试工具调用的认证流程
- 验证全局和组级别认证的协同工作

### 阶段5：文档和优化
**任务5.1：更新文档**
- 更新API文档
- 添加功能使用说明

**任务5.2：性能优化**
- 优化认证查找逻辑
- 确保认证性能不受影响

## 技术实现要点

### 数据结构设计
```typescript
export interface IGroup {
  id: string;
  name: string;
  description?: string;
  servers: string[];
  bearerAuthKey?: string; // 新增：组级别Bearer key
}
```

### 认证优先级逻辑
```typescript
// 伪代码
function validateBearerAuth(req, group?) {
  if (group && group.bearerAuthKey) {
    return validateToken(req, group.bearerAuthKey);
  }
  return validateToken(req, globalBearerAuthKey);
}
```

### 前端表单扩展
- 在组创建/编辑表单中添加可选的Bearer key设置
- 提供清晰的说明和验证

## 兼容性保证
1. **向后兼容**：现有组不设置Bearer key时，继续使用全局key
2. **渐进式升级**：用户可以选择性地为组设置独立key
3. **配置灵活性**：支持全局禁用Bearer认证的现有逻辑

## 开发顺序
按阶段顺序执行：数据模型 → API接口 → 前端界面 → 测试验证 → 文档优化