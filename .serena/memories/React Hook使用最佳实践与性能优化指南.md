# React Hook使用最佳实践与性能优化指南

## 概述
本文档基于MCPHub项目中趋势分析死循环Bug的修复经验，总结了React Hook使用的最佳实践，旨在帮助开发团队避免类似的性能问题。

## Hook依赖项管理

### 核心原则
1. **依赖项必须真正稳定**: 避免在依赖数组中使用会频繁变化的值
2. **最小化依赖**: 只包含真正需要的依赖项
3. **优先使用默认配置**: 避免传入会变化的配置对象

### useCallback 最佳实践

#### ❌ 错误示例 - 会导致死循环
```typescript
const TrendsTab: React.FC = () => {
  // 这些回调函数每次渲染都会重新创建
  const onSuccess = useCallback(() => {
    console.log('Data loaded successfully');
  }, []); // 空依赖数组但函数仍会重新创建

  const onError = useCallback((error: any) => {
    console.error('Failed to load data:', error);
  }, []);

  // 这个对象每次都会重新创建，因为依赖的回调函数变化了
  const options = useMemo(() => ({
    onSuccess,
    onError
  }), [onSuccess, onError]);

  // 导致Hook重新初始化，形成死循环
  const { data, loading } = useDataHook(options);
};
```

#### ✅ 正确示例 - 稳定的依赖项
```typescript
const TrendsTab: React.FC = () => {
  // 方案1: 使用默认配置，避免传入会变化的参数
  const { data, loading, error } = useDataHook();

  // 方案2: 如果必须使用回调，确保它们真正稳定
  const stableCallback = useCallback((result: any) => {
    // 使用ref或其他稳定的方式处理
  }, []); // 确保依赖项真正为空

  // 方案3: 将回调移到Hook外部
  const handleSuccess = (result: any) => {
    console.log('Data loaded successfully');
  };
};
```

### useMemo 最佳实践

#### ❌ 错误示例
```typescript
// 对象每次都会重新创建，因为依赖项不稳定
const config = useMemo(() => ({
  onSuccess: () => console.log('success'),
  onError: (err) => console.error(err),
  retryCount: 3
}), []); // 依赖项为空但对象仍会变化
```

#### ✅ 正确示例
```typescript
// 方案1: 将稳定的配置移到组件外部
const DEFAULT_CONFIG = {
  retryCount: 3,
  timeout: 5000
};

const Component = () => {
  // 直接使用稳定的配置
  const { data } = useDataHook(DEFAULT_CONFIG);
};

// 方案2: 只对真正需要计算的值使用useMemo
const Component = ({ items }: { items: Item[] }) => {
  const expensiveValue = useMemo(() => {
    return items.reduce((sum, item) => sum + item.value, 0);
  }, [items]); // items是真正的依赖项
};
```

### useEffect 最佳实践

#### ❌ 错误示例
```typescript
const Component = () => {
  const [data, setData] = useState(null);
  
  // 这个函数每次渲染都会重新创建
  const loadData = useCallback(async () => {
    const result = await fetchData();
    setData(result);
  }, []); // 依赖项不完整

  // 导致无限循环
  useEffect(() => {
    loadData();
  }, [loadData]); // loadData每次都变化
};
```

#### ✅ 正确示例
```typescript
const Component = () => {
  const [data, setData] = useState(null);
  
  // 方案1: 将异步逻辑直接放在useEffect中
  useEffect(() => {
    const loadData = async () => {
      const result = await fetchData();
      setData(result);
    };
    
    loadData();
  }, []); // 真正的空依赖

  // 方案2: 使用useCallback但包含所有依赖项
  const [filter, setFilter] = useState('');
  
  const loadData = useCallback(async () => {
    const result = await fetchData(filter);
    setData(result);
  }, [filter]); // 包含真正的依赖项

  useEffect(() => {
    loadData();
  }, [loadData]);
};
```

## 自定义Hook设计原则

### 1. 最小化参数
```typescript
// ❌ 避免复杂的配置对象
interface ComplexOptions {
  onSuccess?: () => void;
  onError?: (error: any) => void;
  onRetry?: (count: number) => void;
  retryConfig?: RetryConfig;
}

const useDataHook = (options: ComplexOptions) => {
  // 复杂的依赖管理...
};

// ✅ 优先使用简单参数和默认配置
const useDataHook = (url?: string, enabled: boolean = true) => {
  // 内部使用默认配置
  const defaultConfig = {
    retryCount: 3,
    timeout: 5000
  };
  
  // 简单稳定的逻辑
};
```

### 2. 返回稳定的对象
```typescript
// ❌ 每次返回新对象
const useDataHook = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  return {
    data,
    loading,
    refetch: () => loadData() // 每次都是新函数
  };
};

// ✅ 使用useCallback稳定函数引用
const useDataHook = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  const refetch = useCallback(() => {
    loadData();
  }, []); // 确保稳定
  
  return useMemo(() => ({
    data,
    loading,
    refetch
  }), [data, loading, refetch]);
};
```

## 性能监控与调试

### 1. 识别死循环的方法
```typescript
// 添加调试日志
useEffect(() => {
  console.log('Effect triggered', { dependency1, dependency2 });
  // 如果这个日志重复出现，说明有死循环
}, [dependency1, dependency2]);

// 使用React DevTools Profiler
// 查看组件重新渲染的频率和原因
```

### 2. 性能监控工具
```typescript
// 开发环境下的性能监控
if (process.env.NODE_ENV === 'development') {
  const startTime = performance.now();
  
  useEffect(() => {
    const endTime = performance.now();
    if (endTime - startTime > 100) {
      console.warn('Slow effect detected:', endTime - startTime);
    }
  });
}
```

## 常见反模式与解决方案

### 1. 在依赖数组中使用对象
```typescript
// ❌ 对象每次都不同
const config = { url: '/api/data', method: 'GET' };
useEffect(() => {
  fetchData(config);
}, [config]); // config每次都是新对象

// ✅ 使用基本类型或稳定引用
const url = '/api/data';
const method = 'GET';
useEffect(() => {
  fetchData({ url, method });
}, [url, method]); // 基本类型稳定
```

### 2. 在Hook中创建函数
```typescript
// ❌ 每次都创建新函数
const useDataHook = (onSuccess) => {
  useEffect(() => {
    fetchData().then(onSuccess); // onSuccess可能每次都不同
  }, [onSuccess]);
};

// ✅ 使用useCallback或ref
const useDataHook = (onSuccess) => {
  const onSuccessRef = useRef(onSuccess);
  onSuccessRef.current = onSuccess;
  
  useEffect(() => {
    fetchData().then((data) => onSuccessRef.current(data));
  }, []); // 不依赖onSuccess
};
```

## 代码审查检查清单

### Hook使用检查项
- [ ] useCallback的依赖数组是否包含所有使用的变量？
- [ ] useMemo的依赖数组是否真正必要？
- [ ] useEffect的依赖数组是否会导致无限循环？
- [ ] 自定义Hook的参数是否过于复杂？
- [ ] 是否可以使用更简单的默认配置？

### 性能检查项
- [ ] 组件是否有不必要的重新渲染？
- [ ] Hook的返回值是否稳定？
- [ ] 是否有重复的API调用？
- [ ] 控制台是否有重复的日志输出？

## 实际案例：趋势分析死循环修复

### 问题描述
MCPHub趋势分析页面出现死循环，"Trends data loaded successfully"日志重复输出4175次。

### 根本原因
```typescript
// 问题代码
const onSuccess = useCallback(() => {
  console.log('Trends data loaded successfully');
}, []); // 每次渲染都重新创建

const options = useMemo(() => ({
  onSuccess,
  onError,
  onRetry
}), [onSuccess, onError, onRetry]); // options每次都变化

const { error, isLoading } = useErrorHandler(options); // 导致Hook重新初始化
```

### 修复方案
```typescript
// 修复后的代码
const { error, isLoading, executeWithRetry, clearError } = useErrorHandler();
// 使用默认配置，避免传入会变化的参数
```

### 经验总结
1. **简化优于复杂**: 优先使用默认配置而不是复杂的自定义选项
2. **稳定性第一**: 确保Hook的依赖项真正稳定
3. **及时监控**: 建立日志监控机制，及时发现异常
4. **代码审查**: 重点检查Hook的依赖项管理

## 工具推荐

### 开发工具
- **React DevTools**: 监控组件重新渲染
- **React DevTools Profiler**: 性能分析
- **ESLint Plugin React Hooks**: 静态检查Hook规则

### 监控工具
```typescript
// 自定义Hook监控
const useHookMonitor = (hookName: string, dependencies: any[]) => {
  const renderCount = useRef(0);
  renderCount.current++;
  
  useEffect(() => {
    if (renderCount.current > 10) {
      console.warn(`${hookName} rendered ${renderCount.current} times`);
    }
  });
  
  useEffect(() => {
    console.log(`${hookName} dependencies changed:`, dependencies);
  }, dependencies);
};
```

## 总结

React Hook的正确使用对于应用性能至关重要。通过遵循本文档的最佳实践，可以有效避免死循环、内存泄漏等性能问题。记住：**简单稳定的代码总是比复杂灵活的代码更可靠**。