# 开发环境网络访问配置

## 问题描述
开发环境web无法访问

## 解决方案
在Vite配置中添加host配置允许外部访问

## 具体修改
**文件**: `frontend/vite.config.ts`
**修改内容**: 在server配置中添加 `host: '0.0.0.0'`

```typescript
server: {
  host: '0.0.0.0', // 允许外部访问
  port: 5000,
  proxy: {
    // ... 其他配置
  }
}
```

## 访问地址
- **本地访问**: http://localhost:5000/
- **网络访问**: http://*************:5000/
- **后端服务器**: http://localhost:3001/

## 验证结果
✅ 前端和后端服务器都正常运行
✅ 本地和网络访问都可以正常连接
✅ HTTP响应状态正常 (200 OK)