# 项目文档管理规范

## 文档组织方式
- 每个开发任务创建一个综合性的Markdown文档
- 文档位置：`docs/` 目录下
- 文档命名：使用kebab-case格式，如 `feature-name-development.md`

## 已完成项目文档
1. **Dashboard API调用统计功能**: `docs/dashboard-api-stats-feature.md`
   - 状态：✅ 已完成
   - 包含：需求分析、开发进度、技术实现、测试结果、完成总结
   - 开发时间：2025年7月9日，6-8小时

## 文档结构模板
1. 📋 项目概述（需求描述、核心要求）
2. 🎯 功能规划（阶段划分、任务分解）
3. 🚀 开发进度记录（时间线、完成状态）
4. 🔧 技术实现细节（代码示例、架构说明）
5. 🌍 国际化支持（翻译文件）
6. 🧪 测试结果（测试覆盖、执行结果）
7. 📁 代码更改文件列表
8. ✨ 实现的功能特性
9. 🎨 UI展示效果
10. 📖 使用方法
11. 🔄 与现有代码的交互
12. ⚠️ 注意事项和限制
13. 🚀 后续扩展方向
14. 📊 项目总结

## 文档维护原则
- 一个任务一个文档，避免分散的记忆文件
- 文档应包含从需求到完成的完整记录
- 使用清晰的Markdown格式和适当的emoji标识
- 及时更新文档状态和进度
- 便于项目管理和后续参考