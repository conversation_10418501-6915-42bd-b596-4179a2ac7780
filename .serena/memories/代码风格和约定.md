# MCPHub 代码风格和约定

## TypeScript 配置
- **目标版本**: ES2022
- **模块系统**: ESNext
- **严格模式**: 启用
- **类型检查**: 严格类型检查
- **装饰器**: 启用实验性装饰器支持

## 代码风格
### ESLint 配置
- 基于 TypeScript ESLint 推荐配置
- 扩展: `@typescript-eslint/recommended`
- 文件扩展名: `.ts`, `.tsx`

### Prettier 配置
- **缩进**: 2 空格
- **引号**: 单引号
- **分号**: 必须
- **尾随逗号**: ES5 兼容
- **行宽**: 100 字符

## 命名约定
### 文件命名
- **组件文件**: PascalCase (如 `ServerCard.tsx`)
- **服务文件**: camelCase (如 `mcpService.ts`)
- **类型文件**: camelCase (如 `index.ts`)
- **配置文件**: kebab-case 或 dot notation

### 变量和函数命名
- **变量**: camelCase
- **函数**: camelCase
- **常量**: UPPER_SNAKE_CASE
- **类**: PascalCase
- **接口**: PascalCase (通常以 I 开头，如 `IUser`)
- **类型**: PascalCase

## 项目约定
### 导入顺序
1. Node.js 内置模块
2. 第三方库
3. 项目内部模块 (使用相对路径)

### 组件结构
```typescript
// React 组件结构示例
interface ComponentProps {
  // props 定义
}

const Component: React.FC<ComponentProps> = ({ prop1, prop2 }) => {
  // hooks
  // 事件处理函数
  // 渲染逻辑
  return <div>...</div>;
};

export default Component;
```

### 服务层结构
```typescript
// 服务函数结构示例
export const serviceFunctionName = async (
  param1: Type1,
  param2: Type2
): Promise<ReturnType> => {
  try {
    // 业务逻辑
    return result;
  } catch (error) {
    console.error('Error message:', error);
    throw error;
  }
};
```

## React Hook使用规范

### useCallback使用原则
- **谨慎使用**: 不是所有函数都需要useCallback
- **完整依赖**: 依赖数组必须包含所有使用的变量
- **稳定优先**: 优先保证依赖项的稳定性

```typescript
// ✅ 正确使用
const Component = ({ userId }: { userId: string }) => {
  const handleClick = useCallback((id: string) => {
    console.log('Clicked:', id);
  }, []); // 无外部依赖，真正稳定
  
  const loadData = useCallback(async () => {
    const result = await fetchData(userId);
    setData(result);
  }, [userId]); // 包含所有依赖
};

// ❌ 避免的模式
const Component = () => {
  const handleClick = useCallback(() => {
    setCount(count + 1); // 缺少count依赖
  }, []); // 依赖项不完整
};
```

### useMemo使用原则
- **昂贵计算**: 只对真正昂贵的计算使用useMemo
- **引用稳定**: 用于保持对象引用的稳定性
- **避免滥用**: 不要对简单计算使用useMemo

```typescript
// ✅ 适合使用useMemo
const expensiveValue = useMemo(() => {
  return items.reduce((sum, item) => sum + complexCalculation(item), 0);
}, [items]);

// ❌ 过度优化
const simpleValue = useMemo(() => {
  return name.toUpperCase();
}, [name]); // 简单计算不需要useMemo
```

### useEffect使用原则
- **完整依赖**: 包含所有在effect中使用的变量
- **稳定引用**: 避免在依赖数组中使用不稳定的引用
- **清理函数**: 及时清理副作用

```typescript
// ✅ 正确的useEffect使用
useEffect(() => {
  let cancelled = false;
  
  const loadData = async () => {
    const result = await fetch(url);
    if (!cancelled) {
      setData(result);
    }
  };
  
  loadData();
  
  return () => {
    cancelled = true;
  };
}, [url]); // 完整的依赖项
```

### 自定义Hook设计原则
- **单一职责**: 每个Hook只负责一个功能
- **参数简化**: 避免复杂的配置对象
- **返回稳定**: 确保返回值的引用稳定

```typescript
// ✅ 良好的Hook设计
const useApiData = <T>(url: string, enabled: boolean = true) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  
  const refetch = useCallback(async () => {
    // 加载逻辑
  }, [url, enabled]);
  
  return useMemo(() => ({
    data,
    loading,
    refetch
  }), [data, loading, refetch]);
};
```

## 性能优化规范

### 组件渲染优化
- 合理使用React.memo
- 避免内联对象和函数
- 优化状态结构设计

```typescript
// ✅ 推荐的模式
const ITEM_STYLE = { margin: 10 }; // 提取到组件外部

const Component = React.memo<Props>(({ items, onItemClick }) => {
  const handleClick = useCallback((id: string) => {
    onItemClick(id);
  }, [onItemClick]);
  
  return (
    <div style={ITEM_STYLE}>
      {items.map(item => (
        <Item key={item.id} onClick={handleClick} />
      ))}
    </div>
  );
});
```

### 避免常见性能陷阱
- 不要在渲染过程中直接调用setState
- 避免在useEffect中创建无限循环
- 合理使用依赖数组

```typescript
// ❌ 避免的模式
const Component = () => {
  const [count, setCount] = useState(0);
  
  // 直接在渲染中更新状态，导致无限循环
  if (someCondition) {
    setCount(count + 1);
  }
};

// ✅ 正确的模式
const Component = () => {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    if (someCondition) {
      setCount(prev => prev + 1);
    }
  }, [someCondition]);
};
```

## 错误处理规范

### 错误边界使用
```typescript
// 错误边界组件
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true };
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    return this.props.children;
  }
}
```

### 异步错误处理
```typescript
// 统一的异步错误处理
const useAsyncOperation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const execute = useCallback(async (operation: () => Promise<any>) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await operation();
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);
  
  return { execute, loading, error };
};
```

## 注释和文档
- **函数注释**: 使用 JSDoc 格式
- **复杂逻辑**: 添加行内注释说明
- **TODO**: 使用 `// TODO:` 标记待办事项
- **类型定义**: 为复杂类型添加注释
- **Hook说明**: 自定义Hook需要详细的使用说明

```typescript
/**
 * 自定义Hook用于管理API数据加载
 * @param url - API端点URL
 * @param enabled - 是否启用自动加载
 * @returns 包含数据、加载状态和重新加载函数的对象
 */
const useApiData = <T>(url: string, enabled: boolean = true) => {
  // Hook实现...
};
```

## 错误处理
- 使用 try-catch 包装异步操作
- 记录错误日志到控制台
- 向上抛出错误供调用者处理
- 前端使用 Toast 显示用户友好的错误信息
- 实现错误边界捕获组件错误

## 代码审查检查清单

### React Hook检查项
- [ ] useCallback的依赖数组是否包含所有使用的变量？
- [ ] useMemo是否真正必要，还是过度优化？
- [ ] useEffect是否可能导致无限循环？
- [ ] 自定义Hook的设计是否简洁合理？
- [ ] Hook的返回值是否稳定？

### 性能检查项
- [ ] 组件是否有不必要的重新渲染？
- [ ] 是否正确使用了React.memo？
- [ ] 是否避免了内联对象和函数？
- [ ] 状态结构是否合理？
- [ ] 是否有内存泄漏风险？

### 错误处理检查项
- [ ] 是否有适当的错误边界？
- [ ] 异步操作是否有错误处理？
- [ ] 是否有用户友好的错误提示？
- [ ] 错误是否被正确记录？

## 工具配置

### ESLint配置
```javascript
module.exports = {
  extends: [
    '@typescript-eslint/recommended',
    'plugin:react-hooks/recommended'
  ],
  rules: {
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    'react/jsx-no-bind': 'warn',
    'react/jsx-no-constructed-context-values': 'warn',
    'no-console': ['warn', { allow: ['warn', 'error'] }]
  }
};
```

### TypeScript配置
```json
{
  "compilerOptions": {
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true
  }
}
```

## 总结

本规范基于MCPHub项目的实际开发经验，特别是趋势分析死循环Bug的修复经验，重点关注：

1. **Hook使用的正确性**: 避免死循环和性能问题
2. **代码的可维护性**: 清晰的结构和命名
3. **性能优化**: 合理的优化策略，避免过度优化
4. **错误处理**: 完善的错误处理机制
5. **代码质量**: 通过工具和规范确保代码质量

遵循这些规范可以有效提高代码质量，避免常见的性能问题和Bug。