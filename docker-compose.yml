version: '3.8'

services:
  # PostgreSQL数据库服务 (使用pgvector扩展)
  postgres:
    image: pgvector/pgvector:pg16
    container_name: mcphub-postgres
    environment:
      POSTGRES_DB: mcphub
      POSTGRES_USER: mcphub_user
      POSTGRES_PASSWORD: mcphub_password
      # 设置PostgreSQL配置优化
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      # 数据持久化
      - postgres_data:/var/lib/postgresql/data
      # 初始化脚本
      - ./docker/init-db:/docker-entrypoint-initdb.d
      # PostgreSQL配置
      - ./docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mcphub_user -d mcphub"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - mcphub-network

  # MCPHub应用服务
  mcphub:
    build: 
      context: .
      dockerfile: Dockerfile
      args:
        - HTTP_PROXY=${HTTP_PROXY:-}
        - HTTPS_PROXY=${HTTPS_PROXY:-}
        - REQUEST_TIMEOUT=${REQUEST_TIMEOUT:-60000}
        - BASE_PATH=${BASE_PATH:-}
        - INSTALL_EXT=${INSTALL_EXT:-false}
    container_name: mcphub-app
    environment:
      # 数据库连接
      - DB_URL=******************************************************/mcphub
      # 应用配置
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=${PORT:-3000}
      - REQUEST_TIMEOUT=${REQUEST_TIMEOUT:-60000}
      - BASE_PATH=${BASE_PATH:-}
      # 智能路由配置
      - ENABLE_SMART_ROUTING=${ENABLE_SMART_ROUTING:-false}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - OPENAI_API_BASE_URL=${OPENAI_API_BASE_URL:-}
    ports:
      - "${PORT:-3000}:${PORT:-3000}"
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    volumes:
      # 配置文件挂载
      - ./mcp_settings.json:/app/mcp_settings.json:ro
      - ./servers.json:/app/servers.json:ro
      # 日志目录
      - ./logs:/app/logs
    networks:
      - mcphub-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PORT:-3000}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存服务 (可选，为后续功能预留)
  redis:
    image: redis:7-alpine
    container_name: mcphub-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - mcphub-network
    profiles:
      - full  # 使用profile控制，默认不启动

# 数据卷定义
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

# 网络定义
networks:
  mcphub-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
