# MCPHub 生产环境部署指南

## 🚀 快速开始

### 方式一：使用 npm/pnpm 脚本（推荐）

```bash
# 构建并启动生产环境
pnpm run deploy

# 或者分步执行
pnpm build
pnpm run start:prod
```

### 方式二：使用启动脚本

```bash
# 使用启动脚本
./scripts/start-production.sh

# 或者使用部署脚本
./scripts/deploy-production.sh
```

### 方式三：使用 PM2 进程管理

```bash
# 安装 PM2（如果未安装）
npm install -g pm2

# 使用 PM2 启动
pnpm run start:pm2

# 或者使用部署脚本
./scripts/deploy-production.sh --pm2

# PM2 管理命令
pnpm run stop:pm2      # 停止服务
pnpm run restart:pm2   # 重启服务
pnpm run logs:pm2      # 查看日志
```

### 方式四：使用 Docker 部署

```bash
# 使用 Docker Compose
docker-compose -f docker-compose.prod.yml up -d

# 或者使用部署脚本
./scripts/deploy-production.sh --docker
```

## 📋 可用的 npm 脚本

| 脚本 | 描述 |
|------|------|
| `pnpm run start:prod` | 启动生产环境服务器 |
| `pnpm run start:pm2` | 使用 PM2 启动服务 |
| `pnpm run stop:pm2` | 停止 PM2 服务 |
| `pnpm run restart:pm2` | 重启 PM2 服务 |
| `pnpm run logs:pm2` | 查看 PM2 日志 |
| `pnpm run deploy` | 构建并启动生产环境 |

## 🔧 环境变量

| 变量名 | 默认值 | 描述 |
|--------|--------|------|
| `NODE_ENV` | `production` | 运行环境 |
| `PORT` | `8080` | 服务器端口 |
| `DATABASE_URL` | - | PostgreSQL 数据库连接字符串 |

## 📁 文件结构

```
mcphub/
├── scripts/
│   ├── start-production.sh     # 生产环境启动脚本
│   └── deploy-production.sh    # 生产环境部署脚本
├── ecosystem.config.js         # PM2 配置文件
├── docker-compose.prod.yml     # Docker Compose 生产配置
├── nginx.conf                  # Nginx 反向代理配置
├── Dockerfile                  # Docker 镜像构建文件
└── logs/                       # 日志目录
```

## 🐳 Docker 部署详情

### 服务组件

- **mcphub**: 主应用服务
- **postgres**: PostgreSQL 数据库（带 pgvector 扩展）
- **nginx**: 反向代理和负载均衡

### 端口映射

- `80`: Nginx HTTP 端口
- `443`: Nginx HTTPS 端口（需要 SSL 证书）
- `8080`: 直接访问应用端口
- `5432`: PostgreSQL 数据库端口

### 数据持久化

- PostgreSQL 数据存储在 `postgres_data` 卷中
- 应用日志映射到 `./logs` 目录

## 🔒 安全配置

### SSL/TLS 配置

1. 将 SSL 证书放在 `ssl/` 目录下
2. 更新 `nginx.conf` 中的证书路径
3. 重启 Nginx 服务

### 数据库安全

1. 修改默认数据库密码
2. 限制数据库访问权限
3. 启用数据库 SSL 连接

## 📊 监控和日志

### 日志文件

- `logs/combined.log`: 综合日志
- `logs/out.log`: 标准输出日志
- `logs/error.log`: 错误日志

### 健康检查

- HTTP 健康检查: `GET /api/health`
- Docker 健康检查: 自动配置
- PM2 进程监控: 自动重启

### 性能监控

```bash
# PM2 监控
pm2 monit

# Docker 监控
docker stats mcphub-prod

# 系统资源监控
htop
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   lsof -i :8080
   pkill -f "node dist/index.js"
   ```

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接字符串
   - 检查防火墙设置

3. **构建失败**
   ```bash
   rm -rf node_modules dist frontend/dist
   pnpm install
   pnpm build
   ```

### 日志查看

```bash
# 实时查看日志
tail -f logs/combined.log

# PM2 日志
pm2 logs mcphub

# Docker 日志
docker-compose -f docker-compose.prod.yml logs -f
```

## 🔄 更新部署

```bash
# 拉取最新代码
git pull origin main

# 重新部署
./scripts/deploy-production.sh

# 或者使用 PM2
pm2 restart mcphub

# 或者使用 Docker
docker-compose -f docker-compose.prod.yml up -d --build
```

## 📞 支持

如果遇到问题，请检查：

1. 系统要求是否满足
2. 依赖是否正确安装
3. 环境变量是否正确设置
4. 端口是否被占用
5. 日志文件中的错误信息
