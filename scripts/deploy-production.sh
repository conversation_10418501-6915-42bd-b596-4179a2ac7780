#!/bin/bash

# MCPHub 生产环境部署脚本
# 使用方法: ./scripts/deploy-production.sh [--docker|--pm2|--direct]

set -e

DEPLOY_METHOD=${1:-direct}

echo "🚀 MCPHub 生产环境部署"
echo "部署方式: $DEPLOY_METHOD"
echo "=========================="

# 创建必要的目录
mkdir -p logs
mkdir -p ssl

case $DEPLOY_METHOD in
    --docker)
        echo "🐳 使用 Docker 部署..."

        # 检查 Docker 是否安装
        if ! command -v docker &> /dev/null; then
            echo "❌ Docker 未安装，请先安装 Docker"
            exit 1
        fi

        # 检查 Docker Compose 是否安装
        if ! command -v docker-compose &> /dev/null; then
            echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
            exit 1
        fi

        # 创建必要的目录
        echo "📁 创建必要的目录..."
        mkdir -p docker/init-db docker/postgres

        # 检查是否需要清理数据
        if [[ "$2" == "--clean" ]]; then
            echo "🧹 清理旧数据卷..."
            docker-compose -f docker-compose.prod.yml down -v --remove-orphans
            docker volume rm mcphub_postgres_data 2>/dev/null || true
        else
            # 停止现有容器
            echo "🛑 停止现有容器..."
            docker-compose -f docker-compose.prod.yml down --remove-orphans || true
        fi

        # 构建并启动
        echo "🔨 构建并启动容器..."
        docker-compose -f docker-compose.prod.yml up -d --build

        # 等待数据库启动
        echo "⏳ 等待数据库启动..."
        timeout=60
        while [ $timeout -gt 0 ]; do
            if docker-compose -f docker-compose.prod.yml exec -T postgres pg_isready -U mcphub -d mcphub &>/dev/null; then
                echo "✅ 数据库已就绪！"
                break
            fi
            echo "⏳ 等待数据库... ($timeout 秒剩余)"
            sleep 2
            timeout=$((timeout - 2))
        done

        if [ $timeout -le 0 ]; then
            echo "❌ 数据库启动超时"
            docker-compose -f docker-compose.prod.yml logs postgres
            exit 1
        fi

        # 等待MCPHub启动
        echo "⏳ 等待MCPHub启动..."
        timeout=60
        while [ $timeout -gt 0 ]; do
            if curl -f http://localhost:8080/api/health &>/dev/null; then
                echo "✅ MCPHub已就绪！"
                break
            fi
            echo "⏳ 等待MCPHub... ($timeout 秒剩余)"
            sleep 2
            timeout=$((timeout - 2))
        done

        if [ $timeout -le 0 ]; then
            echo "⚠️  MCPHub可能仍在启动中"
            echo "📋 查看日志: docker-compose -f docker-compose.prod.yml logs mcphub"
        fi

        # 检查服务状态
        echo "📊 服务状态:"
        docker-compose -f docker-compose.prod.yml ps

        echo ""
        echo "✅ Docker 部署完成！"
        echo "🌐 MCPHub Web界面: http://localhost:8080"
        echo "🔗 Nginx代理: http://localhost:80"
        echo "🗄️  PostgreSQL数据库: localhost:5432"
        echo ""
        echo "📋 常用命令:"
        echo "  查看日志: docker-compose -f docker-compose.prod.yml logs -f"
        echo "  停止服务: docker-compose -f docker-compose.prod.yml down"
        echo "  重启服务: docker-compose -f docker-compose.prod.yml restart"
        echo "  清理重部署: $0 --docker --clean"
        ;;
        
    --pm2)
        echo "⚡ 使用 PM2 部署..."
        
        # 检查 PM2 是否安装
        if ! command -v pm2 &> /dev/null; then
            echo "📦 安装 PM2..."
            npm install -g pm2
        fi
        
        # 构建应用
        echo "🔨 构建应用..."
        pnpm build
        
        # 停止现有进程
        echo "🛑 停止现有进程..."
        pm2 stop mcphub || true
        pm2 delete mcphub || true
        
        # 启动应用
        echo "🚀 启动应用..."
        pm2 start ecosystem.config.js --env production
        
        # 保存 PM2 配置
        pm2 save
        pm2 startup
        
        echo "✅ PM2 部署完成！"
        echo "🌐 访问地址: http://localhost:8080"
        echo "📊 查看日志: pm2 logs mcphub"
        echo "🔄 重启服务: pm2 restart mcphub"
        ;;
        
    --direct|*)
        echo "🎯 直接部署..."
        
        # 构建应用
        echo "🔨 构建应用..."
        pnpm build
        
        # 检查端口
        PORT=8080
        if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
            echo "⚠️  端口 $PORT 已被占用，正在停止现有进程..."
            pkill -f "node dist/index.js" || true
            sleep 2
        fi
        
        # 启动服务
        echo "🚀 启动服务..."
        ./scripts/start-production.sh &
        
        # 等待服务启动
        echo "⏳ 等待服务启动..."
        sleep 5
        
        # 检查服务状态
        if curl -f http://localhost:8080/api/health > /dev/null 2>&1; then
            echo "✅ 服务启动成功！"
        else
            echo "❌ 服务启动失败，请检查日志"
            exit 1
        fi
        
        echo "✅ 直接部署完成！"
        echo "🌐 访问地址: http://localhost:8080"
        ;;
esac

echo ""
echo "🎉 部署完成！"
echo "📝 查看日志: tail -f logs/combined.log"
echo "🔧 配置文件: mcp_settings.json"
echo "🛑 停止服务: pkill -f 'node dist/index.js' (直接部署) 或 pm2 stop mcphub (PM2) 或 docker-compose -f docker-compose.prod.yml down (Docker)"
