#!/bin/bash

# MCPHub生产环境Docker部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_message() {
    echo -e "${GREEN}[MCPHub-PROD]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 检查生产环境配置
check_production_config() {
    print_message "检查生产环境配置..."
    
    # 检查必要的环境变量
    local required_vars=("DB_URL" "NODE_ENV")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        print_error "缺少必要的环境变量: ${missing_vars[*]}"
        print_info "请在.env文件中配置这些变量"
        exit 1
    fi
    
    # 检查NODE_ENV是否为production
    if [ "$NODE_ENV" != "production" ]; then
        print_warning "NODE_ENV不是production，当前值: $NODE_ENV"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 构建应用镜像
build_application() {
    print_message "构建应用镜像..."
    docker-compose build mcphub
    print_message "应用镜像构建完成"
}

# 启动生产环境
start_production() {
    print_message "启动生产环境..."
    
    # 启动所有服务
    if [ "$1" = "--with-redis" ]; then
        docker-compose --profile full up -d
    else
        docker-compose up -d
    fi
    
    print_info "等待服务启动..."
    sleep 15
    
    # 检查服务健康状态
    check_services_health
}

# 检查服务健康状态
check_services_health() {
    print_message "检查服务健康状态..."
    
    local services=("postgres" "mcphub")
    local unhealthy_services=()
    
    for service in "${services[@]}"; do
        local health_status=$(docker-compose ps -q $service | xargs docker inspect --format='{{.State.Health.Status}}' 2>/dev/null || echo "no-healthcheck")
        
        if [ "$health_status" = "healthy" ] || [ "$health_status" = "no-healthcheck" ]; then
            print_info "✓ $service: 健康"
        else
            print_warning "✗ $service: $health_status"
            unhealthy_services+=("$service")
        fi
    done
    
    if [ ${#unhealthy_services[@]} -ne 0 ]; then
        print_warning "以下服务状态异常: ${unhealthy_services[*]}"
        print_info "查看日志: docker-compose logs [service_name]"
    else
        print_message "所有服务运行正常！"
    fi
}

# 显示部署信息
show_deployment_info() {
    print_message "部署信息："
    echo "========================================"
    
    # 获取应用端口
    local app_port=$(docker-compose port mcphub 3000 2>/dev/null | cut -d: -f2 || echo "3000")
    
    echo "🌐 应用访问地址:"
    echo "   http://localhost:${app_port}"
    echo ""
    
    echo "📊 服务状态:"
    docker-compose ps
    echo ""
    
    echo "💾 数据卷:"
    docker volume ls | grep mcphub
    echo ""
    
    print_info "有用的命令:"
    echo "• 查看所有日志: docker-compose logs -f"
    echo "• 查看应用日志: docker-compose logs -f mcphub"
    echo "• 查看数据库日志: docker-compose logs -f postgres"
    echo "• 重启应用: docker-compose restart mcphub"
    echo "• 停止所有服务: docker-compose down"
    echo "• 备份数据库: ./scripts/backup-db.sh"
}

# 备份数据库
backup_database() {
    print_message "备份数据库..."
    
    local backup_dir="./backups"
    local backup_file="mcphub_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    mkdir -p "$backup_dir"
    
    docker-compose exec -T postgres pg_dump -U mcphub_user mcphub > "$backup_dir/$backup_file"
    
    if [ $? -eq 0 ]; then
        print_message "数据库备份成功: $backup_dir/$backup_file"
    else
        print_error "数据库备份失败"
        exit 1
    fi
}

# 更新应用
update_application() {
    print_message "更新应用..."
    
    # 备份数据库
    backup_database
    
    # 重新构建镜像
    build_application
    
    # 重启应用服务
    docker-compose up -d mcphub
    
    # 等待服务启动
    sleep 10
    
    # 检查健康状态
    check_services_health
    
    print_message "应用更新完成"
}

# 清理资源
cleanup() {
    print_warning "停止所有服务..."
    docker-compose down
    
    if [ "$1" = "--volumes" ]; then
        print_warning "删除数据卷..."
        docker-compose down -v
    fi
    
    print_message "清理完成"
}

# 显示监控信息
show_monitoring() {
    print_message "系统监控信息"
    echo "========================================"
    
    echo "📈 容器资源使用:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
    echo ""
    
    echo "💽 磁盘使用:"
    docker system df
    echo ""
    
    echo "🔍 最近日志 (最后20行):"
    docker-compose logs --tail=20 mcphub
}

# 主函数
main() {
    print_message "MCPHub生产环境部署脚本"
    echo "========================================"
    
    case "${1:-deploy}" in
        "deploy")
            check_production_config
            build_application
            start_production "$2"
            show_deployment_info
            ;;
        "start")
            start_production "$2"
            show_deployment_info
            ;;
        "stop")
            cleanup
            ;;
        "restart")
            cleanup
            sleep 2
            start_production "$2"
            show_deployment_info
            ;;
        "update")
            update_application
            ;;
        "backup")
            backup_database
            ;;
        "status")
            docker-compose ps
            ;;
        "logs")
            docker-compose logs -f "${2:-mcphub}"
            ;;
        "monitor")
            show_monitoring
            ;;
        "cleanup")
            cleanup "$2"
            ;;
        "help"|"-h"|"--help")
            echo "用法: $0 [命令] [选项]"
            echo ""
            echo "命令:"
            echo "  deploy    完整部署 (构建+启动) [默认]"
            echo "  start     启动服务"
            echo "  stop      停止所有服务"
            echo "  restart   重启所有服务"
            echo "  update    更新应用 (备份+重建+重启)"
            echo "  backup    备份数据库"
            echo "  status    查看服务状态"
            echo "  logs      查看日志 [service_name]"
            echo "  monitor   显示监控信息"
            echo "  cleanup   清理资源 [--volumes]"
            echo "  help      显示此帮助信息"
            echo ""
            echo "选项:"
            echo "  --with-redis  启动Redis服务"
            echo "  --volumes     清理时删除数据卷"
            echo ""
            echo "示例:"
            echo "  $0 deploy --with-redis"
            echo "  $0 update"
            echo "  $0 logs mcphub"
            echo "  $0 cleanup --volumes"
            ;;
        *)
            print_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap cleanup INT TERM

# 执行主函数
main "$@"
