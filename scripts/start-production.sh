#!/bin/bash

# MCPHub 生产环境启动脚本
# 使用方法: ./scripts/start-production.sh

set -e

echo "🚀 启动 MCPHub 生产环境..."

# 检查是否已构建
if [ ! -d "dist" ]; then
    echo "📦 未找到构建文件，开始构建..."
    pnpm build
fi

# 检查是否已构建前端
if [ ! -d "frontend/dist" ]; then
    echo "🎨 未找到前端构建文件，开始构建前端..."
    cd frontend && pnpm build && cd ..
fi

# 创建日志目录
mkdir -p logs

# 检查端口是否被占用
PORT=${PORT:-8080}
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  端口 $PORT 已被占用，正在尝试停止现有进程..."
    pkill -f "node dist/index.js" || true
    sleep 2
fi

# 设置环境变量
export NODE_ENV=production
export PORT=$PORT

echo "🌐 启动服务器在端口 $PORT..."
echo "📊 访问地址: http://localhost:$PORT"
echo "🔧 环境: $NODE_ENV"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "=========================="

# 启动服务器
node dist/index.js
