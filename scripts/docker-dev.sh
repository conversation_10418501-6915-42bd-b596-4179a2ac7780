#!/bin/bash

# MCPHub开发环境Docker启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[MCPHub]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
}

# 检查.env文件
check_env_file() {
    if [ ! -f ".env" ]; then
        print_warning ".env文件不存在，正在创建..."
        cp .env.example .env 2>/dev/null || {
            print_error "无法创建.env文件，请手动创建"
            exit 1
        }
    fi
}

# 启动PostgreSQL数据库
start_database() {
    print_message "启动PostgreSQL数据库..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d postgres
    
    print_info "等待数据库启动..."
    sleep 10
    
    # 检查数据库是否就绪
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose exec -T postgres pg_isready -U mcphub_dev_user -d mcphub_dev &>/dev/null; then
            print_message "数据库启动成功！"
            break
        fi
        
        print_info "等待数据库就绪... (尝试 $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_error "数据库启动超时，请检查Docker日志"
        docker-compose logs postgres
        exit 1
    fi
}

# 验证数据库连接
verify_database() {
    print_message "验证数据库连接..."
    if docker-compose exec -T postgres psql -U mcphub_dev_user -d mcphub_dev -c "SELECT version();" &>/dev/null; then
        print_message "数据库连接验证成功！"
    else
        print_error "数据库连接验证失败"
        exit 1
    fi
}

# 显示数据库信息
show_database_info() {
    print_info "数据库连接信息："
    echo "  主机: localhost"
    echo "  端口: 5432"
    echo "  数据库: mcphub_dev"
    echo "  用户: mcphub_dev_user"
    echo "  密码: mcphub_dev_password"
    echo ""
    echo "连接字符串: postgresql://mcphub_dev_user:mcphub_dev_password@localhost:5432/mcphub_dev"
}

# 启动Redis (可选)
start_redis() {
    if [ "$1" = "--with-redis" ]; then
        print_message "启动Redis缓存..."
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d redis
        print_message "Redis启动成功！"
    fi
}

# 显示下一步操作
show_next_steps() {
    print_message "开发环境准备完成！"
    echo ""
    print_info "下一步操作："
    echo "1. 启动应用开发服务器："
    echo "   pnpm dev"
    echo ""
    echo "2. 或者启动后端调试模式："
    echo "   pnpm debug"
    echo ""
    echo "3. 访问应用："
    echo "   http://localhost:3000"
    echo ""
    print_info "有用的命令："
    echo "• 查看数据库日志: docker-compose logs -f postgres"
    echo "• 连接数据库: docker-compose exec postgres psql -U mcphub_dev_user -d mcphub_dev"
    echo "• 停止服务: docker-compose down"
    echo "• 重启数据库: docker-compose restart postgres"
}

# 清理函数
cleanup() {
    print_warning "正在清理Docker资源..."
    docker-compose down
    print_message "清理完成"
}

# 主函数
main() {
    print_message "MCPHub开发环境启动脚本"
    echo "========================================"
    
    # 检查依赖
    check_docker
    check_env_file
    
    # 处理命令行参数
    case "${1:-start}" in
        "start")
            start_database
            start_redis "$2"
            verify_database
            show_database_info
            show_next_steps
            ;;
        "stop")
            cleanup
            ;;
        "restart")
            cleanup
            sleep 2
            start_database
            start_redis "$2"
            verify_database
            show_database_info
            show_next_steps
            ;;
        "status")
            docker-compose ps
            ;;
        "logs")
            docker-compose logs -f postgres
            ;;
        "help"|"-h"|"--help")
            echo "用法: $0 [命令] [选项]"
            echo ""
            echo "命令:"
            echo "  start     启动开发环境 (默认)"
            echo "  stop      停止所有服务"
            echo "  restart   重启所有服务"
            echo "  status    查看服务状态"
            echo "  logs      查看数据库日志"
            echo "  help      显示此帮助信息"
            echo ""
            echo "选项:"
            echo "  --with-redis  同时启动Redis服务"
            echo ""
            echo "示例:"
            echo "  $0 start --with-redis"
            echo "  $0 restart"
            echo "  $0 stop"
            ;;
        *)
            print_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap cleanup INT TERM

# 执行主函数
main "$@"
