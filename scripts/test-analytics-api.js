#!/usr/bin/env node

/**
 * 测试Analytics API的脚本
 * 创建一些测试数据并验证API端点
 */

import { DataPersistenceService } from '../dist/services/marketService.js';

async function createTestData() {
  console.log('Creating test data for Analytics API...');

  // 确保数据库已初始化
  const { initializeDatabase } = await import('../dist/db/connection.js');
  await initializeDatabase();
  console.log('Database initialized for test script');

  const dataPersistenceService = new DataPersistenceService();
  
  // 创建一些测试调用记录 - 生成24小时的数据用于图表测试
  const testRecords = [];
  const baseDate = new Date('2025-07-16T00:00:00Z');

  // 生成24小时的测试数据，每小时多个数据点
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 15) { // 每15分钟一个数据点
      const callTime = new Date(baseDate.getTime() + hour * 60 * 60 * 1000 + minute * 60 * 1000);

      // 模拟不同的调用模式
      const isSuccess = Math.random() > 0.1; // 90%成功率
      const responseTime = isSuccess ?
        Math.floor(Math.random() * 200) + 50 : // 成功：50-250ms
        Math.floor(Math.random() * 500) + 200; // 失败：200-700ms

      // 模拟异常情况
      let isAnomaly = false;
      if (hour === 8 || hour === 14 || hour === 20) { // 在特定时间制造异常
        if (minute === 0) {
          isAnomaly = true;
        }
      }

      testRecords.push({
        serverName: `test-server-${Math.floor(Math.random() * 3) + 1}`,
        toolName: `test-tool-${Math.floor(Math.random() * 5) + 1}`,
        callTime: callTime,
        success: isAnomaly ? false : isSuccess,
        responseTime: isAnomaly ? Math.floor(Math.random() * 1000) + 500 : responseTime,
        userId: `user${Math.floor(Math.random() * 5) + 1}`,
        errorMessage: (!isSuccess || isAnomaly) ? 'Test error' : null,
        requestParams: { param: `value${Math.random()}` },
        responseData: isSuccess && !isAnomaly ? { result: 'success' } : { error: 'Test error occurred' }
      });
    }
  }

  console.log(`Generated ${testRecords.length} test records for 24 hours`);
  
  try {
    // 批量插入测试数据
    await dataPersistenceService.batchRecordCalls(testRecords);
    console.log(`Successfully created ${testRecords.length} test records`);
    
    // 验证数据插入
    const queryParams = {
      timeRange: {
        startTime: '2025-07-16T00:00:00Z',
        endTime: '2025-07-17T00:00:00Z'
      },
      pagination: {
        page: 1,
        pageSize: 10
      }
    };
    
    const result = await dataPersistenceService.queryHistory(queryParams);
    console.log('Query result:', {
      totalRecords: result.data.length,
      totalCalls: result.aggregates.totalCalls,
      successRate: result.aggregates.successRate,
      avgResponseTime: result.aggregates.avgResponseTime
    });
    
    return true;
  } catch (error) {
    console.error('Error creating test data:', error);
    return false;
  }
}

async function testApiEndpoints() {
  console.log('\nTesting API endpoints...');
  
  const baseUrl = 'http://127.0.0.1:3000';
  
  // 首先登录获取token
  const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      username: 'admin',
      password: 'admin123'
    })
  });
  
  const loginData = await loginResponse.json();
  if (!loginData.success) {
    console.error('Login failed:', loginData);
    return false;
  }
  
  const token = loginData.token;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };
  
  // 测试筛选选项端点
  console.log('Testing /api/analytics/filters...');
  const filtersResponse = await fetch(`${baseUrl}/api/analytics/filters`, { headers });
  const filtersData = await filtersResponse.json();
  console.log('Filters result:', filtersData);
  
  // 测试历史摘要端点
  console.log('\nTesting /api/analytics/summary...');
  const summaryResponse = await fetch(
    `${baseUrl}/api/analytics/summary?startTime=2025-07-16T00:00:00Z&endTime=2025-07-17T00:00:00Z`,
    { headers }
  );
  const summaryData = await summaryResponse.json();
  console.log('Summary result:', summaryData);

  // 测试历史数据查询端点
  console.log('\nTesting /api/analytics/history...');
  const historyResponse = await fetch(
    `${baseUrl}/api/analytics/history?startTime=2025-07-16T00:00:00Z&endTime=2025-07-17T00:00:00Z&page=1&pageSize=5`,
    { headers }
  );
  const historyData = await historyResponse.json();
  console.log('History result:', {
    success: historyData.success,
    dataCount: historyData.data?.length || 0,
    pagination: historyData.pagination,
    aggregates: historyData.aggregates
  });

  // 测试带筛选条件的查询
  console.log('\nTesting filtered history query...');
  const filteredResponse = await fetch(
    `${baseUrl}/api/analytics/history?startTime=2025-07-16T00:00:00Z&endTime=2025-07-17T00:00:00Z&serverNames=test-server-1&success=true&page=1&pageSize=5`,
    { headers }
  );
  const filteredData = await filteredResponse.json();
  console.log('Filtered result:', {
    success: filteredData.success,
    dataCount: filteredData.data?.length || 0,
    aggregates: filteredData.aggregates
  });
  
  return true;
}

async function main() {
  console.log('Analytics API Test Script');
  console.log('========================');
  
  try {
    // 创建测试数据
    const dataCreated = await createTestData();
    if (!dataCreated) {
      console.error('Failed to create test data');
      process.exit(1);
    }
    
    // 等待一下让数据完全写入
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 测试API端点
    const apiTested = await testApiEndpoints();
    if (!apiTested) {
      console.error('API testing failed');
      process.exit(1);
    }
    
    console.log('\n✅ All tests completed successfully!');
    console.log('\nYou can now test the Analytics API endpoints manually:');
    console.log('- GET /api/analytics/filters');
    console.log('- GET /api/analytics/summary?startTime=2025-07-16T00:00:00Z&endTime=2025-07-17T00:00:00Z');
    console.log('- GET /api/analytics/history?startTime=2025-07-16T00:00:00Z&endTime=2025-07-17T00:00:00Z&page=1&pageSize=10');
    
  } catch (error) {
    console.error('Test script failed:', error);
    process.exit(1);
  }
}

// 运行测试
main();
