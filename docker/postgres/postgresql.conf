# PostgreSQL配置文件 - 针对MCPHub优化

# 连接和认证设置
listen_addresses = '*'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# 内存设置
shared_buffers = 256MB
huge_pages = try
work_mem = 4MB
maintenance_work_mem = 64MB
autovacuum_work_mem = -1
max_stack_depth = 2MB
dynamic_shared_memory_type = posix

# 预加载库 (包含vector扩展)
shared_preload_libraries = 'vector'

# WAL设置
wal_level = replica
fsync = on
synchronous_commit = on
wal_sync_method = fsync
full_page_writes = on
wal_compression = off
wal_log_hints = off
wal_buffers = 16MB
wal_writer_delay = 200ms
commit_delay = 0
commit_siblings = 5

# 检查点设置
checkpoint_timeout = 5min
max_wal_size = 1GB
min_wal_size = 80MB
checkpoint_completion_target = 0.9
checkpoint_flush_after = 256kB
checkpoint_warning = 30s

# 归档设置
archive_mode = off

# 复制设置
max_wal_senders = 10
max_replication_slots = 10
track_commit_timestamp = off

# 查询规划器设置
enable_bitmapscan = on
enable_hashagg = on
enable_hashjoin = on
enable_indexscan = on
enable_indexonlyscan = on
enable_material = on
enable_mergejoin = on
enable_nestloop = on
enable_seqscan = on
enable_sort = on
enable_tidscan = on

# 查询规划器成本常量
seq_page_cost = 1.0
random_page_cost = 1.1  # 针对SSD优化
cpu_tuple_cost = 0.01
cpu_index_tuple_cost = 0.005
cpu_operator_cost = 0.0025
parallel_tuple_cost = 0.1
parallel_setup_cost = 1000.0
min_parallel_table_scan_size = 8MB
min_parallel_index_scan_size = 512kB
effective_cache_size = 1GB

# 日志设置
logging_collector = on
log_destination = 'stderr'
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_file_mode = 0600
log_truncate_on_rotation = off
log_rotation_age = 1d
log_rotation_size = 10MB
log_min_messages = warning
log_min_error_statement = error
log_min_duration_statement = 1000  # 记录超过1秒的查询
log_checkpoints = off
log_connections = off
log_disconnections = off
log_duration = off
log_error_verbosity = default
log_hostname = off
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_lock_waits = off
log_statement = 'none'
log_temp_files = 10MB
log_timezone = 'UTC'

# 统计设置
track_activities = on
track_counts = on
track_io_timing = off
track_functions = none
track_activity_query_size = 1024
stats_temp_directory = 'pg_stat_tmp'

# 自动清理设置
autovacuum = on
log_autovacuum_min_duration = -1
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_scale_factor = 0.1
autovacuum_freeze_max_age = 200000000
autovacuum_multixact_freeze_max_age = 400000000
autovacuum_vacuum_cost_delay = 20ms
autovacuum_vacuum_cost_limit = -1

# 客户端连接默认设置
search_path = '"$user", public'
default_tablespace = ''
temp_tablespaces = ''
check_function_bodies = on
default_transaction_isolation = 'read committed'
default_transaction_read_only = off
default_transaction_deferrable = off
session_replication_role = 'origin'
statement_timeout = 0
lock_timeout = 0
idle_in_transaction_session_timeout = 0
vacuum_freeze_min_age = 50000000
vacuum_freeze_table_age = 150000000
vacuum_multixact_freeze_min_age = 5000000
vacuum_multixact_freeze_table_age = 150000000
bytea_output = 'hex'
xmlbinary = 'base64'
xmloption = 'content'
gin_fuzzy_search_limit = 0
gin_pending_list_limit = 4MB

# 区域和格式设置
datestyle = 'iso, mdy'
intervalstyle = 'postgres'
timezone = 'UTC'
timezone_abbreviations = 'Default'
extra_float_digits = 0
client_encoding = utf8

# 锁管理
deadlock_timeout = 1s
max_locks_per_transaction = 64
max_pred_locks_per_transaction = 64
max_pred_locks_per_relation = -2
max_pred_locks_per_page = 2

# 版本和平台兼容性
array_nulls = on
backslash_quote = safe_encoding
default_with_oids = off
escape_string_warning = on
lo_compat_privileges = off
operator_precedence_warning = off
quote_all_identifiers = off
sql_inheritance = on
standard_conforming_strings = on
synchronize_seqscans = on
transform_null_equals = off

# 错误报告和日志
exit_on_error = off
restart_after_crash = on

# 向量扩展特定配置
# 这些参数可能需要根据实际使用情况调整
# vector.enable_prefilter = on
