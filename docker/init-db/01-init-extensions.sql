-- 初始化PostgreSQL扩展和基础配置
-- 这个脚本会在数据库容器首次启动时自动执行

-- 创建必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- 设置一些基础配置
ALTER SYSTEM SET shared_preload_libraries = 'vector';
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- 为向量搜索优化的配置
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- 创建数据持久化相关的表空间 (可选)
-- CREATE TABLESPACE mcphub_data LOCATION '/var/lib/postgresql/tablespaces/mcphub_data';

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE 'MCPHub数据库初始化完成';
    RAISE NOTICE '已安装扩展: uuid-ossp, vector';
    RAISE NOTICE '数据库已优化用于向量搜索和高并发访问';
END $$;
