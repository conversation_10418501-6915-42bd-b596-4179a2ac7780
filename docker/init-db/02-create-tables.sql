-- MCPHub 数据库表结构创建脚本
-- 创建所有必要的表和索引

-- 创建向量嵌入表
CREATE TABLE IF NOT EXISTS vector_embeddings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_type VARCHAR(255) NOT NULL,
    content_id VARCHAR(255) NOT NULL,
    text_content TEXT NOT NULL,
    metadata JSONB,
    embedding vector(1536),  -- OpenAI embedding dimensions
    dimensions INTEGER NOT NULL DEFAULT 1536,
    model VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建向量嵌入表索引
CREATE INDEX IF NOT EXISTS idx_vector_embeddings_content_type ON vector_embeddings(content_type);
CREATE INDEX IF NOT EXISTS idx_vector_embeddings_content_id ON vector_embeddings(content_id);
CREATE INDEX IF NOT EXISTS idx_vector_embeddings_created_at ON vector_embeddings(created_at DESC);

-- 创建向量相似度搜索索引 (HNSW)
CREATE INDEX IF NOT EXISTS idx_vector_embeddings_embedding_hnsw 
ON vector_embeddings USING hnsw (embedding vector_cosine_ops);

-- 创建MCP调用日志表
CREATE TABLE IF NOT EXISTS mcp_call_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    server_name VARCHAR(255) NOT NULL,
    tool_name VARCHAR(255),
    call_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    success BOOLEAN NOT NULL,
    response_time INTEGER,
    user_id VARCHAR(255),
    client_ip VARCHAR(45),
    error_message TEXT,
    request_params JSONB,
    response_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建调用日志表索引
CREATE INDEX IF NOT EXISTS idx_call_logs_server_time ON mcp_call_logs(server_name, call_time DESC);
CREATE INDEX IF NOT EXISTS idx_call_logs_time ON mcp_call_logs(call_time DESC);
CREATE INDEX IF NOT EXISTS idx_call_logs_success ON mcp_call_logs(success, call_time DESC);
CREATE INDEX IF NOT EXISTS idx_call_logs_tool ON mcp_call_logs(tool_name, call_time DESC);
CREATE INDEX IF NOT EXISTS idx_call_logs_user ON mcp_call_logs(user_id, call_time DESC);

-- 创建MCP服务器统计表
CREATE TABLE IF NOT EXISTS mcp_server_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    server_name VARCHAR(255) NOT NULL,
    stat_date DATE NOT NULL,
    stat_hour INTEGER,  -- 0-23，NULL表示日级别统计
    total_calls INTEGER DEFAULT 0,
    success_calls INTEGER DEFAULT 0,
    failed_calls INTEGER DEFAULT 0,
    avg_response_time DECIMAL(10,2),
    min_response_time INTEGER,
    max_response_time INTEGER,
    unique_users INTEGER DEFAULT 0,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(server_name, stat_date, stat_hour)
);

-- 创建服务器统计表索引
CREATE INDEX IF NOT EXISTS idx_server_stats_lookup ON mcp_server_stats(server_name, stat_date, stat_hour);
CREATE INDEX IF NOT EXISTS idx_server_stats_date ON mcp_server_stats(stat_date DESC);

-- 创建MCP监控指标表
CREATE TABLE IF NOT EXISTS mcp_monitoring_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_name VARCHAR(255) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_type VARCHAR(50) NOT NULL,  -- 'gauge', 'counter', 'histogram'
    labels JSONB,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建监控指标表索引
CREATE INDEX IF NOT EXISTS idx_monitoring_metrics_time ON mcp_monitoring_metrics(recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_monitoring_metrics_name ON mcp_monitoring_metrics(metric_name, recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_monitoring_metrics_type ON mcp_monitoring_metrics(metric_type, recorded_at DESC);

-- 输出表创建完成信息
DO $$
DECLARE
    table_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO table_count 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name IN ('vector_embeddings', 'mcp_call_logs', 'mcp_server_stats', 'mcp_monitoring_metrics');
    
    RAISE NOTICE 'MCPHub数据库表结构创建完成';
    RAISE NOTICE '成功创建 % 个表', table_count;
    RAISE NOTICE '表包括: vector_embeddings, mcp_call_logs, mcp_server_stats, mcp_monitoring_metrics';
    RAISE NOTICE '所有索引已创建完成';
END $$;
