-- MCPHub 基础数据初始化脚本
-- 插入一些示例数据用于测试和演示

-- 插入一些示例监控指标
INSERT INTO mcp_monitoring_metrics (metric_name, metric_value, metric_type, labels, recorded_at) VALUES
('system_cpu_usage', 45.5, 'gauge', '{"host": "mcphub-prod", "component": "system"}', NOW() - INTERVAL '1 hour'),
('system_memory_usage', 68.2, 'gauge', '{"host": "mcphub-prod", "component": "system"}', NOW() - INTERVAL '1 hour'),
('database_connections', 12, 'gauge', '{"database": "mcphub", "component": "postgres"}', NOW() - INTERVAL '1 hour'),
('http_requests_total', 1250, 'counter', '{"method": "GET", "status": "200", "endpoint": "/api/servers"}', NOW() - INTERVAL '30 minutes'),
('http_requests_total', 89, 'counter', '{"method": "POST", "status": "200", "endpoint": "/api/tools/call"}', NOW() - INTERVAL '30 minutes'),
('mcp_server_response_time', 125.5, 'histogram', '{"server": "amap", "tool": "search"}', NOW() - INTERVAL '15 minutes'),
('mcp_server_response_time', 89.2, 'histogram', '{"server": "playwright", "tool": "navigate"}', NOW() - INTERVAL '15 minutes'),
('websocket_connections', 5, 'gauge', '{"component": "websocket"}', NOW() - INTERVAL '5 minutes');

-- 插入一些示例服务器统计数据
INSERT INTO mcp_server_stats (server_name, stat_date, stat_hour, total_calls, success_calls, failed_calls, avg_response_time, min_response_time, max_response_time, unique_users, updated_at) VALUES
('amap', CURRENT_DATE, 10, 45, 43, 2, 156.7, 89, 450, 8, NOW()),
('amap', CURRENT_DATE, 11, 67, 65, 2, 142.3, 76, 380, 12, NOW()),
('playwright', CURRENT_DATE, 10, 23, 21, 2, 234.5, 120, 890, 5, NOW()),
('playwright', CURRENT_DATE, 11, 34, 32, 2, 198.9, 98, 650, 7, NOW()),
('context7', CURRENT_DATE, 10, 12, 12, 0, 89.4, 45, 156, 3, NOW()),
('context7', CURRENT_DATE, 11, 18, 17, 1, 95.2, 52, 234, 4, NOW()),
('sequential-thinking', CURRENT_DATE, 10, 8, 8, 0, 1234.5, 890, 2100, 2, NOW()),
('sequential-thinking', CURRENT_DATE, 11, 15, 14, 1, 1156.8, 756, 1890, 3, NOW()),
('fetch', CURRENT_DATE, 10, 5, 4, 1, 345.6, 234, 567, 2, NOW()),
('fetch', CURRENT_DATE, 11, 3, 3, 0, 298.7, 189, 456, 1, NOW());

-- 插入一些示例调用日志
INSERT INTO mcp_call_logs (server_name, tool_name, call_time, success, response_time, user_id, client_ip, request_params, response_data) VALUES
('amap', 'search', NOW() - INTERVAL '2 hours', true, 156, 'user_001', '*************', '{"query": "北京市朝阳区", "type": "poi"}', '{"status": "success", "results": []}'),
('amap', 'geocode', NOW() - INTERVAL '1.5 hours', true, 89, 'user_002', '*************', '{"address": "上海市浦东新区"}', '{"status": "success", "location": {"lat": 31.2304, "lng": 121.4737}}'),
('playwright', 'navigate', NOW() - INTERVAL '1 hour', true, 234, 'user_001', '*************', '{"url": "https://example.com"}', '{"status": "success", "title": "Example Domain"}'),
('playwright', 'screenshot', NOW() - INTERVAL '45 minutes', false, 0, 'user_003', '*************', '{"selector": "#main"}', '{"status": "error", "message": "Element not found"}'),
('context7', 'search', NOW() - INTERVAL '30 minutes', true, 95, 'user_002', '*************', '{"query": "React hooks", "limit": 10}', '{"status": "success", "results": []}'),
('sequential-thinking', 'think', NOW() - INTERVAL '15 minutes', true, 1156, 'user_001', '*************', '{"problem": "如何优化数据库查询"}', '{"status": "success", "thoughts": []}'),
('fetch', 'get', NOW() - INTERVAL '10 minutes', true, 298, 'user_004', '*************', '{"url": "https://api.example.com/data"}', '{"status": "success", "data": {}}');

-- 输出数据初始化完成信息
DO $$
DECLARE
    call_logs_count INTEGER;
    server_stats_count INTEGER;
    metrics_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO call_logs_count FROM mcp_call_logs;
    SELECT COUNT(*) INTO server_stats_count FROM mcp_server_stats;
    SELECT COUNT(*) INTO metrics_count FROM mcp_monitoring_metrics;
    
    RAISE NOTICE 'MCPHub基础数据初始化完成';
    RAISE NOTICE '插入调用日志记录: % 条', call_logs_count;
    RAISE NOTICE '插入服务器统计记录: % 条', server_stats_count;
    RAISE NOTICE '插入监控指标记录: % 条', metrics_count;
    RAISE NOTICE '数据库已准备就绪，可以开始使用MCPHub';
END $$;
