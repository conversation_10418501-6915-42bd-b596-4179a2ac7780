# MCPHub Docker部署指南

## 🚀 快速开始

### 开发环境

1. **启动PostgreSQL数据库**：
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d postgres
   ```

2. **验证数据库连接**：
   ```bash
   docker-compose exec postgres psql -U mcphub_dev_user -d mcphub_dev -c "SELECT version();"
   ```

3. **启动应用 (本地开发)**：
   ```bash
   pnpm dev
   ```

### 生产环境

1. **启动完整服务**：
   ```bash
   docker-compose up -d
   ```

2. **查看服务状态**：
   ```bash
   docker-compose ps
   ```

3. **查看日志**：
   ```bash
   docker-compose logs -f mcphub
   ```

## 📋 服务说明

### PostgreSQL数据库
- **镜像**: `pgvector/pgvector:pg16`
- **端口**: 5432
- **数据库**: mcphub (生产) / mcphub_dev (开发)
- **扩展**: uuid-ossp, vector
- **数据持久化**: Docker volume

### MCPHub应用
- **构建**: 基于项目Dockerfile
- **端口**: 3000 (可配置)
- **健康检查**: `/health` 端点
- **依赖**: PostgreSQL数据库

### Redis缓存 (可选)
- **镜像**: `redis:7-alpine`
- **端口**: 6379
- **用途**: 缓存和会话存储
- **启用**: 使用 `--profile full` 参数

## 🔧 配置说明

### 环境变量
- **DB_URL**: 数据库连接字符串
- **NODE_ENV**: 运行环境 (development/production)
- **PORT**: 应用端口
- **ENABLE_SMART_ROUTING**: 启用智能路由
- **OPENAI_API_KEY**: OpenAI API密钥

### 数据库配置
- **连接数**: 200
- **共享缓冲区**: 256MB
- **工作内存**: 4MB
- **维护内存**: 64MB

## 🛠️ 常用命令

### 数据库管理
```bash
# 连接数据库
docker-compose exec postgres psql -U mcphub_user -d mcphub

# 备份数据库
docker-compose exec postgres pg_dump -U mcphub_user mcphub > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U mcphub_user mcphub < backup.sql

# 查看数据库大小
docker-compose exec postgres psql -U mcphub_user -d mcphub -c "SELECT pg_size_pretty(pg_database_size('mcphub'));"
```

### 应用管理
```bash
# 重启应用
docker-compose restart mcphub

# 查看应用日志
docker-compose logs -f mcphub

# 进入应用容器
docker-compose exec mcphub sh

# 更新应用
docker-compose build mcphub
docker-compose up -d mcphub
```

### 系统监控
```bash
# 查看资源使用
docker stats

# 查看磁盘使用
docker system df

# 清理未使用资源
docker system prune -f
```

## 🔍 故障排查

### 数据库连接问题
1. 检查PostgreSQL容器状态：`docker-compose ps postgres`
2. 查看数据库日志：`docker-compose logs postgres`
3. 验证网络连接：`docker-compose exec mcphub ping postgres`

### 应用启动问题
1. 检查环境变量配置
2. 查看应用日志：`docker-compose logs mcphub`
3. 验证数据库连接：检查DB_URL配置

### 性能问题
1. 监控资源使用：`docker stats`
2. 检查数据库性能：查看慢查询日志
3. 调整PostgreSQL配置：修改 `docker/postgres/postgresql.conf`

## 📊 监控和维护

### 健康检查
- PostgreSQL: `pg_isready` 命令
- MCPHub: `/health` HTTP端点
- Redis: `redis-cli ping` 命令

### 日志管理
- 应用日志：`./logs` 目录
- PostgreSQL日志：容器内 `/var/log/postgresql/`
- 日志轮转：自动配置

### 备份策略
1. **数据库备份**: 定期执行 `pg_dump`
2. **配置备份**: 备份 `.env` 和配置文件
3. **代码备份**: Git版本控制

## 🚀 部署最佳实践

### 安全配置
1. 修改默认密码
2. 配置防火墙规则
3. 启用SSL/TLS
4. 定期更新镜像

### 性能优化
1. 调整PostgreSQL配置
2. 配置Redis缓存
3. 启用应用监控
4. 优化Docker资源限制

### 高可用部署
1. 使用Docker Swarm或Kubernetes
2. 配置数据库主从复制
3. 设置负载均衡
4. 实施监控告警
