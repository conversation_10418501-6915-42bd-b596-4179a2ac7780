---
title: "Group Bearer Authentication User Guide"
description: "Complete user guide for setting up and managing group-level Bearer authentication"
---

# Group Bearer Authentication User Guide

This comprehensive guide walks you through setting up and managing group-level Bearer authentication in MCPHub.

## Overview

Group-level Bearer authentication allows you to:
- Set unique authentication keys for different groups
- Override global authentication settings per group
- Implement fine-grained access control
- Isolate security concerns between different tool sets

## Getting Started

### Prerequisites

- MCPHub instance running (v2.0+)
- Admin access to create/modify groups
- Basic understanding of Bearer token authentication

### Quick Setup

1. **Create a Group with Authentication**
   ```bash
   curl -X POST http://localhost:3000/api/groups \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
     -d '{
       "name": "secure-team",
       "displayName": "Secure Team Tools",
       "bearerAuthKey": "team-secure-key-2024"
     }'
   ```

2. **Test the Authentication**
   ```bash
   curl -X POST http://localhost:3000/mcp/secure-team \
     -H "Authorization: Bearer team-secure-key-2024" \
     -H "Content-Type: application/json" \
     -d '{"jsonrpc": "2.0", "id": 1, "method": "tools/list"}'
   ```

## Step-by-Step Configuration

### Method 1: Using the Dashboard

1. **Navigate to Groups**
   - Open MCPHub dashboard
   - Go to "Groups" section
   - Click "Create New Group" or select existing group

2. **Configure Authentication**
   - Toggle "Enable Bearer Authentication"
   - Enter your Bearer key (8-256 characters)
   - Save changes

3. **Verify Setup**
   - Test API access with the new key
   - Check group status in dashboard

### Method 2: Using the API

#### Create Group with Authentication

```bash
curl -X POST http://localhost:3000/api/groups \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{
    "name": "production-tools",
    "displayName": "Production Tools",
    "description": "Critical production management tools",
    "bearerAuthKey": "prod-secure-key-2024-v1",
    "servers": ["monitoring", "deployment", "alerts"]
  }'
```

#### Update Existing Group

```bash
# Add authentication to existing group
curl -X PUT http://localhost:3000/api/groups/production-tools \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{
    "bearerAuthKey": "new-prod-key-2024-v2"
  }'

# Remove authentication from group
curl -X PUT http://localhost:3000/api/groups/production-tools \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{
    "bearerAuthKey": ""
  }'
```

## Security Best Practices

### Key Generation

```bash
# Generate secure Bearer keys
openssl rand -base64 32  # 32-character key
openssl rand -hex 32     # 64-character hex key
```

### Key Rotation

```bash
# 1. Generate new key
NEW_KEY=$(openssl rand -base64 32)

# 2. Update group with new key
curl -X PUT http://localhost:3000/api/groups/production-tools \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d "{\"bearerAuthKey\": \"$NEW_KEY\"}"
```

## Troubleshooting

### Common Issues

#### 1. Authentication Failed

```bash
# Error: 401 Unauthorized
{
  "error": {
    "code": "AUTHENTICATION_FAILED",
    "message": "Invalid Bearer token for group"
  }
}
```

#### 2. Group Not Found

```bash
# Error: 404 Not Found
{
  "error": {
    "code": "GROUP_NOT_FOUND", 
    "message": "Group 'nonexistent-group' not found"
  }
}
```

#### 3. Invalid Bearer Key Format

```bash
# Error: 400 Bad Request
{
  "error": {
    "code": "INVALID_BEARER_KEY",
    "message": "Bearer key must be 8-256 characters"
  }
}
```

### Debugging Commands

```bash
# Check group configuration
curl -X GET http://localhost:3000/api/groups/your-group \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# Test authentication
curl -X POST http://localhost:3000/mcp/your-group \
  -H "Authorization: Bearer your-group-key" \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "id": 1, "method": "tools/list"}'
```

## Migration Guide

### From Global to Group Authentication

```bash
# 1. Identify current global key
echo $BEARER_AUTH_KEY

# 2. Create groups with individual keys
curl -X POST http://localhost:3000/api/groups \
  -d '{
    "name": "legacy-tools",
    "bearerAuthKey": "'$BEARER_AUTH_KEY'",
    "servers": ["existing-server-1", "existing-server-2"]
  }'

# 3. Update clients to use group endpoints
# Old: POST /mcp -> New: POST /mcp/legacy-tools
```

This guide provides everything you need to successfully implement and manage group-level Bearer authentication in MCPHub.
