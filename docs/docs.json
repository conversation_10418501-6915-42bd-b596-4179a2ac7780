{"$schema": "https://mintlify.com/docs.json", "theme": "mint", "name": "MCPHub Documentation", "description": "The Unified Hub for Model Context Protocol (MCP) Servers", "colors": {"primary": "#16A34A", "light": "#07C983", "dark": "#15803D"}, "favicon": "/favicon.ico", "navigation": {"tabs": [{"tab": "English", "groups": [{"group": "Get Started", "pages": ["index", "quickstart", "installation"]}, {"group": "Core Features", "pages": ["features/server-management", "features/group-management", "features/group-bearer-auth-guide", "features/smart-routing", "features/authentication", "features/monitoring"]}, {"group": "Configuration", "pages": ["configuration/mcp-settings", "configuration/environment-variables", "configuration/docker-setup", "configuration/nginx"]}, {"group": "Development", "pages": ["development/getting-started", "development/architecture", "development/contributing"]}]}, {"tab": "中文", "groups": [{"group": "开始使用", "pages": ["zh/index", "zh/quickstart", "zh/installation"]}, {"group": "核心功能", "pages": ["zh/features/server-management", "zh/features/group-management", "zh/features/group-bearer-auth-guide", "zh/features/smart-routing", "zh/features/authentication", "zh/features/monitoring"]}, {"group": "配置指南", "pages": ["zh/configuration/mcp-settings", "zh/configuration/environment-variables", "zh/configuration/docker-setup", "zh/configuration/nginx"]}, {"group": "开发指南", "pages": ["zh/development/getting-started", "zh/development/architecture", "zh/development/contributing"]}]}, {"tab": "API Reference", "groups": [{"group": "MCP Endpoints", "pages": ["api-reference/introduction", "api-reference/mcp-http", "api-reference/mcp-sse", "api-reference/smart-routing"]}, {"group": "Management API", "pages": ["api-reference/servers", "api-reference/groups", "api-reference/auth", "api-reference/logs", "api-reference/config"]}]}], "global": {"anchors": [{"anchor": "GitHub", "href": "https://github.com/samanhappy/mcphub", "icon": "github"}, {"anchor": "Discord", "href": "https://discord.gg/qMKNsn5Q", "icon": "discord"}, {"anchor": "Sponsor", "href": "https://ko-fi.com/samanhappy", "icon": "heart"}]}}, "logo": {"light": "/logo/light.svg", "dark": "/logo/dark.svg"}, "navbar": {"links": [{"label": "Demo", "href": "http://localhost:3000"}], "primary": {"type": "button", "label": "Get Started", "href": "https://docs.hubmcp.dev/quickstart"}}, "footer": {"socials": {"github": "https://github.com/samanhappy/mcphub", "discord": "https://discord.gg/qMKNsn5Q"}}}