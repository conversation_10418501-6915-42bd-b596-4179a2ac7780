---
title: 'Nginx Configuration'
description: 'Configure Nginx as a reverse proxy for MCPHub'
---

# Nginx Configuration

This guide explains how to configure Nginx as a reverse proxy for MCPHub, including SSL termination, load balancing, and caching strategies.

## Basic Reverse Proxy Setup

### Configuration File

Create or update your Nginx configuration file (`/etc/nginx/sites-available/mcphub`):

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL Configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Main application
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # API endpoints with longer timeout for MCP operations
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300;
        proxy_connect_timeout 60;
        proxy_send_timeout 60;
    }

    # Static assets caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:3000;
        proxy_cache_valid 200 1d;
        proxy_cache_valid 404 1m;
        add_header Cache-Control "public, immutable";
        expires 1y;
    }
}
```

### Enable the Configuration

```bash
# Create symbolic link to enable the site
sudo ln -s /etc/nginx/sites-available/mcphub /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

## Load Balancing Configuration

For high-availability setups with multiple MCPHub instances:

```nginx
upstream mcphub_backend {
    least_conn;
    server 127.0.0.1:3000 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3001 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3002 weight=1 max_fails=3 fail_timeout=30s;

    # Health check (Nginx Plus feature)
    # health_check interval=5s fails=3 passes=2;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL and other configurations...

    location / {
        proxy_pass http://mcphub_backend;
        # Other proxy settings...
    }
}
```

## Caching Configuration

### Browser Caching

```nginx
# Cache static assets
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    proxy_pass http://127.0.0.1:3000;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# Cache API responses (be careful with dynamic content)
location /api/public/ {
    proxy_pass http://127.0.0.1:3000;
    proxy_cache mcphub_cache;
    proxy_cache_valid 200 5m;
    proxy_cache_key "$scheme$request_method$host$request_uri";
    add_header X-Cache-Status $upstream_cache_status;
}
```

### Nginx Proxy Cache

Add to the `http` block in `nginx.conf`:

```nginx
http {
    # Proxy cache configuration
    proxy_cache_path /var/cache/nginx/mcphub
                     levels=1:2
                     keys_zone=mcphub_cache:10m
                     max_size=1g
                     inactive=60m
                     use_temp_path=off;

    # Other configurations...
}
```

## WebSocket Support

For real-time features and SSE (Server-Sent Events):

```nginx
location /api/stream {
    proxy_pass http://127.0.0.1:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # Disable buffering for real-time responses
    proxy_buffering off;
    proxy_cache off;

    # Timeouts for long-lived connections
    proxy_read_timeout 24h;
    proxy_send_timeout 24h;
}
```

## Security Configuration

### Rate Limiting

```nginx
http {
    # Define rate limiting zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    server {
        # Apply rate limiting to API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            # Other configurations...
        }

        # Strict rate limiting for login endpoints
        location /api/auth/login {
            limit_req zone=login burst=5;
            # Other configurations...
        }
    }
}
```

### IP Whitelisting

```nginx
# Allow specific IPs for admin endpoints
location /api/admin/ {
    allow ***********/24;
    allow 10.0.0.0/8;
    deny all;

    proxy_pass http://127.0.0.1:3000;
    # Other proxy settings...
}
```

## Monitoring and Logging

### Access Logs

```nginx
http {
    # Custom log format
    log_format mcphub_format '$remote_addr - $remote_user [$time_local] '
                            '"$request" $status $body_bytes_sent '
                            '"$http_referer" "$http_user_agent" '
                            '$request_time $upstream_response_time';

    server {
        # Enable access logging
        access_log /var/log/nginx/mcphub_access.log mcphub_format;
        error_log /var/log/nginx/mcphub_error.log;

        # Other configurations...
    }
}
```

### Status Page

```nginx
location /nginx_status {
    stub_status;
    allow 127.0.0.1;
    deny all;
}
```

## Docker Integration

When running MCPHub in Docker, update the proxy configuration:

```nginx
upstream mcphub_docker {
    server mcphub:3000;  # Docker service name
}

server {
    location / {
        proxy_pass http://mcphub_docker;
        # Other proxy settings...
    }
}
```

## Complete Example Configuration

Here's a production-ready example using the provided `nginx.conf.example`:

```bash
# Copy the example configuration
cp nginx.conf.example /etc/nginx/sites-available/mcphub

# Update the configuration with your domain and paths
sudo nano /etc/nginx/sites-available/mcphub

# Enable the site
sudo ln -s /etc/nginx/sites-available/mcphub /etc/nginx/sites-enabled/

# Test and reload
sudo nginx -t && sudo systemctl reload nginx
```

## Troubleshooting

### Common Issues

**502 Bad Gateway**: Check if MCPHub is running and accessible

**504 Gateway Timeout**: Increase `proxy_read_timeout` for long-running operations

**WebSocket connection failures**: Ensure proper `Upgrade` and `Connection` headers

**Cache issues**: Clear proxy cache or disable for development

### Debug Commands

```bash
# Test Nginx configuration
sudo nginx -t

# Check Nginx status
sudo systemctl status nginx

# View error logs
sudo tail -f /var/log/nginx/error.log

# Check if MCPHub is responding
curl -I http://localhost:3000
```

## Performance Optimization

### Worker Processes

```nginx
# In nginx.conf
worker_processes auto;
worker_connections 1024;
```

### Buffer Sizes

```nginx
proxy_buffering on;
proxy_buffer_size 128k;
proxy_buffers 4 256k;
proxy_busy_buffers_size 256k;
```

### Keep-Alive

```nginx
upstream mcphub_backend {
    server 127.0.0.1:3000;
    keepalive 32;
}

location / {
    proxy_pass http://mcphub_backend;
    proxy_http_version 1.1;
    proxy_set_header Connection "";
}
```

This configuration provides a solid foundation for running MCPHub behind Nginx with proper security, performance, and reliability features.
