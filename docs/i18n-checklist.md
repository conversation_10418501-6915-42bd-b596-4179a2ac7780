# 前端国际化检查清单

## 🌍 开发时必须检查的国际化项目

### 1. 文本内容
- [ ] **所有用户可见的文本**都使用 `t()` 函数包装
- [ ] **状态值**（如 'healthy', 'warning', 'up', 'down'）都有对应翻译
- [ ] **错误消息**都有国际化处理
- [ ] **按钮文本**都使用翻译键
- [ ] **表单标签和占位符**都国际化

### 2. 数值和单位
- [ ] **时间单位**（ms, s, min, h）都有翻译
- [ ] **数据单位**（bytes, KB, MB, GB）都有翻译
- [ ] **百分比符号**使用翻译
- [ ] **数字格式化**考虑地区差异

### 3. 日期和时间
- [ ] 使用 `toLocaleString()` 或 `toLocaleDateString()`
- [ ] 考虑时区显示
- [ ] 相对时间（如"2分钟前"）使用翻译

### 4. 状态和枚举值
```typescript
// ❌ 错误示例
<span>{status.toUpperCase()}</span>

// ✅ 正确示例  
<span>{t(`status.${status}`, status)}</span>
```

### 5. 动态内容
```typescript
// ❌ 错误示例
`${count} items`

// ✅ 正确示例
t('common.itemCount', { count })
```

## 📝 翻译文件结构

### 中文 (zh.json)
```json
{
  "monitoring": {
    "status": {
      "healthy": "健康",
      "warning": "警告", 
      "critical": "严重",
      "up": "正常",
      "down": "异常"
    },
    "units": {
      "ms": "毫秒",
      "percent": "%",
      "bytes": "字节"
    }
  }
}
```

### 英文 (en.json)
```json
{
  "monitoring": {
    "status": {
      "healthy": "Healthy",
      "warning": "Warning",
      "critical": "Critical", 
      "up": "Up",
      "down": "Down"
    },
    "units": {
      "ms": "ms",
      "percent": "%", 
      "bytes": "bytes"
    }
  }
}
```

## 🔍 常见遗漏点

1. **硬编码的状态值**
   - API返回的状态直接显示
   - 枚举值未翻译

2. **单位和符号**
   - ms, %, bytes 等单位
   - 货币符号、时间格式

3. **动态生成的文本**
   - 字符串拼接
   - 模板字符串

4. **第三方组件的文本**
   - 确保组件支持国际化
   - 传递正确的语言参数

## ✅ 检查工具

### 代码检查命令
```bash
# 查找可能的硬编码文本
grep -r "toUpperCase\|toLowerCase" frontend/src --include="*.tsx" --include="*.ts"

# 查找未翻译的状态值
grep -r "status.*up\|status.*down" frontend/src --include="*.tsx"

# 查找硬编码的单位
grep -r "ms\|%\|bytes" frontend/src --include="*.tsx" | grep -v "t("
```

### 运行时检查
- 切换语言测试所有页面
- 检查控制台是否有翻译缺失警告
- 验证数字和日期格式是否正确

## 📋 发布前检查清单

- [ ] 所有新增文本都有中英文翻译
- [ ] 状态值和枚举都已国际化
- [ ] 数值单位都使用翻译
- [ ] 日期时间格式正确
- [ ] 错误消息都已翻译
- [ ] 在两种语言下测试所有功能
- [ ] 检查文本长度在不同语言下的显示效果

## 🎯 最佳实践

1. **开发时就考虑国际化**，不要事后补充
2. **使用有意义的翻译键**，便于维护
3. **保持翻译文件结构一致**
4. **定期检查翻译完整性**
5. **考虑文本长度差异**对布局的影响

记住：**任何用户可见的文本都必须国际化！**
