---
title: '创建资源'
description: '创建新的 MCP 服务器、用户和组'
---

## 创建服务器

### 端点

```http
POST /api/servers
```

### 请求

#### 请求头

```http
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN
```

#### 请求体

```json
{
  "name": "文件系统服务器",
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-filesystem", "/allowed/path"],
  "env": {
    "NODE_ENV": "production",
    "DEBUG": "mcp:*",
    "MAX_FILES": "1000"
  },
  "cwd": "/app/workspace",
  "timeout": 30000,
  "retries": 3,
  "enabled": true,
  "description": "提供文件系统访问的 MCP 服务器",
  "tags": ["filesystem", "production"],
  "healthCheck": {
    "enabled": true,
    "interval": 30000,
    "timeout": 5000,
    "retries": 3,
    "endpoint": "/health"
  },
  "resources": {
    "memory": {
      "limit": "512MB",
      "warning": "400MB"
    },
    "cpu": {
      "limit": "50%"
    }
  },
  "logging": {
    "level": "info",
    "file": "/var/log/mcphub/server.log",
    "maxSize": "100MB",
    "maxFiles": 5
  }
}
```

#### 必填字段

- `name` (string): 服务器唯一名称
- `command` (string): 执行命令
- `args` (array): 命令参数数组

#### 可选字段

- `env` (object): 环境变量键值对
- `cwd` (string): 工作目录
- `timeout` (number): 超时时间（毫秒）
- `retries` (number): 重试次数
- `enabled` (boolean): 是否启用（默认 true）
- `description` (string): 服务器描述
- `tags` (array): 标签数组
- `healthCheck` (object): 健康检查配置
- `resources` (object): 资源限制配置
- `logging` (object): 日志配置

### 响应

#### 成功响应 (201 Created)

```json
{
  "success": true,
  "data": {
    "id": "server-abc123",
    "name": "文件系统服务器",
    "status": "stopped",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/allowed/path"],
    "env": {
      "NODE_ENV": "production",
      "DEBUG": "mcp:*",
      "MAX_FILES": "1000"
    },
    "cwd": "/app/workspace",
    "timeout": 30000,
    "retries": 3,
    "enabled": true,
    "description": "提供文件系统访问的 MCP 服务器",
    "tags": ["filesystem", "production"],
    "healthCheck": {
      "enabled": true,
      "interval": 30000,
      "timeout": 5000,
      "retries": 3,
      "endpoint": "/health",
      "status": "unknown"
    },
    "resources": {
      "memory": {
        "limit": "512MB",
        "warning": "400MB",
        "current": "0MB"
      },
      "cpu": {
        "limit": "50%",
        "current": "0%"
      }
    },
    "logging": {
      "level": "info",
      "file": "/var/log/mcphub/server.log",
      "maxSize": "100MB",
      "maxFiles": 5,
      "currentSize": "0MB"
    },
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z",
    "createdBy": "user123"
  },
  "message": "服务器创建成功"
}
```

#### 错误响应

**400 Bad Request - 参数错误**

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求数据验证失败",
    "details": [
      {
        "field": "name",
        "message": "服务器名称不能为空"
      },
      {
        "field": "command",
        "message": "执行命令不能为空"
      }
    ]
  }
}
```

**409 Conflict - 名称冲突**

```json
{
  "success": false,
  "error": {
    "code": "RESOURCE_CONFLICT",
    "message": "服务器名称已存在",
    "details": {
      "field": "name",
      "value": "文件系统服务器",
      "conflictingResourceId": "server-xyz789"
    }
  }
}
```

### 示例

#### cURL

```bash
curl -X POST http://localhost:3000/api/servers \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "文件系统服务器",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/data"],
    "env": {
      "NODE_ENV": "production"
    },
    "description": "生产环境文件系统服务器"
  }'
```

#### JavaScript

```javascript
const response = await fetch('/api/servers', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  },
  body: JSON.stringify({
    name: '文件系统服务器',
    command: 'npx',
    args: ['-y', '@modelcontextprotocol/server-filesystem', '/data'],
    env: {
      NODE_ENV: 'production',
    },
    description: '生产环境文件系统服务器',
  }),
});

const result = await response.json();
if (result.success) {
  console.log('服务器创建成功:', result.data);
} else {
  console.error('创建失败:', result.error);
}
```

#### Python

```python
import requests

response = requests.post(
    'http://localhost:3000/api/servers',
    headers={
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    },
    json={
        'name': '文件系统服务器',
        'command': 'npx',
        'args': ['-y', '@modelcontextprotocol/server-filesystem', '/data'],
        'env': {
            'NODE_ENV': 'production'
        },
        'description': '生产环境文件系统服务器'
    }
)

if response.status_code == 201:
    result = response.json()
    print('服务器创建成功:', result['data'])
else:
    error = response.json()
    print('创建失败:', error['error'])
```

## 创建用户

### 端点

```http
POST /api/users
```

### 请求体

```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "role": "user",
  "groups": ["dev-team", "qa-team"],
  "profile": {
    "firstName": "张",
    "lastName": "三",
    "department": "开发部",
    "title": "软件工程师",
    "phone": "+86-138-0013-8000",
    "location": "北京"
  },
  "preferences": {
    "language": "zh-CN",
    "timezone": "Asia/Shanghai",
    "notifications": {
      "email": true,
      "slack": false,
      "browser": true
    }
  },
  "enabled": true
}
```

### 响应 (201 Created)

```json
{
  "success": true,
  "data": {
    "id": "user-abc123",
    "username": "newuser",
    "email": "<EMAIL>",
    "role": "user",
    "groups": [
      {
        "id": "dev-team",
        "name": "开发团队",
        "role": "member"
      }
    ],
    "profile": {
      "firstName": "张",
      "lastName": "三",
      "fullName": "张三",
      "department": "开发部",
      "title": "软件工程师",
      "phone": "+86-138-0013-8000",
      "location": "北京",
      "avatar": null
    },
    "preferences": {
      "language": "zh-CN",
      "timezone": "Asia/Shanghai",
      "notifications": {
        "email": true,
        "slack": false,
        "browser": true
      }
    },
    "enabled": true,
    "lastLoginAt": null,
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z"
  },
  "message": "用户创建成功"
}
```

## 创建组

### 端点

```http
POST /api/groups
```

### 请求体

```json
{
  "name": "dev-team",
  "displayName": "开发团队",
  "description": "前端和后端开发人员",
  "parentGroup": null,
  "permissions": {
    "servers": {
      "create": false,
      "read": true,
      "update": true,
      "delete": false,
      "execute": true
    },
    "tools": {
      "filesystem": {
        "read": true,
        "write": true,
        "paths": ["/app/data", "/tmp"]
      },
      "web-search": {
        "enabled": true,
        "maxQueries": 100
      }
    },
    "monitoring": {
      "viewLogs": true,
      "viewMetrics": true,
      "exportData": false
    }
  },
  "settings": {
    "autoAssign": false,
    "maxMembers": 50,
    "requireApproval": true,
    "sessionTimeout": "8h"
  },
  "quotas": {
    "requests": {
      "daily": 1000,
      "monthly": 30000
    },
    "storage": {
      "maxSize": "10GB"
    }
  }
}
```

### 响应 (201 Created)

```json
{
  "success": true,
  "data": {
    "id": "group-abc123",
    "name": "dev-team",
    "displayName": "开发团队",
    "description": "前端和后端开发人员",
    "parentGroup": null,
    "permissions": {
      "servers": {
        "create": false,
        "read": true,
        "update": true,
        "delete": false,
        "execute": true
      },
      "tools": {
        "filesystem": {
          "read": true,
          "write": true,
          "paths": ["/app/data", "/tmp"]
        },
        "web-search": {
          "enabled": true,
          "maxQueries": 100
        }
      },
      "monitoring": {
        "viewLogs": true,
        "viewMetrics": true,
        "exportData": false
      }
    },
    "settings": {
      "autoAssign": false,
      "maxMembers": 50,
      "requireApproval": true,
      "sessionTimeout": "8h"
    },
    "quotas": {
      "requests": {
        "daily": 1000,
        "monthly": 30000
      },
      "storage": {
        "maxSize": "10GB"
      }
    },
    "memberCount": 0,
    "serverCount": 0,
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z",
    "createdBy": "admin"
  },
  "message": "组创建成功"
}
```

## 批量创建

### 批量创建服务器

```http
POST /api/servers/bulk
```

#### 请求体

```json
{
  "servers": [
    {
      "name": "dev-server-1",
      "command": "python",
      "args": ["-m", "mcp_server"],
      "env": { "ENV": "development" }
    },
    {
      "name": "dev-server-2",
      "command": "node",
      "args": ["server.js"],
      "env": { "ENV": "development" }
    }
  ],
  "options": {
    "skipExisting": true,
    "validateAll": true,
    "startAfterCreate": false
  }
}
```

#### 响应 (201 Created)

```json
{
  "success": true,
  "data": {
    "created": [
      {
        "id": "server-1",
        "name": "dev-server-1",
        "status": "created"
      },
      {
        "id": "server-2",
        "name": "dev-server-2",
        "status": "created"
      }
    ],
    "skipped": [],
    "failed": [],
    "summary": {
      "total": 2,
      "created": 2,
      "skipped": 0,
      "failed": 0
    }
  },
  "message": "批量创建完成，成功创建 2 个服务器"
}
```

## 验证

### 预验证创建请求

在实际创建资源之前验证请求：

```http
POST /api/servers/validate
```

#### 请求体

```json
{
  "name": "test-server",
  "command": "invalid-command",
  "args": []
}
```

#### 响应

```json
{
  "success": false,
  "data": {
    "valid": false,
    "errors": [
      {
        "field": "command",
        "message": "命令 'invalid-command' 不存在或无法执行"
      }
    ],
    "warnings": [
      {
        "field": "args",
        "message": "参数数组为空，服务器可能无法正常启动"
      }
    ]
  }
}
```

有关更多 API 端点信息，请参阅 [获取资源](/zh/api-reference/endpoint/get)、[删除资源](/zh/api-reference/endpoint/delete) 和 [WebHooks](/zh/api-reference/endpoint/webhook) 文档。
