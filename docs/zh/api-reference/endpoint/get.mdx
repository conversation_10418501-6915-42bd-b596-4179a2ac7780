---
title: '获取资源'
description: '查询和检索 MCP 服务器、用户和组信息'
---

## 获取服务器列表

### 端点

```http
GET /api/servers
```

### 查询参数

| 参数             | 类型    | 描述                            | 示例                         |
| ---------------- | ------- | ------------------------------- | ---------------------------- |
| `page`           | integer | 页码（从 1 开始）               | `?page=2`                    |
| `limit`          | integer | 每页记录数（默认 20，最大 100） | `?limit=50`                  |
| `sort`           | string  | 排序字段                        | `?sort=name`                 |
| `order`          | string  | 排序顺序（asc/desc）            | `?order=desc`                |
| `status`         | string  | 过滤服务器状态                  | `?status=running`            |
| `search`         | string  | 搜索服务器名称或描述            | `?search=python`             |
| `group`          | string  | 过滤所属组                      | `?group=dev-team`            |
| `tags`           | string  | 过滤标签（逗号分隔）            | `?tags=python,production`    |
| `enabled`        | boolean | 过滤启用状态                    | `?enabled=true`              |
| `created_after`  | string  | 创建时间起始                    | `?created_after=2024-01-01`  |
| `created_before` | string  | 创建时间结束                    | `?created_before=2024-01-31` |

### 响应

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "server-abc123",
        "name": "文件系统服务器",
        "status": "running",
        "command": "npx",
        "args": ["-y", "@modelcontextprotocol/server-filesystem", "/data"],
        "env": {
          "NODE_ENV": "production"
        },
        "cwd": "/app",
        "pid": 12345,
        "uptime": 3600000,
        "enabled": true,
        "description": "提供文件系统访问的 MCP 服务器",
        "tags": ["filesystem", "production"],
        "health": {
          "status": "healthy",
          "lastCheck": "2024-01-01T12:00:00Z",
          "responseTime": "45ms"
        },
        "resources": {
          "memory": {
            "used": "128MB",
            "limit": "512MB",
            "percentage": 25
          },
          "cpu": {
            "used": "15%",
            "limit": "50%"
          }
        },
        "stats": {
          "totalRequests": 1523,
          "errorCount": 2,
          "avgResponseTime": "234ms"
        },
        "lastRestart": "2024-01-01T08:00:00Z",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "pages": 3,
      "hasNext": true,
      "hasPrev": false
    },
    "filters": {
      "status": "running",
      "totalFiltered": 12
    }
  }
}
```

### 示例

```bash
# 获取运行中的服务器，按名称排序
curl -X GET "http://localhost:3000/api/servers?status=running&sort=name&order=asc" \
  -H "Authorization: Bearer $TOKEN"

# 搜索包含 "python" 的服务器
curl -X GET "http://localhost:3000/api/servers?search=python&limit=10" \
  -H "Authorization: Bearer $TOKEN"

# 获取开发团队的服务器
curl -X GET "http://localhost:3000/api/servers?group=dev-team" \
  -H "Authorization: Bearer $TOKEN"
```

## 获取服务器详情

### 端点

```http
GET /api/servers/{serverId}
```

### 路径参数

- `serverId` (string): 服务器唯一标识符

### 查询参数

| 参数            | 类型   | 描述                                            |
| --------------- | ------ | ----------------------------------------------- |
| `include`       | string | 包含额外信息（逗号分隔）：`logs,metrics,events` |
| `metrics_range` | string | 指标时间范围：`1h`, `24h`, `7d`                 |

### 响应

```json
{
  "success": true,
  "data": {
    "id": "server-abc123",
    "name": "文件系统服务器",
    "status": "running",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/data"],
    "env": {
      "NODE_ENV": "production",
      "DEBUG": "mcp:*"
    },
    "cwd": "/app",
    "pid": 12345,
    "uptime": 3600000,
    "enabled": true,
    "description": "提供文件系统访问的 MCP 服务器",
    "tags": ["filesystem", "production"],
    "healthCheck": {
      "enabled": true,
      "interval": 30000,
      "timeout": 5000,
      "retries": 3,
      "endpoint": "/health",
      "status": "healthy",
      "lastCheck": "2024-01-01T12:00:00Z",
      "responseTime": "45ms",
      "consecutiveFailures": 0
    },
    "resources": {
      "memory": {
        "used": "128MB",
        "limit": "512MB",
        "warning": "400MB",
        "percentage": 25
      },
      "cpu": {
        "used": "15%",
        "limit": "50%",
        "cores": 4
      },
      "network": {
        "bytesIn": "1.2GB",
        "bytesOut": "890MB"
      }
    },
    "stats": {
      "totalRequests": 1523,
      "successfulRequests": 1521,
      "errorCount": 2,
      "avgResponseTime": "234ms",
      "p95ResponseTime": "450ms",
      "requestsPerMinute": 25,
      "lastError": {
        "timestamp": "2024-01-01T11:30:00Z",
        "message": "Temporary connection timeout",
        "count": 1
      }
    },
    "capabilities": [
      {
        "type": "tool",
        "name": "read_file",
        "description": "读取文件内容",
        "schema": {
          "type": "object",
          "properties": {
            "path": { "type": "string" }
          }
        }
      },
      {
        "type": "tool",
        "name": "write_file",
        "description": "写入文件内容",
        "schema": {
          "type": "object",
          "properties": {
            "path": { "type": "string" },
            "content": { "type": "string" }
          }
        }
      }
    ],
    "groups": [
      {
        "id": "dev-team",
        "name": "开发团队",
        "permissions": ["read", "write", "execute"]
      }
    ],
    "events": [
      {
        "id": "event-123",
        "type": "started",
        "timestamp": "2024-01-01T08:00:00Z",
        "message": "服务器启动成功",
        "metadata": {
          "pid": 12345,
          "startupTime": "2.3s"
        }
      }
    ],
    "lastRestart": "2024-01-01T08:00:00Z",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z",
    "createdBy": "admin"
  }
}
```

### 示例

```bash
# 获取服务器基本信息
curl -X GET "http://localhost:3000/api/servers/server-abc123" \
  -H "Authorization: Bearer $TOKEN"

# 获取服务器详情包含日志和指标
curl -X GET "http://localhost:3000/api/servers/server-abc123?include=logs,metrics&metrics_range=24h" \
  -H "Authorization: Bearer $TOKEN"
```

## 获取服务器状态

### 端点

```http
GET /api/servers/{serverId}/status
```

### 响应

```json
{
  "success": true,
  "data": {
    "serverId": "server-abc123",
    "status": "running",
    "health": "healthy",
    "pid": 12345,
    "uptime": 3600000,
    "startedAt": "2024-01-01T08:00:00Z",
    "lastHealthCheck": "2024-01-01T12:00:00Z",
    "resources": {
      "memory": {
        "rss": 134217728,
        "heapTotal": 67108864,
        "heapUsed": 45088768,
        "external": 8388608
      },
      "cpu": {
        "user": 1000000,
        "system": 500000,
        "percentage": 15.5
      }
    },
    "connections": {
      "active": 5,
      "total": 127
    },
    "performance": {
      "requestsPerSecond": 12.5,
      "avgResponseTime": "234ms",
      "errorRate": "0.1%"
    }
  }
}
```

## 获取服务器日志

### 端点

```http
GET /api/servers/{serverId}/logs
```

### 查询参数

| 参数     | 类型    | 描述                                           |
| -------- | ------- | ---------------------------------------------- |
| `level`  | string  | 日志级别过滤：`error`, `warn`, `info`, `debug` |
| `limit`  | integer | 返回日志条数（默认 100，最大 1000）            |
| `since`  | string  | 开始时间（ISO 8601 格式）                      |
| `until`  | string  | 结束时间（ISO 8601 格式）                      |
| `follow` | boolean | 实时跟踪日志流                                 |
| `search` | string  | 搜索日志内容                                   |

### 响应

```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "log-123",
        "timestamp": "2024-01-01T12:00:00Z",
        "level": "info",
        "message": "处理请求: read_file",
        "source": "mcp-server",
        "metadata": {
          "requestId": "req-456",
          "userId": "user-789",
          "duration": "45ms"
        }
      },
      {
        "id": "log-124",
        "timestamp": "2024-01-01T12:00:05Z",
        "level": "error",
        "message": "文件不存在: /nonexistent/file.txt",
        "source": "filesystem",
        "metadata": {
          "requestId": "req-457",
          "path": "/nonexistent/file.txt",
          "error": "ENOENT"
        }
      }
    ],
    "pagination": {
      "limit": 100,
      "total": 1523,
      "hasMore": true,
      "nextCursor": "cursor-abc123"
    }
  }
}
```

### 实时日志流

```bash
# 实时跟踪日志
curl -X GET "http://localhost:3000/api/servers/server-abc123/logs?follow=true" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Accept: text/event-stream"
```

## 获取服务器指标

### 端点

```http
GET /api/servers/{serverId}/metrics
```

### 查询参数

| 参数          | 类型   | 描述                                        |
| ------------- | ------ | ------------------------------------------- |
| `timeRange`   | string | 时间范围：`1h`, `24h`, `7d`, `30d`          |
| `granularity` | string | 数据粒度：`1m`, `5m`, `1h`, `1d`            |
| `metrics`     | string | 指定指标（逗号分隔）：`cpu,memory,requests` |

### 响应

```json
{
  "success": true,
  "data": {
    "timeRange": "1h",
    "granularity": "5m",
    "metrics": {
      "cpu": {
        "data": [
          { "timestamp": "2024-01-01T11:00:00Z", "value": 12.5 },
          { "timestamp": "2024-01-01T11:05:00Z", "value": 15.2 }
        ],
        "summary": {
          "avg": 13.8,
          "min": 8.1,
          "max": 18.5,
          "current": 15.2
        }
      },
      "memory": {
        "data": [
          { "timestamp": "2024-01-01T11:00:00Z", "value": 125 },
          { "timestamp": "2024-01-01T11:05:00Z", "value": 128 }
        ],
        "summary": {
          "avg": 126.5,
          "min": 120,
          "max": 135,
          "current": 128
        }
      },
      "requests": {
        "data": [
          { "timestamp": "2024-01-01T11:00:00Z", "value": 45 },
          { "timestamp": "2024-01-01T11:05:00Z", "value": 52 }
        ],
        "summary": {
          "total": 2847,
          "avg": 48.5,
          "peak": 67
        }
      },
      "responseTime": {
        "data": [
          { "timestamp": "2024-01-01T11:00:00Z", "avg": 230, "p95": 450 },
          { "timestamp": "2024-01-01T11:05:00Z", "avg": 245, "p95": 480 }
        ],
        "summary": {
          "avgResponseTime": "237ms",
          "p95ResponseTime": "465ms"
        }
      }
    }
  }
}
```

## 获取用户列表

### 端点

```http
GET /api/users
```

### 查询参数

| 参数               | 类型    | 描述             |
| ------------------ | ------- | ---------------- |
| `role`             | string  | 过滤用户角色     |
| `group`            | string  | 过滤所属组       |
| `enabled`          | boolean | 过滤启用状态     |
| `search`           | string  | 搜索用户名或邮箱 |
| `last_login_after` | string  | 最后登录时间起始 |

### 响应

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "user-abc123",
        "username": "zhangsan",
        "email": "<EMAIL>",
        "role": "user",
        "enabled": true,
        "profile": {
          "firstName": "张",
          "lastName": "三",
          "fullName": "张三",
          "department": "开发部",
          "title": "软件工程师"
        },
        "groups": [
          {
            "id": "dev-team",
            "name": "开发团队",
            "role": "member"
          }
        ],
        "stats": {
          "totalSessions": 45,
          "totalRequests": 1234,
          "lastRequestAt": "2024-01-01T11:30:00Z"
        },
        "lastLoginAt": "2024-01-01T08:00:00Z",
        "createdAt": "2023-12-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 89,
      "pages": 5
    }
  }
}
```

## 获取组列表

### 端点

```http
GET /api/groups
```

### 响应

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "group-abc123",
        "name": "dev-team",
        "displayName": "开发团队",
        "description": "前端和后端开发人员",
        "memberCount": 12,
        "serverCount": 8,
        "parentGroup": null,
        "children": [],
        "permissions": {
          "servers": ["read", "write", "execute"],
          "tools": ["read", "execute"]
        },
        "quotas": {
          "requests": {
            "used": 750,
            "limit": 1000
          }
        },
        "createdAt": "2023-12-01T00:00:00Z"
      }
    ]
  }
}
```

## 搜索

### 全局搜索

```http
GET /api/search
```

### 查询参数

| 参数    | 类型    | 描述                                           |
| ------- | ------- | ---------------------------------------------- |
| `q`     | string  | 搜索关键词                                     |
| `type`  | string  | 资源类型：`servers`, `users`, `groups`, `logs` |
| `limit` | integer | 每种类型的最大结果数                           |

### 响应

```json
{
  "success": true,
  "data": {
    "query": "python",
    "results": {
      "servers": [
        {
          "id": "server-1",
          "name": "Python MCP Server",
          "type": "server",
          "relevance": 0.95
        }
      ],
      "users": [],
      "groups": [
        {
          "id": "python-devs",
          "name": "Python 开发者",
          "type": "group",
          "relevance": 0.8
        }
      ],
      "logs": [
        {
          "id": "log-123",
          "message": "Starting Python server...",
          "type": "log",
          "relevance": 0.7
        }
      ]
    },
    "total": 3
  }
}
```

有关更多信息，请参阅 [创建资源](/zh/api-reference/endpoint/create)、[删除资源](/zh/api-reference/endpoint/delete) 和 [WebHooks](/zh/api-reference/endpoint/webhook) 文档。
