---
title: 删除资源 API
description: 删除各种资源的 API 端点，包括服务器、组和配置等
---

# 删除资源 API

本文档描述了用于删除各种资源的 API 端点。

## 删除 MCP 服务器

删除指定的 MCP 服务器配置。

### 端点

```http
DELETE /api/servers/{id}
```

### 参数

| 参数名 | 类型   | 位置 | 必需 | 描述               |
| ------ | ------ | ---- | ---- | ------------------ |
| id     | string | path | 是   | 服务器的唯一标识符 |

### 请求示例

```bash
curl -X DELETE \
  'https://api.mcphub.io/api/servers/mcp-server-123' \
  -H 'Authorization: Bearer YOUR_API_TOKEN' \
  -H 'Content-Type: application/json'
```

### 响应

#### 成功响应 (204 No Content)

```json
{
  "success": true,
  "message": "服务器已成功删除",
  "data": {
    "id": "mcp-server-123",
    "deletedAt": "2024-01-15T10:30:00Z"
  }
}
```

#### 错误响应

**404 Not Found**

```json
{
  "error": {
    "code": "SERVER_NOT_FOUND",
    "message": "指定的服务器不存在",
    "details": {
      "serverId": "mcp-server-123"
    }
  }
}
```

**409 Conflict**

```json
{
  "error": {
    "code": "SERVER_IN_USE",
    "message": "服务器正在使用中，无法删除",
    "details": {
      "activeConnections": 5,
      "associatedGroups": ["group-1", "group-2"]
    }
  }
}
```

## 删除服务器组

删除指定的服务器组。

### 端点

```http
DELETE /api/groups/{id}
```

### 参数

| 参数名 | 类型    | 位置  | 必需 | 描述                           |
| ------ | ------- | ----- | ---- | ------------------------------ |
| id     | string  | path  | 是   | 组的唯一标识符                 |
| force  | boolean | query | 否   | 是否强制删除（包含服务器的组） |

### 请求示例

```bash
curl -X DELETE \
  'https://api.mcphub.io/api/groups/production-group?force=true' \
  -H 'Authorization: Bearer YOUR_API_TOKEN' \
  -H 'Content-Type: application/json'
```

### 响应

#### 成功响应 (204 No Content)

```json
{
  "success": true,
  "message": "服务器组已成功删除",
  "data": {
    "id": "production-group",
    "deletedServers": ["server-1", "server-2"],
    "deletedAt": "2024-01-15T10:30:00Z"
  }
}
```

## 删除配置项

删除指定的配置项。

### 端点

```http
DELETE /api/config/{key}
```

### 参数

| 参数名 | 类型   | 位置 | 必需 | 描述     |
| ------ | ------ | ---- | ---- | -------- |
| key    | string | path | 是   | 配置键名 |

### 请求示例

```bash
curl -X DELETE \
  'https://api.mcphub.io/api/config/custom-setting' \
  -H 'Authorization: Bearer YOUR_API_TOKEN'
```

### 响应

#### 成功响应 (200 OK)

```json
{
  "success": true,
  "message": "配置项已删除",
  "data": {
    "key": "custom-setting",
    "previousValue": "old-value",
    "deletedAt": "2024-01-15T10:30:00Z"
  }
}
```

## 批量删除

### 批量删除服务器

删除多个 MCP 服务器。

#### 端点

```http
DELETE /api/servers/batch
```

#### 请求体

```json
{
  "serverIds": ["server-1", "server-2", "server-3"],
  "force": false
}
```

#### 响应

```json
{
  "success": true,
  "message": "批量删除完成",
  "data": {
    "deleted": ["server-1", "server-3"],
    "failed": [
      {
        "id": "server-2",
        "reason": "服务器正在使用中"
      }
    ],
    "summary": {
      "total": 3,
      "deleted": 2,
      "failed": 1
    }
  }
}
```

## 软删除 vs 硬删除

### 软删除

默认情况下，MCPHub 使用软删除机制：

- 资源被标记为已删除但保留在数据库中
- 可以通过恢复 API 恢复删除的资源
- 删除的资源在列表 API 中默认不显示

### 硬删除

使用 `permanent=true` 参数执行硬删除：

```bash
curl -X DELETE \
  'https://api.mcphub.io/api/servers/mcp-server-123?permanent=true' \
  -H 'Authorization: Bearer YOUR_API_TOKEN'
```

<Warning>硬删除操作不可逆，请谨慎使用。</Warning>

## 权限要求

| 操作       | 所需权限                 |
| ---------- | ------------------------ |
| 删除服务器 | `servers:delete`         |
| 删除组     | `groups:delete`          |
| 删除配置   | `config:delete`          |
| 硬删除     | `admin:permanent_delete` |

## 错误代码

| 错误代码                   | HTTP 状态码 | 描述             |
| -------------------------- | ----------- | ---------------- |
| `RESOURCE_NOT_FOUND`       | 404         | 资源不存在       |
| `RESOURCE_IN_USE`          | 409         | 资源正在使用中   |
| `INSUFFICIENT_PERMISSIONS` | 403         | 权限不足         |
| `VALIDATION_ERROR`         | 400         | 请求参数验证失败 |
| `INTERNAL_ERROR`           | 500         | 服务器内部错误   |

## 最佳实践

### 1. 删除前检查

在删除资源前，建议先检查资源的使用情况：

```bash
# 检查服务器使用情况
curl -X GET \
  'https://api.mcphub.io/api/servers/mcp-server-123/usage' \
  -H 'Authorization: Bearer YOUR_API_TOKEN'
```

### 2. 备份重要数据

对于重要资源，建议在删除前进行备份：

```bash
# 导出服务器配置
curl -X GET \
  'https://api.mcphub.io/api/servers/mcp-server-123/export' \
  -H 'Authorization: Bearer YOUR_API_TOKEN' \
  > server-backup.json
```

### 3. 使用事务删除

对于复杂的删除操作，使用事务确保数据一致性：

```json
{
  "transaction": true,
  "operations": [
    {
      "type": "delete",
      "resource": "server",
      "id": "server-1"
    },
    {
      "type": "delete",
      "resource": "group",
      "id": "group-1"
    }
  ]
}
```

## 恢复删除的资源

软删除的资源可以通过恢复 API 恢复：

```bash
curl -X POST \
  'https://api.mcphub.io/api/servers/mcp-server-123/restore' \
  -H 'Authorization: Bearer YOUR_API_TOKEN'
```
