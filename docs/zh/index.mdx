---
title: '欢迎使用 MCPHub'
description: 'MCPHub 是一个强大的 Model Context Protocol (MCP) 服务器管理平台，提供智能路由、负载均衡和实时监控功能'
---

<img className="block dark:hidden" src="/images/hero-light.png" alt="MCPHub Hero Light" />
<img className="hidden dark:block" src="/images/hero-dark.png" alt="MCPHub Hero Dark" />

## 什么是 MCPHub？

MCPHub 是一个现代化的 Model Context Protocol (MCP) 服务器管理平台，旨在简化 AI 模型服务的部署、管理和监控。通过智能路由和负载均衡技术，MCPHub 帮助您构建高可用、可扩展的 AI 服务架构。

### 核心功能

- **🚀 智能路由** - 基于负载、延迟和健康状态的智能请求分发
- **⚖️ 负载均衡** - 多种负载均衡策略，确保最优性能
- **📊 实时监控** - 全面的性能指标和健康检查
- **🔐 安全认证** - 企业级身份认证和访问控制
- **🏗️ 服务器组管理** - 灵活的服务器分组和配置管理
- **🔄 故障转移** - 自动故障检测和流量切换

## 快速开始

立即开始使用 MCPHub，只需几分钟即可部署您的第一个 MCP 服务器。

<CardGroup cols={2}>
  <Card title="5 分钟快速部署" icon="rocket" href="/zh/quickstart">
    跟随我们的快速开始指南，5 分钟内部署 MCPHub 并连接您的第一个 MCP 服务器
  </Card>
  <Card title="开发环境搭建" icon="code" href="/zh/development/getting-started">
    设置本地开发环境，了解 MCPHub 的架构和开发工作流
  </Card>
</CardGroup>

## 核心概念

了解 MCPHub 的核心概念，为深入使用做好准备。

<CardGroup cols={2}>
  <Card title="MCP 协议介绍" icon="network-wired" href="/zh/concepts/mcp-protocol">
    深入了解 Model Context Protocol 的工作原理和最佳实践
  </Card>
  <Card title="智能路由机制" icon="route" href="/zh/features/smart-routing">
    学习 MCPHub 的智能路由算法和配置策略
  </Card>
  <Card title="服务器管理" icon="server" href="/zh/features/server-management">
    掌握 MCP 服务器的添加、配置和管理技巧
  </Card>
  <Card title="监控与分析" icon="chart-line" href="/zh/features/monitoring">
    使用内置的监控工具跟踪性能和识别问题
  </Card>
</CardGroup>

## 部署选项

MCPHub 支持多种部署方式，满足不同规模和场景的需求。

<CardGroup cols={3}>
  <Card title="Docker 部署" icon="docker" href="/zh/configuration/docker-setup">
    使用 Docker 容器快速部署，支持单机和集群模式
  </Card>
  <Card title="云服务部署" icon="cloud" href="/zh/deployment/cloud">
    在 AWS、GCP、Azure 等云平台上部署 MCPHub
  </Card>
  <Card title="Kubernetes" icon="dharmachakra" href="/zh/deployment/kubernetes">
    在 Kubernetes 集群中部署高可用的 MCPHub 服务
  </Card>
</CardGroup>

## API 和集成

MCPHub 提供完整的 RESTful API 和多语言 SDK，方便与现有系统集成。

<CardGroup cols={2}>
  <Card title="API 参考文档" icon="code" href="/zh/api-reference/introduction">
    完整的 API 接口文档，包含详细的请求示例和响应格式
  </Card>
  <Card title="SDK 和工具" icon="toolbox" href="/zh/sdk">
    官方 SDK 和命令行工具，加速开发集成
  </Card>
</CardGroup>

## 社区和支持

加入 MCPHub 社区，获取帮助和分享经验。

<CardGroup cols={2}>
  <Card title="GitHub 仓库" icon="github" href="https://github.com/mcphub/mcphub">
    查看源代码、提交问题和贡献代码
  </Card>
  <Card title="Discord 社区" icon="discord" href="https://discord.gg/mcphub">
    与其他开发者交流，获取实时帮助
  </Card>
  <Card title="Sponsor" icon="heart" href="https://ko-fi.com/samanhappy">
    支持 MCPHub 的开发和维护，帮助我们持续改进
  </Card>
</CardGroup>
