---
title: 'Nginx 配置'
description: '配置 Nginx 作为 MCPHub 的反向代理'
---

# Nginx 配置

本指南说明如何配置 Nginx 作为 MCPHub 的反向代理，包括 SSL 终止、负载均衡和缓存策略。

## 基本反向代理设置

### 配置文件

创建或更新您的 Nginx 配置文件（`/etc/nginx/sites-available/mcphub`）：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 将 HTTP 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL 配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 主应用程序
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # API 端点，为 MCP 操作设置更长的超时
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300;
        proxy_connect_timeout 60;
        proxy_send_timeout 60;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:3000;
        proxy_cache_valid 200 1d;
        proxy_cache_valid 404 1m;
        add_header Cache-Control "public, immutable";
        expires 1y;
    }
}
```

### 启用配置

```bash
# 创建符号链接启用站点
sudo ln -s /etc/nginx/sites-available/mcphub /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重新加载 Nginx
sudo systemctl reload nginx
```

## 负载均衡配置

对于具有多个 MCPHub 实例的高可用性设置：

```nginx
upstream mcphub_backend {
    least_conn;
    server 127.0.0.1:3000 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3001 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3002 weight=1 max_fails=3 fail_timeout=30s;

    # 健康检查（Nginx Plus 功能）
    # health_check interval=5s fails=3 passes=2;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL 和其他配置...

    location / {
        proxy_pass http://mcphub_backend;
        # 其他代理设置...
    }
}
```

## 缓存配置

### 浏览器缓存

```nginx
# 缓存静态资源
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    proxy_pass http://127.0.0.1:3000;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# 缓存 API 响应（小心动态内容）
location /api/public/ {
    proxy_pass http://127.0.0.1:3000;
    proxy_cache mcphub_cache;
    proxy_cache_valid 200 5m;
    proxy_cache_key "$scheme$request_method$host$request_uri";
    add_header X-Cache-Status $upstream_cache_status;
}
```

### Nginx 代理缓存

在 `nginx.conf` 的 `http` 块中添加：

```nginx
http {
    # 代理缓存配置
    proxy_cache_path /var/cache/nginx/mcphub
                     levels=1:2
                     keys_zone=mcphub_cache:10m
                     max_size=1g
                     inactive=60m
                     use_temp_path=off;

    # 其他配置...
}
```

## WebSocket 支持

对于实时功能和 SSE（服务器发送事件）：

```nginx
location /api/stream {
    proxy_pass http://127.0.0.1:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # 禁用实时响应的缓冲
    proxy_buffering off;
    proxy_cache off;

    # 长连接超时
    proxy_read_timeout 24h;
    proxy_send_timeout 24h;
}
```

## 安全配置

### 速率限制

```nginx
http {
    # 定义速率限制区域
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    server {
        # 对 API 端点应用速率限制
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            # 其他配置...
        }

        # 登录端点的严格速率限制
        location /api/auth/login {
            limit_req zone=login burst=5;
            # 其他配置...
        }
    }
}
```

### IP 白名单

```nginx
# 为管理端点允许特定 IP
location /api/admin/ {
    allow ***********/24;
    allow 10.0.0.0/8;
    deny all;

    proxy_pass http://127.0.0.1:3000;
    # 其他代理设置...
}
```

## 监控和日志

### 访问日志

```nginx
http {
    # 自定义日志格式
    log_format mcphub_format '$remote_addr - $remote_user [$time_local] '
                            '"$request" $status $body_bytes_sent '
                            '"$http_referer" "$http_user_agent" '
                            '$request_time $upstream_response_time';

    server {
        # 启用访问日志
        access_log /var/log/nginx/mcphub_access.log mcphub_format;
        error_log /var/log/nginx/mcphub_error.log;

        # 其他配置...
    }
}
```

### 状态页面

```nginx
location /nginx_status {
    stub_status;
    allow 127.0.0.1;
    deny all;
}
```

## Docker 集成

当在 Docker 中运行 MCPHub 时，更新代理配置：

```nginx
upstream mcphub_docker {
    server mcphub:3000;  # Docker 服务名
}

server {
    location / {
        proxy_pass http://mcphub_docker;
        # 其他代理设置...
    }
}
```

## 完整示例配置

使用提供的 `nginx.conf.example` 的生产就绪示例：

```bash
# 复制示例配置
cp nginx.conf.example /etc/nginx/sites-available/mcphub

# 使用您的域名和路径更新配置
sudo nano /etc/nginx/sites-available/mcphub

# 启用站点
sudo ln -s /etc/nginx/sites-available/mcphub /etc/nginx/sites-enabled/

# 测试并重新加载
sudo nginx -t && sudo systemctl reload nginx
```

## 故障排除

### 常见问题

**502 Bad Gateway**：检查 MCPHub 是否正在运行且可访问

**504 Gateway Timeout**：为长时间运行的操作增加 `proxy_read_timeout`

**WebSocket 连接失败**：确保正确的 `Upgrade` 和 `Connection` 头

**缓存问题**：清除代理缓存或在开发中禁用

### 调试命令

```bash
# 测试 Nginx 配置
sudo nginx -t

# 检查 Nginx 状态
sudo systemctl status nginx

# 查看错误日志
sudo tail -f /var/log/nginx/error.log

# 检查 MCPHub 是否响应
curl -I http://localhost:3000
```

## 性能优化

### 工作进程

```nginx
# 在 nginx.conf 中
worker_processes auto;
worker_connections 1024;
```

### 缓冲区大小

```nginx
proxy_buffering on;
proxy_buffer_size 128k;
proxy_buffers 4 256k;
proxy_busy_buffers_size 256k;
```

### Keep-Alive

```nginx
upstream mcphub_backend {
    server 127.0.0.1:3000;
    keepalive 32;
}

location / {
    proxy_pass http://mcphub_backend;
    proxy_http_version 1.1;
    proxy_set_header Connection "";
}
```

此配置为在 Nginx 后运行 MCPHub 提供了坚实的基础，具有适当的安全性、性能和可靠性功能。
