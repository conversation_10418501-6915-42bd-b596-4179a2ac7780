---
title: "组级别Bearer认证用户指南"
description: "设置和管理组级别Bearer认证的完整用户指南"
---

# 组级别Bearer认证用户指南

本指南将详细介绍如何在MCPHub中设置和管理组级别Bearer认证。

## 概述

组级别Bearer认证允许您：
- 为不同组设置独特的认证密钥
- 覆盖每个组的全局认证设置
- 实现细粒度访问控制
- 在不同工具集之间隔离安全问题

## 快速开始

### 前提条件

- MCPHub实例运行中（v2.0+）
- 具有创建/修改组的管理员权限
- 基本了解Bearer令牌认证

### 快速设置

1. **创建带认证的组**
   ```bash
   curl -X POST http://localhost:3000/api/groups \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
     -d '{
       "name": "secure-team",
       "displayName": "安全团队工具",
       "bearerAuthKey": "team-secure-key-2024"
     }'
   ```

2. **测试认证**
   ```bash
   curl -X POST http://localhost:3000/mcp/secure-team \
     -H "Authorization: Bearer team-secure-key-2024" \
     -H "Content-Type: application/json" \
     -d '{"jsonrpc": "2.0", "id": 1, "method": "tools/list"}'
   ```

## 分步配置

### 方法1：使用控制面板

1. **导航到组**
   - 打开MCPHub控制面板
   - 转到"组"部分
   - 点击"创建新组"或选择现有组

2. **配置认证**
   - 切换"启用Bearer认证"
   - 输入您的Bearer密钥（8-256字符）
   - 保存更改

3. **验证设置**
   - 使用新密钥测试API访问
   - 在控制面板中检查组状态

### 方法2：使用API

#### 创建带认证的组

```bash
curl -X POST http://localhost:3000/api/groups \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{
    "name": "production-tools",
    "displayName": "生产工具",
    "description": "关键生产管理工具",
    "bearerAuthKey": "prod-secure-key-2024-v1",
    "servers": ["monitoring", "deployment", "alerts"]
  }'
```

#### 更新现有组

```bash
# 为现有组添加认证
curl -X PUT http://localhost:3000/api/groups/production-tools \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{
    "bearerAuthKey": "new-prod-key-2024-v2"
  }'

# 从组中移除认证
curl -X PUT http://localhost:3000/api/groups/production-tools \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{
    "bearerAuthKey": ""
  }'
```

## 安全最佳实践

### 密钥生成

```bash
# 生成安全的Bearer密钥
openssl rand -base64 32  # 32字符密钥
openssl rand -hex 32     # 64字符十六进制密钥
```

### 密钥轮换

```bash
# 1. 生成新密钥
NEW_KEY=$(openssl rand -base64 32)

# 2. 使用新密钥更新组
curl -X PUT http://localhost:3000/api/groups/production-tools \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d "{\"bearerAuthKey\": \"$NEW_KEY\"}"
```

## 故障排除

### 常见问题

#### 1. 认证失败

```bash
# 错误：401 未授权
{
  "error": {
    "code": "AUTHENTICATION_FAILED",
    "message": "组的Bearer令牌无效"
  }
}
```

#### 2. 组未找到

```bash
# 错误：404 未找到
{
  "error": {
    "code": "GROUP_NOT_FOUND", 
    "message": "未找到组 'nonexistent-group'"
  }
}
```

#### 3. 无效的Bearer密钥格式

```bash
# 错误：400 错误请求
{
  "error": {
    "code": "INVALID_BEARER_KEY",
    "message": "Bearer密钥必须为8-256字符"
  }
}
```

### 调试命令

```bash
# 检查组配置
curl -X GET http://localhost:3000/api/groups/your-group \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# 测试认证
curl -X POST http://localhost:3000/mcp/your-group \
  -H "Authorization: Bearer your-group-key" \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "id": 1, "method": "tools/list"}'
```

## 迁移指南

### 从全局认证到组认证

```bash
# 1. 识别当前全局密钥
echo $BEARER_AUTH_KEY

# 2. 使用个别密钥创建组
curl -X POST http://localhost:3000/api/groups \
  -d '{
    "name": "legacy-tools",
    "bearerAuthKey": "'$BEARER_AUTH_KEY'",
    "servers": ["existing-server-1", "existing-server-2"]
  }'

# 3. 更新客户端使用组端点
# 旧：POST /mcp -> 新：POST /mcp/legacy-tools
```

## 认证优先级

1. **组Bearer密钥**：如果为特定组设置，则优先使用
2. **全局Bearer密钥**：如果未设置组特定密钥，则作为后备使用
3. **无认证**：如果两者都未配置（生产环境不推荐）

## 环境变量配置

```bash
# 全局Bearer认证（可选）
BEARER_AUTH_KEY=your-global-bearer-key-here

# 组级别密钥通过API或控制面板配置
# 无需额外的环境变量
```

本指南提供了在MCPHub中成功实施和管理组级别Bearer认证所需的一切信息。
