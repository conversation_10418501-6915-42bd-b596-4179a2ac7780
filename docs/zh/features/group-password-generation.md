# 分组管理密码生成功能

## 功能概述

在分组管理功能中，我们为编辑分组界面添加了密码生成功能，让用户能够轻松生成符合安全规范的Bearer认证密钥。

## 功能特性

### 🔐 安全密码生成
- **默认长度**: 48字符，适合Bearer认证使用
- **长度范围**: 32-64字符，可根据需要调整
- **字符集优化**: 排除容易混淆的字符（0、O、l、I）
- **加密安全**: 使用`crypto.getRandomValues()`确保密码的随机性

### 🎯 用户体验优化
- **一键生成**: 点击按钮即可生成安全密钥
- **自动填充**: 生成的密钥自动填充到输入框
- **即时显示**: 生成后自动显示密钥内容，方便用户查看
- **视觉反馈**: 提供清晰的按钮样式和图标

### ✅ 格式兼容性
- **Bearer认证格式**: 符合`/^[a-zA-Z0-9._-]+$/`正则表达式
- **长度验证**: 满足8-256字符的验证要求
- **向后兼容**: 不影响现有的手动输入功能

## 使用方法

### 创建新分组时
1. 打开"添加新分组"对话框
2. 填写分组名称和描述
3. 在"Bearer认证密钥"输入框下方，点击"生成安全密钥"按钮
4. 系统自动生成48字符的安全密钥并填充到输入框
5. 密钥自动显示，用户可以查看和复制
6. 保存分组设置

### 编辑现有分组时
1. 选择要编辑的分组，点击"编辑"
2. 在编辑对话框中找到"Bearer认证密钥"部分
3. 点击"生成安全密钥"按钮
4. 新密钥将替换原有密钥
5. 保存更改

## 技术实现

### 密钥生成算法
```typescript
export function generateSecureBearerKey(length: number = 48): string {
  // 确保长度在合理范围内
  const keyLength = Math.max(32, Math.min(64, length));
  
  // 使用安全字符集，排除混淆字符
  const safeCharacters = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789-._';
  
  const array = new Uint8Array(keyLength);
  crypto.getRandomValues(array);
  
  return Array.from(array)
    .map((x) => safeCharacters.charAt(x % safeCharacters.length))
    .join('');
}
```

### UI组件集成
- **AddGroupForm**: 添加分组表单中的密钥生成
- **EditGroupForm**: 编辑分组表单中的密钥生成
- **国际化支持**: 中英文界面文本
- **错误处理**: 生成失败时的用户提示

## 安全特性

### 密码强度
- **熵值**: 每个字符约5.8位熵，48字符总熵约278位
- **字符集**: 58个安全字符，避免视觉混淆
- **随机性**: 使用浏览器原生加密API

### 格式验证
- **前端验证**: 实时验证密钥格式
- **后端验证**: 服务器端二次验证
- **长度限制**: 严格的长度范围控制

## 测试覆盖

### 单元测试
- ✅ 密钥长度验证
- ✅ 字符集验证
- ✅ 唯一性测试
- ✅ 边界值测试
- ✅ 格式兼容性测试

### 集成测试
- ✅ 表单工作流程测试
- ✅ 用户交互测试
- ✅ 错误处理测试

## 国际化支持

### 中文界面
- `generateSecureKey`: "生成安全密钥"
- `keyGenerated`: "已生成新的安全密钥"
- `keyGenerationFailed`: "密钥生成失败"

### 英文界面
- `generateSecureKey`: "Generate Secure Key"
- `keyGenerated`: "New secure key generated"
- `keyGenerationFailed`: "Key generation failed"

## 最佳实践

### 使用建议
1. **定期更换**: 建议定期更换Bearer认证密钥
2. **安全存储**: 生成的密钥应安全存储，避免泄露
3. **权限控制**: 确保只有授权用户能够生成和查看密钥
4. **备份记录**: 重要的密钥应有备份和记录

### 安全注意事项
- 生成的密钥仅在客户端显示，不会被日志记录
- 密钥传输使用HTTPS加密
- 服务器端存储时进行适当的安全处理

## 兼容性

- **浏览器支持**: 支持所有现代浏览器（需要crypto API）
- **向后兼容**: 完全兼容现有的手动输入方式
- **移动端**: 支持移动端浏览器操作

这个功能大大简化了Bearer认证密钥的生成过程，提高了安全性和用户体验。
