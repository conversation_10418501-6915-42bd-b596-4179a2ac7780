---
title: '快速开始'
description: '5 分钟内部署 MCPHub 并连接您的第一个 MCP 服务器'
---

## 欢迎使用 MCPHub！

本指南将帮助您在 5 分钟内完成 MCPHub 的部署和配置，并连接您的第一个 MCP 服务器。

## 前提条件

在开始之前，请确保您的系统满足以下要求：

<AccordionGroup>
  <Accordion icon="desktop" title="系统要求">
    - **操作系统**: Linux、macOS 或 Windows
    - **内存**: 最少 2GB RAM（推荐 4GB+）
    - **存储**: 至少 1GB 可用空间
    - **网络**: 稳定的互联网连接
  </Accordion>

  <Accordion icon="code" title="软件依赖">
    - **Node.js**: 18.0+ 版本
    - **Docker**: 最新版本（可选，用于容器化部署）
    - **Git**: 用于代码管理
    
    检查版本：
    ```bash
    node --version  # 应该 >= 18.0.0
    npm --version   # 应该 >= 8.0.0
    docker --version # 可选
    ```
  </Accordion>
</AccordionGroup>

## 安装 MCPHub

### 方式一：使用 npm（推荐）

<AccordionGroup>
  <Accordion icon="download" title="安装 MCPHub CLI">
    首先安装 MCPHub 命令行工具：

    ```bash
    npm install -g @mcphub/cli
    ```

    验证安装：
    ```bash
    mcphub --version
    ```

  </Accordion>

  <Accordion icon="folder-plus" title="创建新项目">
    创建一个新的 MCPHub 项目：

    ```bash
    # 创建项目
    mcphub init my-mcphub-project
    cd my-mcphub-project

    # 安装依赖
    npm install
    ```

  </Accordion>

  <Accordion icon="gear" title="配置环境">
    复制并编辑环境变量文件：

    ```bash
    cp .env.example .env
    ```

    编辑 `.env` 文件，设置基本配置：
    ```bash
    # 服务器配置
    PORT=3000
    NODE_ENV=development

    # 数据库配置（使用内置 SQLite）
    DATABASE_URL=sqlite:./data/mcphub.db

    # JWT 密钥（请更改为安全的随机字符串）
    JWT_SECRET=your-super-secret-jwt-key-change-me

    # 管理员账户
    ADMIN_EMAIL=<EMAIL>
    ADMIN_PASSWORD=admin123
    ```

  </Accordion>
</AccordionGroup>

### 方式二：使用 Docker

<AccordionGroup>
  <Accordion icon="docker" title="Docker 快速部署">
    使用 Docker Compose 一键部署：

    ```bash
    # 下载配置文件
    curl -O https://raw.githubusercontent.com/mcphub/mcphub/main/docker-compose.yml

    # 启动服务
    docker-compose up -d
    ```

    或者直接运行 Docker 容器：
    ```bash
    docker run -d \
      --name mcphub \
      -p 3000:3000 \
      -e NODE_ENV=production \
      -e JWT_SECRET=your-secret-key \
      mcphub/server:latest
    ```

  </Accordion>
</AccordionGroup>

## 启动 MCPHub

### 开发模式启动

```bash
# 初始化数据库
npm run db:setup

# 启动开发服务器
npm run dev
```

### 生产模式启动

```bash
# 构建应用
npm run build

# 启动生产服务器
npm start
```

<Note>开发模式下，MCPHub 会在 `http://localhost:3000` 启动，并具有热重载功能。</Note>

## 首次访问和配置

### 1. 访问管理界面

打开浏览器，访问 `http://localhost:3000`，您将看到 MCPHub 的欢迎页面。

### 2. 登录管理员账户

使用您在 `.env` 文件中设置的管理员凭据登录：

- **邮箱**: `<EMAIL>`
- **密码**: `admin123`

<Warning>首次登录后，请立即更改默认密码以确保安全！</Warning>

### 3. 完成初始配置

登录后，系统会引导您完成初始配置：

1. **更改管理员密码**
2. **设置组织信息**
3. **配置基本设置**

## 添加您的第一个 MCP 服务器

### 1. 准备 MCP 服务器

如果您还没有 MCP 服务器，可以使用我们的示例服务器进行测试：

```bash
# 克隆示例服务器
git clone https://github.com/mcphub/example-mcp-server.git
cd example-mcp-server

# 安装依赖并启动
npm install
npm start
```

示例服务器将在 `http://localhost:3001` 启动。

### 2. 在 MCPHub 中添加服务器

在 MCPHub 管理界面中：

1. 点击 **"添加服务器"** 按钮
2. 填写服务器信息：
   ```
   名称: Example MCP Server
   端点: http://localhost:3001
   描述: 示例 MCP 服务器用于测试
   ```
3. 选择功能类型（如：chat、completion、analysis）
4. 点击 **"测试连接"** 验证服务器可达性
5. 点击 **"保存"** 完成添加

### 3. 验证服务器状态

添加成功后，您应该能在服务器列表中看到新添加的服务器，状态显示为 **"活跃"**（绿色）。

## 测试路由功能

### 发送测试请求

使用 cURL 或其他 HTTP 客户端测试路由功能：

```bash
# 发送聊天请求
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "Hello, this is a test message!"
      }
    ]
  }'
```

### 查看请求日志

在 MCPHub 管理界面的 **"监控"** 页面中，您可以实时查看：

- 请求数量和响应时间
- 服务器健康状态
- 错误日志和统计

## 后续步骤

恭喜！您已经成功部署了 MCPHub 并添加了第一个 MCP 服务器。接下来您可以：

<CardGroup cols={2}>
  <Card title="配置负载均衡" icon="balance-scale" href="/zh/features/smart-routing">
    学习如何配置智能路由和负载均衡策略
  </Card>
  <Card title="添加更多服务器" icon="plus" href="/zh/features/server-management">
    了解服务器管理的高级功能
  </Card>
  <Card title="设置监控告警" icon="bell" href="/zh/features/monitoring">
    配置性能监控和告警通知
  </Card>
  <Card title="API 集成" icon="code" href="/zh/api-reference/introduction">
    将 MCPHub 集成到您的应用程序中
  </Card>
</CardGroup>

## 常见问题

<AccordionGroup>
  <Accordion icon="question" title="无法连接到 MCP 服务器">
    **可能原因**：
    - 服务器地址错误或服务器未启动
    - 防火墙阻止连接
    - 网络配置问题

    **解决方案**：
    1. 验证服务器是否正在运行：`curl http://localhost:3001/health`
    2. 检查防火墙设置
    3. 确认网络连接正常

  </Accordion>

  <Accordion icon="question" title="服务器状态显示为离线">
    **可能原因**：
    - 健康检查失败
    - 服务器响应超时
    - 服务器崩溃或重启

    **解决方案**：
    1. 检查服务器日志
    2. 调整健康检查间隔
    3. 重启服务器进程

  </Accordion>

  <Accordion icon="question" title="忘记管理员密码">
    **解决方案**：
    ```bash
    # 重置管理员密码
    npm run reset-admin-password
    ```
    或者删除数据库文件重新初始化：
    ```bash
    rm data/mcphub.db
    npm run db:setup
    ```
  </Accordion>
</AccordionGroup>

## 获取帮助

如果您在设置过程中遇到问题：

- 📖 查看 [完整文档](/zh/development/getting-started)
- 🐛 在 [GitHub](https://github.com/mcphub/mcphub/issues) 上报告问题
- 💬 加入 [Discord 社区](https://discord.gg/mcphub) 获取实时帮助
- 📧 发送邮件至 <EMAIL>
