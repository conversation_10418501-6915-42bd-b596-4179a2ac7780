---
title: 'Markdown 语法'
description: 'MCPHub 文档的 Markdown 编写指南和最佳实践'
---

## 标题

在 MCPHub 文档中，每个页面应该只使用一个 `#` 标题，它会自动成为页面标题。

```md
# MCP 服务器配置指南

## 快速开始

### 安装依赖

#### 系统要求

##### Node.js 版本

###### 推荐版本
```

<ResponseExample>

# 标题 1

## 标题 2

### 标题 3

#### 标题 4

##### 标题 5

###### 标题 6

</ResponseExample>

## 文本格式

### 基本格式

MCPHub 文档支持标准的 Markdown 文本格式：

```md
**粗体文本** - 用于强调重要概念
_斜体文本_ - 用于强调或引用
`行内代码` - 用于命令、配置键或代码片段
~~删除线~~ - 用于标记过时的内容
```

**粗体文本** - 用于强调重要概念  
_斜体文本_ - 用于强调或引用  
`行内代码` - 用于命令、配置键或代码片段  
~~删除线~~ - 用于标记过时的内容

### 链接

#### 内部链接

链接到其他文档页面：

```md
查看 [服务器配置指南](/zh/configuration/mcp-settings) 获取详细信息。
```

查看 [服务器配置指南](/zh/configuration/mcp-settings) 获取详细信息。

#### 外部链接

```md
访问 [Model Context Protocol 官网](https://modelcontextprotocol.io) 了解更多。
```

访问 [Model Context Protocol 官网](https://modelcontextprotocol.io) 了解更多。

## 列表

### 无序列表

适用于功能列表、要求等：

```md
MCPHub 主要功能：

- 智能路由分发
- 服务器组管理
- 实时监控
- 身份认证
  - JWT 令牌
  - API 密钥
  - OAuth 2.0
```

MCPHub 主要功能：

- 智能路由分发
- 服务器组管理
- 实时监控
- 身份认证
  - JWT 令牌
  - API 密钥
  - OAuth 2.0

### 有序列表

适用于步骤说明、安装指南等：

```md
快速部署步骤：

1. 克隆仓库
2. 安装依赖
3. 配置环境变量
4. 启动服务
5. 验证部署
```

快速部署步骤：

1. 克隆仓库
2. 安装依赖
3. 配置环境变量
4. 启动服务
5. 验证部署

## 代码块

### 基本代码块

````md
```javascript
// MCPHub 客户端初始化
const mcpClient = new MCPClient({
  endpoint: 'https://api.mcphub.io',
  apiKey: process.env.MCPHUB_API_KEY,
});
```
````

```javascript
// MCPHub 客户端初始化
const mcpClient = new MCPClient({
  endpoint: 'https://api.mcphub.io',
  apiKey: process.env.MCPHUB_API_KEY,
});
```

### 配置文件示例

````md
```yaml title="docker-compose.yml"
version: '3.8'
services:
  mcphub:
    image: mcphub/server:latest
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - DATABASE_URL=******************************/mcphub
```
````

```yaml title="docker-compose.yml"
version: '3.8'
services:
  mcphub:
    image: mcphub/server:latest
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - DATABASE_URL=******************************/mcphub
```

### 终端命令

````md
```bash
# 安装 MCPHub CLI
npm install -g @mcphub/cli

# 初始化项目
mcphub init my-project

# 启动开发服务器
mcphub dev
```
````

```bash
# 安装 MCPHub CLI
npm install -g @mcphub/cli

# 初始化项目
mcphub init my-project

# 启动开发服务器
mcphub dev
```

## 表格

### 基本表格

```md
| 功能         | 开源版 | 企业版 |
| ------------ | ------ | ------ |
| 基础路由     | ✅     | ✅     |
| 智能负载均衡 | ❌     | ✅     |
| 高级监控     | ❌     | ✅     |
| 24/7 支持    | ❌     | ✅     |
```

| 功能         | 开源版 | 企业版 |
| ------------ | ------ | ------ |
| 基础路由     | ✅     | ✅     |
| 智能负载均衡 | ❌     | ✅     |
| 高级监控     | ❌     | ✅     |
| 24/7 支持    | ❌     | ✅     |

### API 参数表格

```md
| 参数名     | 类型    | 必需 | 描述                   |
| ---------- | ------- | ---- | ---------------------- |
| `serverId` | string  | 是   | 服务器唯一标识符       |
| `groupId`  | string  | 否   | 服务器组 ID            |
| `active`   | boolean | 否   | 是否激活（默认：true） |
```

| 参数名     | 类型    | 必需 | 描述                   |
| ---------- | ------- | ---- | ---------------------- |
| `serverId` | string  | 是   | 服务器唯一标识符       |
| `groupId`  | string  | 否   | 服务器组 ID            |
| `active`   | boolean | 否   | 是否激活（默认：true） |

## 引用块

### 信息提示

```md
> 📝 **提示**  
> 在生产环境中部署前，请确保已正确配置所有环境变量。
```

> 📝 **提示**  
> 在生产环境中部署前，请确保已正确配置所有环境变量。

### 警告信息

```md
> ⚠️ **警告**  
> 修改核心配置可能会影响系统稳定性，请谨慎操作。
```

> ⚠️ **警告**  
> 修改核心配置可能会影响系统稳定性，请谨慎操作。

## 任务列表

```md
- [x] 完成服务器配置
- [x] 设置数据库连接
- [ ] 配置负载均衡
- [ ] 设置监控告警
- [ ] 编写单元测试
```

- [x] 完成服务器配置
- [x] 设置数据库连接
- [ ] 配置负载均衡
- [ ] 设置监控告警
- [ ] 编写单元测试

## 水平分割线

用于分隔不同的内容部分：

```md
## 第一部分

内容...

---

## 第二部分

更多内容...
```

---

## 转义字符

当需要显示 Markdown 特殊字符时：

```md
\*这不是斜体\*
\`这不是代码\`
\[这不是链接\]
```

\*这不是斜体\*  
\`这不是代码\`  
\[这不是链接\]

## MCPHub 文档特定约定

### 配置项格式

环境变量和配置项使用特定格式：

```md
设置 `MCPHUB_PORT` 环境变量为 `3000`。
```

设置 `MCPHUB_PORT` 环境变量为 `3000`。

### API 端点格式

```md
`GET /api/servers/{id}` - 获取服务器详情
```

`GET /api/servers/{id}` - 获取服务器详情

### 版本标记

```md
该功能在 v2.1.0+ 版本中可用。
```

该功能在 v2.1.0+ 版本中可用。

## 最佳实践

1. **标题层级**：保持清晰的标题层级结构
2. **代码示例**：为所有代码块指定语言
3. **链接检查**：确保所有内部链接有效
4. **图片描述**：为图片添加有意义的 alt 文本
5. **一致性**：在整个文档中保持术语和格式一致

### 文档模板示例

````md
---
title: '功能名称'
description: '简短的功能描述'
---

## 概述

简要介绍该功能的用途和重要性。

## 快速开始

### 前提条件

- 系统要求
- 依赖软件

### 安装步骤

1. 第一步
2. 第二步
3. 第三步

```bash
# 示例命令
npm install example
```

## 配置

### 基本配置

| 配置项    | 类型   | 描述     |
| --------- | ------ | -------- |
| `option1` | string | 选项描述 |

### 高级配置

详细的配置说明...

## 示例

### 基本用法

```javascript
// 代码示例
const example = new Example();
```

### 高级用法

更复杂的使用场景...

## 故障排除

### 常见问题

**问题**：描述问题
**解决方案**：解决步骤

## 参考资料

- [相关文档链接](/link)
- [外部资源](https://example.com)
````
