# Analytics API 文档

## 概述

Analytics API 提供历史数据查询、趋势分析和统计功能，支持多维度筛选、分页查询和聚合统计。

## 基础信息

- **基础路径**: `/api/analytics`
- **认证**: 需要Bearer Token认证
- **内容类型**: `application/json`

## API 端点

### 1. 历史数据查询

**端点**: `GET /api/analytics/history`

**描述**: 查询历史调用记录，支持多维度筛选、分页和聚合统计。

#### 请求参数

| 参数名 | 类型 | 必需 | 描述 | 示例 |
|--------|------|------|------|------|
| startTime | string | 是 | 开始时间 (ISO 8601格式) | `2024-01-01T00:00:00Z` |
| endTime | string | 是 | 结束时间 (ISO 8601格式) | `2024-01-31T23:59:59Z` |
| serverNames | string | 否 | 服务器名称列表 (逗号分隔) | `server1,server2` |
| toolNames | string | 否 | 工具名称列表 (逗号分隔) | `tool1,tool2` |
| userIds | string | 否 | 用户ID列表 (逗号分隔) | `user1,user2` |
| success | boolean | 否 | 成功状态筛选 | `true` 或 `false` |
| page | number | 否 | 页码 (默认1) | `1` |
| pageSize | number | 否 | 每页大小 (默认20，最大1000) | `50` |
| groupBy | string | 否 | 聚合粒度 | `hour`, `day`, `week`, `month` |
| metrics | string | 否 | 聚合指标 (逗号分隔) | `count,success_rate,avg_response_time` |

#### 响应格式

```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "serverName": "example-server",
      "toolName": "example-tool",
      "callTime": "2024-01-15T10:30:00Z",
      "success": true,
      "responseTime": 150,
      "userId": "user123",
      "errorMessage": null,
      "requestParams": {...},
      "responseData": {...},
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "total": 100,
    "totalPages": 5
  },
  "aggregates": {
    "totalCalls": 100,
    "successRate": 95.5,
    "avgResponseTime": 145.2
  },
  "query": {
    "timeRange": {
      "startTime": "2024-01-01T00:00:00Z",
      "endTime": "2024-01-31T23:59:59Z"
    },
    "filters": {
      "serverNames": ["server1", "server2"]
    }
  }
}
```

#### 示例请求

```bash
curl -X GET "http://localhost:3001/api/analytics/history?startTime=2024-01-01T00:00:00Z&endTime=2024-01-31T23:59:59Z&page=1&pageSize=50&serverNames=server1,server2" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 历史数据摘要

**端点**: `GET /api/analytics/summary`

**描述**: 获取指定时间范围内的统计摘要，不返回详细记录。

#### 请求参数

| 参数名 | 类型 | 必需 | 描述 | 示例 |
|--------|------|------|------|------|
| startTime | string | 是 | 开始时间 (ISO 8601格式) | `2024-01-01T00:00:00Z` |
| endTime | string | 是 | 结束时间 (ISO 8601格式) | `2024-01-31T23:59:59Z` |
| serverNames | string | 否 | 服务器名称列表 (逗号分隔) | `server1,server2` |

#### 响应格式

```json
{
  "success": true,
  "summary": {
    "totalCalls": 1500,
    "successRate": 96.2,
    "avgResponseTime": 142.8
  },
  "timeRange": {
    "startTime": "2024-01-01T00:00:00Z",
    "endTime": "2024-01-31T23:59:59Z"
  },
  "filters": {
    "serverNames": ["server1", "server2"]
  }
}
```

### 3. 筛选选项

**端点**: `GET /api/analytics/filters`

**描述**: 获取可用的筛选选项，包括服务器名称、工具名称和用户ID列表。

#### 响应格式

```json
{
  "success": true,
  "filters": {
    "serverNames": ["server1", "server2", "server3"],
    "toolNames": ["tool1", "tool2", "tool3"],
    "userIds": ["user1", "user2", "user3"]
  }
}
```

## 错误处理

所有API端点都遵循统一的错误响应格式：

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息",
  "errors": [
    {
      "field": "参数名",
      "message": "参数错误描述"
    }
  ]
}
```

### 常见错误码

- **400 Bad Request**: 请求参数无效
- **401 Unauthorized**: 认证失败
- **500 Internal Server Error**: 服务器内部错误

## 性能指标

- **查询响应时间**: < 500ms (P95)
- **分页查询**: 支持最大1000条记录/页
- **并发查询**: 支持多用户同时查询
- **数据完整性**: 保证查询结果的准确性和一致性

## 使用示例

### JavaScript/TypeScript

```typescript
// 查询历史数据
const response = await fetch('/api/analytics/history?' + new URLSearchParams({
  startTime: '2024-01-01T00:00:00Z',
  endTime: '2024-01-31T23:59:59Z',
  page: '1',
  pageSize: '50',
  serverNames: 'server1,server2'
}), {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const data = await response.json();
console.log('历史数据:', data);

// 获取摘要统计
const summaryResponse = await fetch('/api/analytics/summary?' + new URLSearchParams({
  startTime: '2024-01-01T00:00:00Z',
  endTime: '2024-01-31T23:59:59Z'
}), {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const summary = await summaryResponse.json();
console.log('统计摘要:', summary);
```

## 注意事项

1. **时间格式**: 所有时间参数必须使用ISO 8601格式
2. **分页限制**: 单页最大返回1000条记录
3. **查询性能**: 大时间范围查询可能需要更长时间
4. **数据保留**: 历史数据根据配置的保留策略自动清理
5. **认证要求**: 所有端点都需要有效的Bearer Token

## 更新日志

- **v1.0.0** (2024-07-14): 初始版本，支持基础历史查询功能
