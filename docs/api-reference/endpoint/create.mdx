---
title: "Create Resources"
description: "Create new servers, groups, and other resources"
---

## Create Group

Create a new group with optional Bearer authentication.

### Endpoint

```http
POST /api/groups
```

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| name | string | Yes | Unique group identifier |
| displayName | string | No | Human-readable group name |
| description | string | No | Group description |
| bearerAuthKey | string | No | Group-specific Bearer authentication key (8-256 characters) |
| servers | array | No | Array of server names to include in the group |

### Request Example

```bash
curl -X POST http://localhost:3000/api/groups \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "secure-tools",
    "displayName": "Secure Tools Group",
    "description": "Tools requiring enhanced authentication",
    "bearerAuthKey": "your-secure-bearer-key-here",
    "servers": ["sensitive-api", "admin-tools"]
  }'
```

### Response

#### Success Response (201 Created)

```json
{
  "success": true,
  "message": "Group created successfully",
  "data": {
    "id": "secure-tools",
    "name": "secure-tools",
    "displayName": "Secure Tools Group",
    "description": "Tools requiring enhanced authentication",
    "bearerAuthKey": "your-secure-bearer-key-here",
    "servers": ["sensitive-api", "admin-tools"],
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

#### Error Responses

**400 Bad Request - Invalid Bearer Key**

```json
{
  "success": false,
  "error": {
    "code": "INVALID_BEARER_KEY",
    "message": "Bearer authentication key must be between 8 and 256 characters and contain only alphanumeric characters, hyphens, underscores, and dots",
    "details": {
      "field": "bearerAuthKey",
      "provided": "abc"
    }
  }
}
```

**409 Conflict - Group Already Exists**

```json
{
  "success": false,
  "error": {
    "code": "GROUP_ALREADY_EXISTS",
    "message": "A group with this name already exists",
    "details": {
      "groupName": "secure-tools"
    }
  }
}
```

## Update Group

Update an existing group, including Bearer authentication settings.

### Endpoint

```http
PUT /api/groups/{id}
```

### Parameters

| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| id | string | path | Yes | Group identifier |
| displayName | string | body | No | Updated display name |
| description | string | body | No | Updated description |
| bearerAuthKey | string | body | No | Updated Bearer key (empty string to remove) |
| servers | array | body | No | Updated server list |

### Request Example

```bash
# Update Bearer authentication key
curl -X PUT http://localhost:3000/api/groups/secure-tools \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "bearerAuthKey": "new-secure-bearer-key",
    "description": "Updated description"
  }'

# Remove Bearer authentication
curl -X PUT http://localhost:3000/api/groups/secure-tools \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "bearerAuthKey": ""
  }'
```

### Response

#### Success Response (200 OK)

```json
{
  "success": true,
  "message": "Group updated successfully",
  "data": {
    "id": "secure-tools",
    "name": "secure-tools",
    "displayName": "Secure Tools Group",
    "description": "Updated description",
    "bearerAuthKey": "new-secure-bearer-key",
    "servers": ["sensitive-api", "admin-tools"],
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T11:45:00Z"
  }
}
```
