import { parseAnomalyDescription, localizeAnomalyDescription, localizeAnomalyRecommendation } from '../../../frontend/src/utils/anomalyLocalizer';

// Mock translation function
const mockT = (key: string, params?: any) => {
  const translations: Record<string, string> = {
    'analytics.anomaly.descriptions.spike': '检测到峰值：{{value}} 高于预期',
    'analytics.anomaly.descriptions.drop': '检测到下降：{{value}} 低于预期',
    'analytics.anomaly.descriptions.significantSpike': '显著峰值：{{value}} 高于预期（{{percent}}% 增长）',
    'analytics.anomaly.descriptions.significantDrop': '显著下降：{{value}} 低于预期（{{percent}}% 下降）',
    'analytics.anomaly.descriptions.volatility': '检测到波动性：相对于近期趋势偏差 {{percent}}%',
    'analytics.anomaly.descriptions.trendBreak': '检测到趋势中断：{{value}} 偏离预期模式',
    'analytics.anomaly.descriptions.trendBreakDirection': '趋势中断：方向从{{from}}转为{{to}}',
    'analytics.anomaly.descriptions.directionUp': '上升',
    'analytics.anomaly.descriptions.directionDown': '下降',
    'analytics.anomaly.recommendations.callsDropHigh': '调查可能的服务中断或连接问题导致的API调用下降。',
    'analytics.anomaly.recommendations.callsSpikeHigh': '检查异常流量模式或潜在的DDoS攻击导致的API调用激增。',
    'analytics.anomaly.recommendations.general': '监控相关指标并调查异常原因。',
  };

  // Handle defaultValue parameter
  if (params && params.defaultValue !== undefined) {
    if (translations[key]) {
      let result = translations[key];
      // Simple template replacement
      Object.keys(params).forEach(paramKey => {
        if (paramKey !== 'defaultValue') {
          result = result.replace(new RegExp(`{{${paramKey}}}`, 'g'), params[paramKey]);
        }
      });
      return result;
    } else {
      return params.defaultValue;
    }
  }

  let result = translations[key] || key;

  // Simple template replacement
  if (params) {
    Object.keys(params).forEach(paramKey => {
      result = result.replace(new RegExp(`{{${paramKey}}}`, 'g'), params[paramKey]);
    });
  }

  return result;
};

describe('Anomaly Localizer', () => {
  describe('parseAnomalyDescription', () => {
    it('should parse spike description', () => {
      const description = 'Spike detected: 123.45 above expected';
      const result = parseAnomalyDescription(description);
      
      expect(result.type).toBe('spike');
      expect(result.params.value).toBe(123.45);
    });

    it('should parse drop description', () => {
      const description = 'Drop detected: 67.89 below expected';
      const result = parseAnomalyDescription(description);
      
      expect(result.type).toBe('drop');
      expect(result.params.value).toBe(67.89);
    });

    it('should parse significant spike description', () => {
      const description = 'Significant spike: 200.5 above expected (75% increase)';
      const result = parseAnomalyDescription(description);
      
      expect(result.type).toBe('significantSpike');
      expect(result.params.value).toBe(200.5);
      expect(result.params.percent).toBe(75);
    });

    it('should parse significant drop description', () => {
      const description = 'Significant drop: 50.25 below expected (60% decrease)';
      const result = parseAnomalyDescription(description);
      
      expect(result.type).toBe('significantDrop');
      expect(result.params.value).toBe(50.25);
      expect(result.params.percent).toBe(60);
    });

    it('should parse volatility description', () => {
      const description = 'Volatility detected: 25% deviation from recent trend';
      const result = parseAnomalyDescription(description);
      
      expect(result.type).toBe('volatility');
      expect(result.params.percent).toBe(25);
    });

    it('should parse trend break description', () => {
      const description = 'Trend break detected: 15.5 deviation from expected pattern';
      const result = parseAnomalyDescription(description);
      
      expect(result.type).toBe('trendBreak');
      expect(result.params.value).toBe(15.5);
    });

    it('should parse trend break direction description', () => {
      const description = 'Trend break: direction changed from up to down';
      const result = parseAnomalyDescription(description);
      
      expect(result.type).toBe('trendBreakDirection');
      expect(result.params.from).toBe('up');
      expect(result.params.to).toBe('down');
    });

    it('should handle unknown description', () => {
      const description = 'Unknown anomaly type detected';
      const result = parseAnomalyDescription(description);
      
      expect(result.type).toBe('unknown');
      expect(result.params).toEqual({});
    });
  });

  describe('localizeAnomalyDescription', () => {
    it('should localize spike description', () => {
      const description = 'Spike detected: 123.45 above expected';
      const result = localizeAnomalyDescription(description, mockT as any);

      expect(result).toBe('检测到峰值：123.45 高于预期');
    });

    it('should localize drop description', () => {
      const description = 'Drop detected: 67.89 below expected';
      const result = localizeAnomalyDescription(description, mockT as any);

      expect(result).toBe('检测到下降：67.89 低于预期');
    });

    it('should localize significant spike description', () => {
      const description = 'Significant spike: 200.5 above expected (75% increase)';
      const result = localizeAnomalyDescription(description, mockT as any);

      expect(result).toBe('显著峰值：200.5 高于预期（75% 增长）');
    });

    it('should localize volatility description', () => {
      const description = 'Volatility detected: 25% deviation from recent trend';
      const result = localizeAnomalyDescription(description, mockT as any);

      expect(result).toBe('检测到波动性：相对于近期趋势偏差 25%');
    });

    it('should localize trend break direction description', () => {
      const description = 'Trend break: direction changed from up to down';
      const result = localizeAnomalyDescription(description, mockT as any);

      expect(result).toBe('趋势中断：方向从上升转为下降');
    });

    it('should return original description for unknown type', () => {
      const description = 'Unknown anomaly type detected';
      const result = localizeAnomalyDescription(description, mockT as any);

      expect(result).toBe(description);
    });
  });

  describe('localizeAnomalyRecommendation', () => {
    it('should localize specific recommendation', () => {
      const recommendation = 'Investigate potential service outages';
      const result = localizeAnomalyRecommendation(recommendation, 'calls', 'drop', 'high', mockT as any);

      expect(result).toBe('调查可能的服务中断或连接问题导致的API调用下降。');
    });

    it('should fall back to general recommendation', () => {
      const recommendation = 'Monitor the situation';
      const result = localizeAnomalyRecommendation(recommendation, 'unknown', 'unknown', 'low', mockT as any);

      // Should return the general recommendation since it's available in translations
      expect(result).toBe('监控相关指标并调查异常原因。');
    });
  });
});
