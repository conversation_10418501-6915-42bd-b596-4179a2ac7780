import { formatValue, formatDate, formatTime, formatPercentage, formatNumber } from '../../../frontend/src/utils/formatters';

// Mock translation function
const mockT = (key: string, defaultValue?: string) => {
  const translations: Record<string, string> = {
    'format.locale': 'zh-CN',
    'format.currency': 'CNY',
    'format.dateTime.short': 'MM/dd HH:mm',
    'format.dateTime.medium': 'yyyy/MM/dd HH:mm:ss',
    'format.dateTime.long': 'yyyy年MM月dd日 HH:mm:ss',
    'format.number.decimal': '2',
    'format.number.thousand': ',',
    'format.number.decimalPoint': '.',
    'monitoring.units.ms': '毫秒',
  };
  
  return translations[key] || defaultValue || key;
};

describe('Formatters', () => {
  describe('formatValue', () => {
    it('should format calls correctly', () => {
      const result = formatValue(1234, 'calls', mockT);
      expect(result).toBe('1,234');
    });

    it('should format success_rate correctly', () => {
      const result = formatValue(95.5, 'success_rate', mockT);
      expect(result).toBe('95.5%');
    });

    it('should format response_time correctly', () => {
      const result = formatValue(123.7, 'response_time', mockT);
      expect(result).toBe('124毫秒');
    });

    it('should format unknown metric as number', () => {
      const result = formatValue(1234.56, 'unknown', mockT);
      expect(result).toBe('1,234.56');
    });
  });

  describe('formatDate', () => {
    const testDate = '2025-07-16T10:30:45.000Z';

    it('should format date in short format', () => {
      const result = formatDate(testDate, 'short', mockT);
      // The exact format depends on locale, but should contain date and time
      expect(result).toMatch(/\d{2}\/\d{2}\s+\d{2}:\d{2}/);
    });

    it('should format date in medium format', () => {
      const result = formatDate(testDate, 'medium', mockT);
      // Should contain year, month, day, hour, minute, second
      expect(result).toMatch(/\d{4}\/\d{2}\/\d{2}\s+\d{2}:\d{2}:\d{2}/);
    });

    it('should format date in long format', () => {
      const result = formatDate(testDate, 'long', mockT);
      // Should be a longer format with month name or Chinese format
      expect(result).toBeTruthy();
      expect(result.length).toBeGreaterThan(10);
    });
  });

  describe('formatTime', () => {
    it('should format time correctly', () => {
      const testDate = '2025-07-16T10:30:45.000Z';
      const result = formatTime(testDate, mockT);
      // Should contain hour and minute
      expect(result).toMatch(/\d{2}:\d{2}/);
    });
  });

  describe('formatPercentage', () => {
    it('should format percentage with default decimals', () => {
      const result = formatPercentage(95.567, undefined, mockT);
      expect(result).toBe('95.6%');
    });

    it('should format percentage with custom decimals', () => {
      const result = formatPercentage(95.567, 2, mockT);
      expect(result).toBe('95.57%');
    });
  });

  describe('formatNumber', () => {
    it('should format number with locale', () => {
      const result = formatNumber(1234567.89, undefined, mockT);
      expect(result).toBe('1,234,567.89');
    });

    it('should format number with custom decimals', () => {
      const result = formatNumber(1234.567, 1, mockT);
      expect(result).toBe('1,234.6');
    });

    it('should handle number without translation function', () => {
      const result = formatNumber(1234.567, 2);
      expect(result).toBe('1,234.57');
    });
  });
});
