// Tests for MCP Service data persistence enhancements

describe('MCP Service Data Persistence Enhancements', () => {
  describe('API Compatibility', () => {
    it('should maintain backward compatibility for ServerCallStats interface', () => {
      // Test that the interface structure is correct
      const mockCallStats = {
        totalCalls: 10,
        lastCallTime: Date.now(),
        successCalls: 8,
        failedCalls: 2
      };

      expect(typeof mockCallStats.totalCalls).toBe('number');
      expect(typeof mockCallStats.successCalls).toBe('number');
      expect(typeof mockCallStats.failedCalls).toBe('number');
      expect(typeof mockCallStats.lastCallTime).toBe('number');
      expect(mockCallStats.successCalls + mockCallStats.failedCalls).toBe(mockCallStats.totalCalls);
    });

    it('should handle empty call statistics', () => {
      const emptyCallStats = {
        totalCalls: 0,
        lastCallTime: undefined,
        successCalls: 0,
        failedCalls: 0
      };

      expect(emptyCallStats.totalCalls).toBe(0);
      expect(emptyCallStats.successCalls).toBe(0);
      expect(emptyCallStats.failedCalls).toBe(0);
      expect(emptyCallStats.lastCallTime).toBeUndefined();
    });
  });

  describe('Enhanced Statistics Features', () => {
    it('should support additional metrics in enhanced stats', () => {
      // Test enhanced statistics structure
      const enhancedStats = {
        totalCalls: 100,
        successCalls: 95,
        failedCalls: 5,
        lastCallTime: Date.now(),
        avgResponseTime: 250,
        minResponseTime: 50,
        maxResponseTime: 1000
      };

      expect(enhancedStats.avgResponseTime).toBe(250);
      expect(enhancedStats.minResponseTime).toBe(50);
      expect(enhancedStats.maxResponseTime).toBe(1000);
      expect(enhancedStats.avgResponseTime).toBeGreaterThanOrEqual(enhancedStats.minResponseTime);
      expect(enhancedStats.avgResponseTime).toBeLessThanOrEqual(enhancedStats.maxResponseTime);
    });

    it('should handle missing enhanced metrics gracefully', () => {
      // Test that basic stats work without enhanced metrics
      const basicStats: any = {
        totalCalls: 50,
        successCalls: 48,
        failedCalls: 2,
        lastCallTime: Date.now()
      };

      expect(basicStats.avgResponseTime).toBeUndefined();
      expect(basicStats.minResponseTime).toBeUndefined();
      expect(basicStats.maxResponseTime).toBeUndefined();
      // Basic functionality should still work
      expect(basicStats.totalCalls).toBe(50);
    });
  });

  describe('Performance Monitoring', () => {
    it('should track response times in tool calls', () => {
      // Test that response time tracking is properly implemented
      const startTime = Date.now();
      const endTime = startTime + 250; // 250ms response time
      const responseTime = endTime - startTime;
      
      expect(responseTime).toBe(250);
      expect(typeof responseTime).toBe('number');
      expect(responseTime).toBeGreaterThan(0);
    });

    it('should handle undefined response times gracefully', () => {
      // Test that the system handles cases where response time is not available
      const responseTime = undefined;
      
      expect(responseTime).toBeUndefined();
      // The system should not crash when responseTime is undefined
    });
  });

  describe('Error Handling', () => {
    it('should capture error messages in failed calls', () => {
      const error = new Error('Tool execution failed');
      const errorMessage = error.message;
      
      expect(errorMessage).toBe('Tool execution failed');
      expect(typeof errorMessage).toBe('string');
    });

    it('should handle non-Error objects gracefully', () => {
      const error = 'String error';
      const errorMessage = String(error);
      
      expect(errorMessage).toBe('String error');
      expect(typeof errorMessage).toBe('string');
    });
  });
});
