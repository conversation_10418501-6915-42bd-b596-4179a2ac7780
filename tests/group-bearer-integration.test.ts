/**
 * 组级别Bearer认证功能完整集成测试
 * 测试端到端的认证场景，包括SSE连接、工具调用等
 */

import { Request } from 'express';

// Mock group service
const mockGroupService = {
  getGroupByIdOrName: jest.fn(),
};

// Mock request helper
const mockRequest = (authHeader?: string): Partial<Request> => ({
  headers: authHeader ? { authorization: authHeader } : {},
});

// 复制auth middleware中的validateBearerAuth逻辑用于测试
const validateBearerAuth = (req: Request, routingConfig: any, group?: string): boolean => {
  if (!routingConfig.enableBearerAuth) {
    return false;
  }

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }

  const token = authHeader.substring(7); // Remove "Bearer " prefix

  // If group is provided, check for group-level Bearer key first
  if (group) {
    const groupInfo = mockGroupService.getGroupByIdOrName(group);

    if (groupInfo && groupInfo.bearerAuthKey) {
      // Group has its own Bearer key, use it for authentication
      return token === groupInfo.bearerAuthKey;
    }
    // If group doesn't have its own Bearer key, fall back to global key
  }

  // Use global Bearer key for authentication
  return token === routingConfig.bearerAuthKey;
};

// 复制sseService中的validateBearerAuth逻辑用于测试
const sseValidateBearerAuth = (req: Request, routingConfig: any, group?: string): boolean => {
  return validateBearerAuth(req, routingConfig, group);
};

describe('Group-Level Bearer Authentication Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const routingConfig = {
    enableBearerAuth: true,
    bearerAuthKey: 'global-test-key',
  };

  describe('End-to-End Authentication Scenarios', () => {
    it('should handle complete authentication flow for group with Bearer key', () => {
      // 设置测试数据
      const groupInfo = {
        id: 'test-group-1',
        name: 'Test Group 1',
        bearerAuthKey: 'group-specific-key-123',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);

      // 测试正确的组级别认证
      const req = mockRequest('Bearer group-specific-key-123') as Request;
      const result = validateBearerAuth(req, routingConfig, 'test-group-1');

      expect(result).toBe(true);
      expect(mockGroupService.getGroupByIdOrName).toHaveBeenCalledWith('test-group-1');
    });

    it('should handle complete authentication flow for group without Bearer key', () => {
      // 设置测试数据 - 组没有Bearer key
      const groupInfo = {
        id: 'test-group-2',
        name: 'Test Group 2',
        servers: [],
        // 没有 bearerAuthKey 字段
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);

      // 测试回退到全局认证
      const req = mockRequest('Bearer global-test-key') as Request;
      const result = validateBearerAuth(req, routingConfig, 'test-group-2');

      expect(result).toBe(true);
      expect(mockGroupService.getGroupByIdOrName).toHaveBeenCalledWith('test-group-2');
    });

    it('should handle authentication for non-existent group', () => {
      // 设置测试数据 - 组不存在
      mockGroupService.getGroupByIdOrName.mockReturnValue(undefined);

      // 测试回退到全局认证
      const req = mockRequest('Bearer global-test-key') as Request;
      const result = validateBearerAuth(req, routingConfig, 'non-existent-group');

      expect(result).toBe(true);
      expect(mockGroupService.getGroupByIdOrName).toHaveBeenCalledWith('non-existent-group');
    });
  });

  describe('SSE Service Authentication Integration', () => {
    it('should authenticate SSE connections with group-level Bearer key', () => {
      const groupInfo = {
        id: 'sse-test-group',
        name: 'SSE Test Group',
        bearerAuthKey: 'sse-group-key-456',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);

      const req = mockRequest('Bearer sse-group-key-456') as Request;
      const result = sseValidateBearerAuth(req, routingConfig, 'sse-test-group');

      expect(result).toBe(true);
      expect(mockGroupService.getGroupByIdOrName).toHaveBeenCalledWith('sse-test-group');
    });

    it('should reject SSE connections with invalid group Bearer key', () => {
      const groupInfo = {
        id: 'sse-test-group',
        name: 'SSE Test Group',
        bearerAuthKey: 'sse-group-key-456',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);

      const req = mockRequest('Bearer invalid-sse-key') as Request;
      const result = sseValidateBearerAuth(req, routingConfig, 'sse-test-group');

      expect(result).toBe(false);
      expect(mockGroupService.getGroupByIdOrName).toHaveBeenCalledWith('sse-test-group');
    });
  });

  describe('Authentication Priority and Fallback Logic', () => {
    it('should demonstrate complete priority chain: group key > global key', () => {
      const groupWithKey = {
        id: 'priority-group',
        name: 'Priority Group',
        bearerAuthKey: 'priority-group-key',
        servers: [],
      };

      const groupWithoutKey = {
        id: 'fallback-group',
        name: 'Fallback Group',
        servers: [],
        // 没有 bearerAuthKey
      };

      // 测试场景1：组有自己的key，应该使用组key
      mockGroupService.getGroupByIdOrName.mockReturnValueOnce(groupWithKey);
      const req1 = mockRequest('Bearer priority-group-key') as Request;
      const result1 = validateBearerAuth(req1, routingConfig, 'priority-group');
      expect(result1).toBe(true);

      // 测试场景2：组有自己的key，但使用全局key应该失败
      mockGroupService.getGroupByIdOrName.mockReturnValueOnce(groupWithKey);
      const req2 = mockRequest('Bearer global-test-key') as Request;
      const result2 = validateBearerAuth(req2, routingConfig, 'priority-group');
      expect(result2).toBe(false);

      // 测试场景3：组没有key，应该回退到全局key
      mockGroupService.getGroupByIdOrName.mockReturnValueOnce(groupWithoutKey);
      const req3 = mockRequest('Bearer global-test-key') as Request;
      const result3 = validateBearerAuth(req3, routingConfig, 'fallback-group');
      expect(result3).toBe(true);

      // 测试场景4：组不存在，应该回退到全局key
      mockGroupService.getGroupByIdOrName.mockReturnValueOnce(undefined);
      const req4 = mockRequest('Bearer global-test-key') as Request;
      const result4 = validateBearerAuth(req4, routingConfig, 'non-existent');
      expect(result4).toBe(true);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty Bearer key in group configuration', () => {
      const groupWithEmptyKey = {
        id: 'empty-key-group',
        name: 'Empty Key Group',
        bearerAuthKey: '', // 空字符串
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupWithEmptyKey);

      // 空的Bearer key应该回退到全局认证
      const req = mockRequest('Bearer global-test-key') as Request;
      const result = validateBearerAuth(req, routingConfig, 'empty-key-group');

      expect(result).toBe(true);
    });

    it('should handle null Bearer key in group configuration', () => {
      const groupWithNullKey = {
        id: 'null-key-group',
        name: 'Null Key Group',
        bearerAuthKey: null as any, // null值
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupWithNullKey);

      // null的Bearer key应该回退到全局认证
      const req = mockRequest('Bearer global-test-key') as Request;
      const result = validateBearerAuth(req, routingConfig, 'null-key-group');

      expect(result).toBe(true);
    });

    it('should handle authentication when Bearer auth is globally disabled', () => {
      const disabledConfig = {
        enableBearerAuth: false,
        bearerAuthKey: 'global-test-key',
      };

      const groupInfo = {
        id: 'test-group',
        name: 'Test Group',
        bearerAuthKey: 'group-key',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);

      const req = mockRequest('Bearer group-key') as Request;
      const result = validateBearerAuth(req, disabledConfig, 'test-group');

      // 当全局Bearer认证被禁用时，应该返回false
      expect(result).toBe(false);
    });
  });

  describe('Performance and Consistency Tests', () => {
    it('should maintain consistent performance across multiple authentication calls', () => {
      const groupInfo = {
        id: 'perf-test-group',
        name: 'Performance Test Group',
        bearerAuthKey: 'perf-test-key',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);

      const req = mockRequest('Bearer perf-test-key') as Request;

      // 执行多次认证调用，确保结果一致
      const results = [];
      for (let i = 0; i < 10; i++) {
        results.push(validateBearerAuth(req, routingConfig, 'perf-test-group'));
      }

      // 所有结果应该一致
      expect(results.every((result: boolean) => result === true)).toBe(true);
      expect(mockGroupService.getGroupByIdOrName).toHaveBeenCalledTimes(10);
    });

    it('should handle concurrent authentication requests correctly', async () => {
      const groupInfo = {
        id: 'concurrent-test-group',
        name: 'Concurrent Test Group',
        bearerAuthKey: 'concurrent-test-key',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);

      const req = mockRequest('Bearer concurrent-test-key') as Request;

      // 模拟并发认证请求
      const promises = Array.from({ length: 5 }, () =>
        Promise.resolve(validateBearerAuth(req, routingConfig, 'concurrent-test-group'))
      );

      const results = await Promise.all(promises);

      // 所有并发请求都应该成功
      expect(results.every((result: boolean) => result === true)).toBe(true);
    });
  });
});
