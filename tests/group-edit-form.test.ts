// Test for Group Edit Form Bearer key functionality

describe('Group Edit Form Bearer Key Validation', () => {

  // Copy the validateBearerKeyFormat function from EditGroupForm for testing
  const validateBearerKeyFormat = (bearerKey: string): boolean => {
    if (!bearerKey) return true // Empty is valid (optional field)
    const bearerKeyRegex = /^[a-zA-Z0-9._-]+$/
    return bearerKey.length >= 8 && bearerKey.length <= 256 && bearerKeyRegex.test(bearerKey)
  }

  it('should validate empty bearer key as valid', () => {
    expect(validateBearerKeyFormat('')).toBe(true)
  })

  it('should validate valid bearer key format', () => {
    expect(validateBearerKeyFormat('valid-bearer-key-123')).toBe(true)
    expect(validateBearerKeyFormat('test_key.with-special_chars')).toBe(true)
    expect(validateBearerKeyFormat('12345678')).toBe(true) // Minimum length
  })

  it('should reject bearer key that is too short', () => {
    expect(validateBearerKeyFormat('short')).toBe(false) // Less than 8 characters
    expect(validateBearerKeyFormat('1234567')).toBe(false) // Exactly 7 characters
  })

  it('should reject bearer key that is too long', () => {
    const longKey = 'a'.repeat(257) // More than 256 characters
    expect(validateBearerKeyFormat(longKey)).toBe(false)
  })

  it('should reject bearer key with invalid characters', () => {
    expect(validateBearerKeyFormat('invalid@key')).toBe(false) // Contains @
    expect(validateBearerKeyFormat('invalid#key')).toBe(false) // Contains #
    expect(validateBearerKeyFormat('invalid$key')).toBe(false) // Contains $
    expect(validateBearerKeyFormat('invalid key')).toBe(false) // Contains space
    expect(validateBearerKeyFormat('invalid%key')).toBe(false) // Contains %
  })

  it('should accept bearer key with valid special characters', () => {
    expect(validateBearerKeyFormat('valid-key')).toBe(true) // Contains -
    expect(validateBearerKeyFormat('valid_key')).toBe(true) // Contains _
    expect(validateBearerKeyFormat('valid.key')).toBe(true) // Contains .
    expect(validateBearerKeyFormat('valid-key_with.all-chars123')).toBe(true) // All valid chars
  })

  it('should validate bearer key length boundaries', () => {
    const minValidKey = 'a'.repeat(8) // Exactly 8 characters
    const maxValidKey = 'a'.repeat(256) // Exactly 256 characters

    expect(validateBearerKeyFormat(minValidKey)).toBe(true)
    expect(validateBearerKeyFormat(maxValidKey)).toBe(true)
  })
})
