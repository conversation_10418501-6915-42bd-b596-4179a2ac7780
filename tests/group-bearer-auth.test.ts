// Test for group-level Bearer authentication functionality

import { Request } from 'express';

// Mock group service
const mockGroupService = {
  getGroupByIdOrName: jest.fn(),
};

// Mock settings
const mockSettings = {
  systemConfig: {
    routing: {
      enableBearerAuth: true,
      bearerAuthKey: 'global-bearer-key',
    },
  },
};

// Mock loadSettings function
const _mockLoadSettings = jest.fn(() => mockSettings);

// Mock the validateBearerAuth function logic from auth middleware
const validateBearerAuthLogic = (req: Request, routingConfig: any, group?: string): boolean => {
  if (!routingConfig.enableBearerAuth) {
    return false;
  }

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }

  const token = authHeader.substring(7); // Remove "Bearer " prefix

  // If group is provided, check for group-level Bearer key first
  if (group) {
    const groupInfo = mockGroupService.getGroupByIdOrName(group);
    
    if (groupInfo && groupInfo.bearerAuthKey) {
      // Group has its own Bearer key, use it for authentication
      console.log(`Using group-level Bearer authentication for group: ${group}`);
      return token === groupInfo.bearerAuthKey;
    }
    // If group doesn't have its own Bearer key, fall back to global key
    console.log(`Group ${group} has no Bearer key, falling back to global authentication`);
  }

  // Use global Bearer key for authentication
  return token === routingConfig.bearerAuthKey;
};

// Helper function to create mock request
const mockRequest = (authHeader?: string): Partial<Request> => ({
  headers: {
    authorization: authHeader,
  },
});

describe('Group-Level Bearer Authentication Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateBearerAuth Logic', () => {

    const routingConfig = {
      enableBearerAuth: true,
      bearerAuthKey: 'global-bearer-key',
    };

    it('should return false when Bearer auth is disabled', () => {
      const req = mockRequest('Bearer some-token') as Request;
      const config = { ...routingConfig, enableBearerAuth: false };

      const result = validateBearerAuthLogic(req, config);

      expect(result).toBe(false);
    });

    it('should return false when no authorization header is provided', () => {
      const req = mockRequest() as Request;

      const result = validateBearerAuthLogic(req, routingConfig);

      expect(result).toBe(false);
    });

    it('should return false when authorization header does not start with Bearer', () => {
      const req = mockRequest('Basic some-token') as Request;

      const result = validateBearerAuthLogic(req, routingConfig);

      expect(result).toBe(false);
    });

    it('should authenticate with global Bearer key when no group is specified', () => {
      const req = mockRequest('Bearer global-bearer-key') as Request;

      const result = validateBearerAuthLogic(req, routingConfig);

      expect(result).toBe(true);
    });

    it('should reject invalid global Bearer key when no group is specified', () => {
      const req = mockRequest('Bearer invalid-key') as Request;

      const result = validateBearerAuthLogic(req, routingConfig);

      expect(result).toBe(false);
    });

    it('should authenticate with group Bearer key when group has its own key', () => {
      const req = mockRequest('Bearer group-specific-key') as Request;
      const groupInfo = {
        id: 'test-group',
        name: 'Test Group',
        bearerAuthKey: 'group-specific-key',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);

      const result = validateBearerAuthLogic(req, routingConfig, 'test-group');

      expect(result).toBe(true);
      expect(mockGroupService.getGroupByIdOrName).toHaveBeenCalledWith('test-group');
    });

    it('should reject invalid group Bearer key', () => {
      const req = mockRequest('Bearer invalid-group-key') as Request;
      const groupInfo = {
        id: 'test-group',
        name: 'Test Group',
        bearerAuthKey: 'group-specific-key',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);

      const result = validateBearerAuthLogic(req, routingConfig, 'test-group');

      expect(result).toBe(false);
      expect(mockGroupService.getGroupByIdOrName).toHaveBeenCalledWith('test-group');
    });

    it('should fall back to global Bearer key when group has no Bearer key', () => {
      const req = mockRequest('Bearer global-bearer-key') as Request;
      const groupInfo = {
        id: 'test-group',
        name: 'Test Group',
        servers: [],
        // No bearerAuthKey property
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);

      const result = validateBearerAuthLogic(req, routingConfig, 'test-group');

      expect(result).toBe(true);
      expect(mockGroupService.getGroupByIdOrName).toHaveBeenCalledWith('test-group');
    });

    it('should fall back to global Bearer key when group is not found', () => {
      const req = mockRequest('Bearer global-bearer-key') as Request;

      mockGroupService.getGroupByIdOrName.mockReturnValue(undefined);

      const result = validateBearerAuthLogic(req, routingConfig, 'non-existent-group');

      expect(result).toBe(true);
      expect(mockGroupService.getGroupByIdOrName).toHaveBeenCalledWith('non-existent-group');
    });

    it('should prioritize group Bearer key over global key', () => {
      const req = mockRequest('Bearer group-specific-key') as Request;
      const groupInfo = {
        id: 'test-group',
        name: 'Test Group',
        bearerAuthKey: 'group-specific-key',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);

      // Should succeed with group key
      const result1 = validateBearerAuthLogic(req, routingConfig, 'test-group');
      expect(result1).toBe(true);

      // Should fail with global key when group is specified
      const req2 = mockRequest('Bearer global-bearer-key') as Request;
      const result2 = validateBearerAuthLogic(req2, routingConfig, 'test-group');
      expect(result2).toBe(false);
    });
  });

  describe('Authentication Priority Logic', () => {
    it('should demonstrate correct priority: group key > global key', () => {
      const routingConfig = {
        enableBearerAuth: true,
        bearerAuthKey: 'global-key',
      };

      const groupInfo = {
        id: 'priority-test-group',
        name: 'Priority Test Group',
        bearerAuthKey: 'group-key',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);

      // Test 1: Group key should work
      const reqWithGroupKey = mockRequest('Bearer group-key') as Request;
      const result1 = validateBearerAuthLogic(reqWithGroupKey, routingConfig, 'priority-test-group');
      expect(result1).toBe(true);

      // Test 2: Global key should NOT work when group has its own key
      const reqWithGlobalKey = mockRequest('Bearer global-key') as Request;
      const result2 = validateBearerAuthLogic(reqWithGlobalKey, routingConfig, 'priority-test-group');
      expect(result2).toBe(false);

      // Test 3: Global key should work when no group is specified
      const result3 = validateBearerAuthLogic(reqWithGlobalKey, routingConfig);
      expect(result3).toBe(true);
    });
  });
});

// Additional tests for API parameter validation
describe('Group API Bearer Key Validation', () => {

  // Mock validateBearerKeyFormat function from groupController
  const validateBearerKeyFormat = (bearerKey: string): boolean => {
    const bearerKeyRegex = /^[a-zA-Z0-9._-]+$/;
    return bearerKey.length >= 8 && bearerKey.length <= 256 && bearerKeyRegex.test(bearerKey);
  };

  describe('validateBearerKeyFormat', () => {

    it('should accept valid bearer keys', () => {
      const validKeys = [
        'valid-key-123',
        'another_valid.key',
        'UPPERCASE-key',
        'mixed.Case_Key-123',
        '********', // minimum length
        'a'.repeat(256), // maximum length
      ];

      validKeys.forEach(key => {
        expect(validateBearerKeyFormat(key)).toBe(true);
      });
    });

    it('should reject bearer keys that are too short', () => {
      const shortKeys = [
        '',
        'a',
        'ab',
        'abc',
        'abcd',
        'abcde',
        'abcdef',
        'abcdefg', // 7 characters, below minimum
      ];

      shortKeys.forEach(key => {
        expect(validateBearerKeyFormat(key)).toBe(false);
      });
    });

    it('should reject bearer keys that are too long', () => {
      const longKey = 'a'.repeat(257); // 257 characters, above maximum
      expect(validateBearerKeyFormat(longKey)).toBe(false);
    });

    it('should reject bearer keys with invalid characters', () => {
      const invalidKeys = [
        'invalid key with spaces',
        'invalid@key',
        'invalid#key',
        'invalid$key',
        'invalid%key',
        'invalid&key',
        'invalid*key',
        'invalid+key',
        'invalid=key',
        'invalid[key]',
        'invalid{key}',
        'invalid|key',
        'invalid\\key',
        'invalid/key',
        'invalid?key',
        'invalid<key>',
        'invalid,key',
        'invalid;key',
        'invalid:key',
        'invalid"key"',
        "invalid'key'",
        'invalid`key`',
        'invalid~key',
        'invalid!key',
      ];

      invalidKeys.forEach(key => {
        expect(validateBearerKeyFormat(key)).toBe(false);
      });
    });

    it('should accept bearer keys with allowed special characters', () => {
      const validKeys = [
        'key-with-hyphens',
        'key_with_underscores',
        'key.with.dots',
        'key-with_mixed.special-chars',
      ];

      validKeys.forEach(key => {
        expect(validateBearerKeyFormat(key)).toBe(true);
      });
    });

  });

});

// Test for Bearer key clearing functionality
describe('Bearer Key Clearing Functionality', () => {
  
  // Mock updateGroup function from groupService
  const _mockUpdateGroup = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should clear Bearer key when empty string is provided', () => {
    // Simulate the updateGroup logic
    const updateGroupLogic = (id: string, data: any) => {
      const updatedData = { ...data };
      if ('bearerAuthKey' in data) {
        if (data.bearerAuthKey === '' || data.bearerAuthKey === undefined) {
          updatedData.bearerAuthKey = undefined;
        }
      }
      
      // Simulate removing undefined bearerAuthKey
      if (updatedData.bearerAuthKey === undefined) {
        delete updatedData.bearerAuthKey;
      }
      
      return updatedData;
    };

    // Test with empty string
    const result1 = updateGroupLogic('test-group', { 
      name: 'Test Group',
      bearerAuthKey: '' 
    });
    
    expect(result1).toEqual({ name: 'Test Group' });
    expect('bearerAuthKey' in result1).toBe(false);

    // Test with undefined
    const result2 = updateGroupLogic('test-group', { 
      name: 'Test Group',
      bearerAuthKey: undefined 
    });
    
    expect(result2).toEqual({ name: 'Test Group' });
    expect('bearerAuthKey' in result2).toBe(false);
  });

  it('should preserve Bearer key when valid value is provided', () => {
    const updateGroupLogic = (id: string, data: any) => {
      const updatedData = { ...data };
      if ('bearerAuthKey' in data) {
        if (data.bearerAuthKey === '' || data.bearerAuthKey === undefined) {
          updatedData.bearerAuthKey = undefined;
        }
      }
      
      if (updatedData.bearerAuthKey === undefined) {
        delete updatedData.bearerAuthKey;
      }
      
      return updatedData;
    };

    const result = updateGroupLogic('test-group', { 
      name: 'Test Group',
      bearerAuthKey: 'valid-bearer-key' 
    });
    
    expect(result).toEqual({ 
      name: 'Test Group',
      bearerAuthKey: 'valid-bearer-key'
    });
  });

  it('should handle case where bearerAuthKey is not in data object', () => {
    const updateGroupLogic = (id: string, data: any) => {
      const updatedData = { ...data };
      if ('bearerAuthKey' in data) {
        if (data.bearerAuthKey === '' || data.bearerAuthKey === undefined) {
          updatedData.bearerAuthKey = undefined;
        }
      }
      
      if (updatedData.bearerAuthKey === undefined) {
        delete updatedData.bearerAuthKey;
      }
      
      return updatedData;
    };

    const result = updateGroupLogic('test-group', { 
      name: 'Test Group',
      description: 'Test Description'
    });
    
    expect(result).toEqual({ 
      name: 'Test Group',
      description: 'Test Description'
    });
    expect('bearerAuthKey' in result).toBe(false);
  });

});
