import { generateSecureBearerKey } from '../frontend/src/utils/key'

describe('Group Password Generation Feature', () => {
  describe('Integration with Group Forms', () => {
    it('should generate secure keys suitable for Bearer authentication', () => {
      const key = generateSecureBearerKey()
      
      // 验证长度
      expect(key.length).toBe(48)
      
      // 验证字符集 - 应该符合Bearer认证格式
      const bearerKeyRegex = /^[a-zA-Z0-9._-]+$/
      expect(key).toMatch(bearerKeyRegex)
      
      // 验证不包含混淆字符
      expect(key).not.toMatch(/[0OlI]/)
      
      // 验证符合表单验证规则
      expect(key.length).toBeGreaterThanOrEqual(8)
      expect(key.length).toBeLessThanOrEqual(256)
    })

    it('should generate different keys for multiple groups', () => {
      const keys = []
      for (let i = 0; i < 5; i++) {
        keys.push(generateSecureBearerKey())
      }
      
      // 所有密钥应该是唯一的
      const uniqueKeys = new Set(keys)
      expect(uniqueKeys.size).toBe(5)
      
      // 每个密钥都应该符合格式要求
      keys.forEach(key => {
        expect(key).toMatch(/^[a-zA-Z0-9._-]+$/)
        expect(key.length).toBe(48)
      })
    })

    it('should handle custom length requirements', () => {
      // 测试不同长度的密钥生成
      const shortKey = generateSecureBearerKey(32)
      const longKey = generateSecureBearerKey(64)
      
      expect(shortKey.length).toBe(32)
      expect(longKey.length).toBe(64)
      
      // 都应该符合Bearer认证格式
      expect(shortKey).toMatch(/^[a-zA-Z0-9._-]+$/)
      expect(longKey).toMatch(/^[a-zA-Z0-9._-]+$/)
    })

    it('should enforce security constraints', () => {
      // 测试长度限制
      const tooShort = generateSecureBearerKey(10) // 应该被限制为32
      const tooLong = generateSecureBearerKey(100) // 应该被限制为64
      
      expect(tooShort.length).toBe(32)
      expect(tooLong.length).toBe(64)
    })

    it('should be cryptographically secure', () => {
      // 生成大量密钥测试随机性
      const keys = []
      for (let i = 0; i < 100; i++) {
        keys.push(generateSecureBearerKey())
      }
      
      // 检查字符分布 - 不应该有明显的模式
      const charCounts: Record<string, number> = {}
      keys.join('').split('').forEach(char => {
        charCounts[char] = (charCounts[char] || 0) + 1
      })
      
      // 应该使用了多种不同的字符
      const uniqueChars = Object.keys(charCounts).length
      expect(uniqueChars).toBeGreaterThan(20) // 至少使用20种不同字符
      
      // 所有密钥都应该是唯一的
      const uniqueKeys = new Set(keys)
      expect(uniqueKeys.size).toBe(100)
    })
  })

  describe('Form Integration Scenarios', () => {
    it('should work with AddGroupForm workflow', () => {
      // 模拟添加组表单的工作流程
      const formData = {
        name: 'test-group',
        description: 'Test group with generated key',
        servers: [],
        bearerAuthKey: ''
      }
      
      // 生成密钥
      const generatedKey = generateSecureBearerKey()
      formData.bearerAuthKey = generatedKey
      
      // 验证表单数据
      expect(formData.bearerAuthKey).toBe(generatedKey)
      expect(formData.bearerAuthKey.length).toBe(48)
      
      // 验证符合表单验证规则
      const bearerKeyRegex = /^[a-zA-Z0-9._-]+$/
      expect(formData.bearerAuthKey).toMatch(bearerKeyRegex)
    })

    it('should work with EditGroupForm workflow', () => {
      // 模拟编辑组表单的工作流程
      const existingGroup = {
        id: 'group-1',
        name: 'existing-group',
        description: 'Existing group',
        servers: ['server1'],
        bearerAuthKey: 'old-key-123'
      }
      
      // 生成新密钥
      const newKey = generateSecureBearerKey()
      const updatedGroup = {
        ...existingGroup,
        bearerAuthKey: newKey
      }
      
      // 验证更新后的组数据
      expect(updatedGroup.bearerAuthKey).toBe(newKey)
      expect(updatedGroup.bearerAuthKey).not.toBe(existingGroup.bearerAuthKey)
      expect(updatedGroup.bearerAuthKey.length).toBe(48)
    })
  })
})
