import { generateRandomKey, generateSecureBearer<PERSON>ey } from '../frontend/src/utils/key'

describe('Frontend Key Generation Utils', () => {
  describe('generateRandomKey', () => {
    it('should generate a key with default length of 32', () => {
      const key = generateRandomKey()
      expect(key).toHaveLength(32)
      expect(typeof key).toBe('string')
    })

    it('should generate a key with specified length', () => {
      const key = generateRandomKey(16)
      expect(key).toHaveLength(16)
    })

    it('should generate different keys on multiple calls', () => {
      const key1 = generateRandomKey()
      const key2 = generateRandomKey()
      expect(key1).not.toBe(key2)
    })

    it('should only contain valid characters', () => {
      const key = generateRandomKey()
      const validChars = /^[ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789]+$/
      expect(key).toMatch(validChars)
    })
  })

  describe('generateSecureBearerKey', () => {
    it('should generate a key with default length of 48', () => {
      const key = generateSecureBearerKey()
      expect(key).toHaveLength(48)
      expect(typeof key).toBe('string')
    })

    it('should generate a key with specified length within valid range', () => {
      const key = generateSecureBearerKey(40)
      expect(key).toHaveLength(40)
    })

    it('should enforce minimum length of 32', () => {
      const key = generateSecureBearerKey(20)
      expect(key).toHaveLength(32)
    })

    it('should enforce maximum length of 64', () => {
      const key = generateSecureBearerKey(100)
      expect(key).toHaveLength(64)
    })

    it('should generate different keys on multiple calls', () => {
      const key1 = generateSecureBearerKey()
      const key2 = generateSecureBearerKey()
      expect(key1).not.toBe(key2)
    })

    it('should only contain safe characters (no confusing characters)', () => {
      const key = generateSecureBearerKey()
      // Should not contain confusing characters like 0, O, l, I
      expect(key).not.toMatch(/[0OlI]/)
      // Should only contain safe characters
      const safeChars = /^[ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789\-._]+$/
      expect(key).toMatch(safeChars)
    })

    it('should be suitable for Bearer authentication format validation', () => {
      const key = generateSecureBearerKey()
      // Should match the Bearer key validation regex from the forms
      const bearerKeyRegex = /^[a-zA-Z0-9._-]+$/
      expect(key).toMatch(bearerKeyRegex)
      expect(key.length).toBeGreaterThanOrEqual(8)
      expect(key.length).toBeLessThanOrEqual(256)
    })
  })
})
