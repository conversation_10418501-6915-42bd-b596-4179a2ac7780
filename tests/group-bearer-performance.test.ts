/**
 * 组级别Bearer认证功能性能测试
 * 确保认证逻辑不会显著影响系统性能
 */

import { Request } from 'express';

// Mock group service
const mockGroupService = {
  getGroupByIdOrName: jest.fn(),
};

// Mock request helper
const mockRequest = (authHeader?: string): Partial<Request> => ({
  headers: authHeader ? { authorization: authHeader } : {},
});

// 复制auth middleware中的validateBearerAuth逻辑用于测试
const validateBearerAuth = (req: Request, routingConfig: any, group?: string): boolean => {
  if (!routingConfig.enableBearerAuth) {
    return false;
  }

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }

  const token = authHeader.substring(7); // Remove "Bearer " prefix

  // If group is provided, check for group-level Bearer key first
  if (group) {
    const groupInfo = mockGroupService.getGroupByIdOrName(group);
    
    if (groupInfo && groupInfo.bearerAuthKey) {
      // Group has its own Bearer key, use it for authentication
      return token === groupInfo.bearerAuthKey;
    }
    // If group doesn't have its own Bearer key, fall back to global key
  }

  // Use global Bearer key for authentication
  return token === routingConfig.bearerAuthKey;
};

describe('Group-Level Bearer Authentication Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const routingConfig = {
    enableBearerAuth: true,
    bearerAuthKey: 'global-performance-key',
  };

  describe('Authentication Performance Benchmarks', () => {
    it('should authenticate global requests within acceptable time limits', () => {
      const req = mockRequest('Bearer global-performance-key') as Request;
      
      const startTime = process.hrtime.bigint();
      
      // 执行1000次全局认证
      for (let i = 0; i < 1000; i++) {
        validateBearerAuth(req, routingConfig);
      }
      
      const endTime = process.hrtime.bigint();
      const executionTime = Number(endTime - startTime) / 1000000; // 转换为毫秒
      
      // 1000次认证应该在100毫秒内完成
      expect(executionTime).toBeLessThan(100);
      console.log(`Global authentication 1000 times took: ${executionTime.toFixed(2)}ms`);
    });

    it('should authenticate group requests within acceptable time limits', () => {
      const groupInfo = {
        id: 'perf-test-group',
        name: 'Performance Test Group',
        bearerAuthKey: 'group-performance-key',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);
      
      const req = mockRequest('Bearer group-performance-key') as Request;
      
      const startTime = process.hrtime.bigint();
      
      // 执行1000次组级别认证
      for (let i = 0; i < 1000; i++) {
        validateBearerAuth(req, routingConfig, 'perf-test-group');
      }
      
      const endTime = process.hrtime.bigint();
      const executionTime = Number(endTime - startTime) / 1000000; // 转换为毫秒
      
      // 1000次组认证应该在200毫秒内完成（允许额外的组查询开销）
      expect(executionTime).toBeLessThan(200);
      console.log(`Group authentication 1000 times took: ${executionTime.toFixed(2)}ms`);
    });

    it('should handle mixed authentication scenarios efficiently', () => {
      const groupInfo = {
        id: 'mixed-perf-group',
        name: 'Mixed Performance Group',
        bearerAuthKey: 'mixed-group-key',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);
      
      const globalReq = mockRequest('Bearer global-performance-key') as Request;
      const groupReq = mockRequest('Bearer mixed-group-key') as Request;
      
      const startTime = process.hrtime.bigint();
      
      // 执行500次全局认证和500次组认证的混合场景
      for (let i = 0; i < 500; i++) {
        validateBearerAuth(globalReq, routingConfig);
        validateBearerAuth(groupReq, routingConfig, 'mixed-perf-group');
      }
      
      const endTime = process.hrtime.bigint();
      const executionTime = Number(endTime - startTime) / 1000000; // 转换为毫秒
      
      // 1000次混合认证应该在250毫秒内完成
      expect(executionTime).toBeLessThan(250);
      console.log(`Mixed authentication 1000 times took: ${executionTime.toFixed(2)}ms`);
    });
  });

  describe('Memory Usage and Resource Management', () => {
    it('should not cause memory leaks during repeated authentication', () => {
      const groupInfo = {
        id: 'memory-test-group',
        name: 'Memory Test Group',
        bearerAuthKey: 'memory-test-key',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);
      
      const req = mockRequest('Bearer memory-test-key') as Request;
      
      // 记录初始内存使用
      const initialMemory = process.memoryUsage();
      
      // 执行大量认证操作
      for (let i = 0; i < 10000; i++) {
        validateBearerAuth(req, routingConfig, 'memory-test-group');
      }
      
      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage();
      
      // 内存增长应该在合理范围内（小于10MB）
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // 10MB
      
      console.log(`Memory increase after 10000 authentications: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    });

    it('should handle high-frequency authentication requests', () => {
      const groupInfo = {
        id: 'high-freq-group',
        name: 'High Frequency Group',
        bearerAuthKey: 'high-freq-key',
        servers: [],
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(groupInfo);
      
      const req = mockRequest('Bearer high-freq-key') as Request;
      
      const startTime = process.hrtime.bigint();
      
      // 模拟高频请求场景
      const promises = [];
      for (let i = 0; i < 100; i++) {
        promises.push(
          Promise.resolve().then(() => {
            // 每个Promise执行10次认证
            for (let j = 0; j < 10; j++) {
              validateBearerAuth(req, routingConfig, 'high-freq-group');
            }
          })
        );
      }
      
      return Promise.all(promises).then(() => {
        const endTime = process.hrtime.bigint();
        const executionTime = Number(endTime - startTime) / 1000000; // 转换为毫秒
        
        // 1000次高频认证应该在300毫秒内完成
        expect(executionTime).toBeLessThan(300);
        console.log(`High-frequency authentication 1000 times took: ${executionTime.toFixed(2)}ms`);
      });
    });
  });

  describe('Scalability Tests', () => {
    it('should scale well with increasing number of groups', () => {
      // 创建多个组的测试数据
      const groups = Array.from({ length: 100 }, (_, i) => ({
        id: `scale-group-${i}`,
        name: `Scale Group ${i}`,
        bearerAuthKey: `scale-key-${i}`,
        servers: [],
      }));

      // Mock返回不同的组信息
      mockGroupService.getGroupByIdOrName.mockImplementation((groupId: string) => {
        const index = parseInt(groupId.split('-')[2]);
        return groups[index];
      });

      const startTime = process.hrtime.bigint();

      // 对每个组执行10次认证
      for (let i = 0; i < 100; i++) {
        const req = mockRequest(`Bearer scale-key-${i}`) as Request;
        for (let j = 0; j < 10; j++) {
          validateBearerAuth(req, routingConfig, `scale-group-${i}`);
        }
      }

      const endTime = process.hrtime.bigint();
      const executionTime = Number(endTime - startTime) / 1000000; // 转换为毫秒

      // 100个组，每个组10次认证，总共1000次认证应该在500毫秒内完成
      expect(executionTime).toBeLessThan(500);
      console.log(`Scalability test with 100 groups (1000 authentications) took: ${executionTime.toFixed(2)}ms`);
    });

    it('should maintain performance with complex group configurations', () => {
      // 创建复杂的组配置
      const complexGroup = {
        id: 'complex-group',
        name: 'Complex Group with Long Name and Many Properties',
        description: 'This is a complex group with many properties for testing performance',
        bearerAuthKey: 'complex-bearer-key-with-long-name-for-performance-testing',
        servers: Array.from({ length: 50 }, (_, i) => ({
          id: `server-${i}`,
          name: `Server ${i}`,
          url: `http://server-${i}.example.com`,
        })),
        metadata: {
          created: new Date().toISOString(),
          updated: new Date().toISOString(),
          tags: ['performance', 'testing', 'complex'],
          settings: {
            timeout: 30000,
            retries: 3,
            concurrent: true,
          },
        },
      };

      mockGroupService.getGroupByIdOrName.mockReturnValue(complexGroup);

      const req = mockRequest('Bearer complex-bearer-key-with-long-name-for-performance-testing') as Request;

      const startTime = process.hrtime.bigint();

      // 执行1000次复杂组认证
      for (let i = 0; i < 1000; i++) {
        validateBearerAuth(req, routingConfig, 'complex-group');
      }

      const endTime = process.hrtime.bigint();
      const executionTime = Number(endTime - startTime) / 1000000; // 转换为毫秒

      // 复杂组配置不应该显著影响认证性能
      expect(executionTime).toBeLessThan(300);
      console.log(`Complex group authentication 1000 times took: ${executionTime.toFixed(2)}ms`);
    });
  });
});
